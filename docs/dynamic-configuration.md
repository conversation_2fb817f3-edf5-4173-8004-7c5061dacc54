# API网关动态规则配置系统

## 概述

API网关的动态规则配置系统允许在不重启网关的情况下，实时更新路由规则、安全策略、插件配置等。该系统提供了完整的配置管理、版本控制、变更通知和回滚机制。

## 系统架构

### 核心组件

1. **DynamicConfigManager**: 动态配置管理器，负责配置的生命周期管理
2. **ConfigStorage**: 配置存储接口，支持内存、Redis、文件等多种存储后端
3. **ConfigValidator**: 配置验证器，确保配置的正确性和一致性
4. **ConfigChangeListener**: 配置变更监听器，处理配置变更事件
5. **Admin API**: 管理API接口，提供RESTful接口进行配置管理

### 配置存储

支持多种配置存储后端：

- **内存存储**: 适用于单实例部署，配置存储在内存中
- **Redis存储**: 适用于分布式部署，支持配置共享和持久化
- **文件存储**: 适用于传统部署，配置存储在文件系统中

### 配置验证

提供多层次的配置验证：

- **语法验证**: 检查配置格式和数据类型
- **语义验证**: 检查配置的逻辑正确性
- **依赖验证**: 检查配置间的依赖关系
- **安全验证**: 检查配置的安全性

## 功能特性

### 1. 动态路由管理

- **添加路由**: 动态添加新的路由规则
- **更新路由**: 修改现有路由的配置
- **删除路由**: 移除不需要的路由
- **路由优先级**: 支持路由优先级和匹配顺序

### 2. 安全规则动态配置

- **速率限制**: 动态调整API调用频率限制
- **CORS配置**: 实时更新跨域资源共享设置
- **WAF规则**: 动态添加/修改Web应用防火墙规则
- **IP过滤**: 实时更新IP白名单和黑名单

### 3. 插件动态管理

- **插件启用/禁用**: 动态控制插件的启用状态
- **插件配置更新**: 实时修改插件参数
- **插件优先级**: 调整插件执行顺序
- **插件热加载**: 支持插件的热加载和卸载

### 4. 认证配置动态更新

- **JWT配置**: 动态更新JWT密钥和算法
- **API Key配置**: 修改API Key验证规则
- **OIDC配置**: 更新OpenID Connect设置
- **认证策略**: 动态调整认证策略

### 5. 配置版本管理

- **版本历史**: 保存配置变更历史
- **配置回滚**: 支持回滚到任意历史版本
- **变更追踪**: 记录配置变更的详细信息
- **差异对比**: 比较不同版本间的配置差异

## API接口

### 配置管理接口

```http
GET    /admin/api/v1/config              # 获取当前配置
PUT    /admin/api/v1/config              # 更新完整配置
POST   /admin/api/v1/config/reload       # 重载配置
```

### 路由管理接口

```http
GET    /admin/api/v1/config/routes       # 获取所有路由
POST   /admin/api/v1/config/routes       # 添加新路由
PUT    /admin/api/v1/config/routes/:name # 更新指定路由
DELETE /admin/api/v1/config/routes/:name # 删除指定路由
```

### 安全配置接口

```http
GET    /admin/api/v1/config/security           # 获取安全配置
PUT    /admin/api/v1/config/security/rate-limit # 更新速率限制
PUT    /admin/api/v1/config/security/cors       # 更新CORS配置
PUT    /admin/api/v1/config/security/waf        # 更新WAF规则
PUT    /admin/api/v1/config/security/ip-filter  # 更新IP过滤
```

### 插件管理接口

```http
GET    /admin/api/v1/config/plugins       # 获取插件配置
PUT    /admin/api/v1/config/plugins/:name # 更新插件配置
DELETE /admin/api/v1/config/plugins/:name # 删除插件配置
```

### 认证配置接口

```http
GET    /admin/api/v1/config/auth         # 获取认证配置
PUT    /admin/api/v1/config/auth/jwt     # 更新JWT配置
PUT    /admin/api/v1/config/auth/api-key # 更新API Key配置
PUT    /admin/api/v1/config/auth/oidc    # 更新OIDC配置
```

### 版本管理接口

```http
GET    /admin/api/v1/config/versions              # 获取版本历史
POST   /admin/api/v1/config/versions/rollback/:version # 回滚到指定版本
```

## 使用示例

### 添加新路由

```bash
curl -X POST http://localhost:8080/admin/api/v1/config/routes \
  -H "Content-Type: application/json" \
  -d '{
    "name": "new-service",
    "path": "/api/v1/new-service/*",
    "method": "*",
    "upstream": {
      "type": "static",
      "load_balancer": "round_robin",
      "servers": [
        {
          "host": "localhost",
          "port": 8082,
          "weight": 1
        }
      ]
    },
    "timeout": "30s",
    "retries": 3,
    "plugins": ["auth", "rate_limit"]
  }'
```

### 更新速率限制

```bash
curl -X PUT http://localhost:8080/admin/api/v1/config/security/rate-limit \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "algorithm": "token_bucket",
    "rules": [
      {
        "path": "/api/*",
        "method": "*",
        "rate": 200,
        "burst": 400,
        "window": "1m",
        "key_by": "ip"
      }
    ]
  }'
```

### 配置回滚

```bash
curl -X POST http://localhost:8080/admin/api/v1/config/versions/rollback/v1640995200
```

## 配置变更流程

1. **接收变更请求**: 通过Admin API接收配置变更请求
2. **配置验证**: 验证新配置的正确性和完整性
3. **版本保存**: 保存当前配置版本到历史记录
4. **配置更新**: 更新内存中的配置
5. **持久化存储**: 将配置保存到存储后端
6. **变更通知**: 通知相关组件配置已变更
7. **组件更新**: 各组件根据新配置更新自身状态

## 监控和告警

### 配置变更监控

- 配置变更次数统计
- 配置变更成功/失败率
- 配置验证错误统计
- 配置回滚次数统计

### 性能监控

- 配置更新延迟
- 配置存储性能
- 配置验证耗时
- 组件更新耗时

### 告警规则

- 配置变更失败告警
- 配置验证错误告警
- 配置存储异常告警
- 频繁配置变更告警

## 最佳实践

### 1. 配置管理

- 使用版本控制管理配置文件
- 建立配置变更审批流程
- 定期备份配置数据
- 监控配置变更频率

### 2. 安全考虑

- 限制配置管理API的访问权限
- 使用HTTPS保护配置传输
- 记录所有配置变更操作
- 定期审计配置变更日志

### 3. 性能优化

- 避免频繁的配置变更
- 使用批量更新减少开销
- 合理设置配置缓存策略
- 监控配置变更的性能影响

### 4. 故障处理

- 建立配置回滚机制
- 设置配置变更告警
- 准备配置恢复预案
- 定期测试配置恢复流程

## 扩展性

### 自定义存储后端

可以通过实现`ConfigStorage`接口来支持新的存储后端：

```go
type ConfigStorage interface {
    Get(key string) ([]byte, error)
    Set(key string, value []byte) error
    Delete(key string) error
    List(prefix string) (map[string][]byte, error)
    Watch(key string) (<-chan ConfigEvent, error)
    Close() error
}
```

### 自定义配置监听器

可以通过实现`ConfigChangeListener`接口来处理特定的配置变更：

```go
type ConfigChangeListener interface {
    OnConfigChange(ctx context.Context, change ConfigChange) error
    GetName() string
}
```

### 自定义配置验证器

可以通过实现`ConfigValidator`接口来添加自定义验证逻辑：

```go
type ConfigValidator interface {
    ValidateConfig(config *Config) error
    ValidateRouteConfig(route RouteConfig) error
    ValidateSecurityConfig(security SecurityConfig) error
    ValidatePluginConfig(plugin PluginConfig) error
}
```

## 总结

动态规则配置系统为API网关提供了强大的运行时配置管理能力，支持无停机的配置更新、完整的版本管理和灵活的扩展机制。通过合理使用这些功能，可以大大提高系统的可维护性和运营效率。
