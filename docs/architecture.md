# API Gateway 架构设计文档

## 概述

本API网关是一个高性能、企业级的微服务网关，采用Go语言开发，基于零信任安全架构设计。网关提供了完整的流量管理、安全防护、服务发现、可观测性等功能。

## 核心架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    API Gateway                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Client    │  │   Admin     │  │  Metrics    │         │
│  │   Request   │  │     API     │  │   Server    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                 Middleware Chain                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  Security   │  │    Auth     │  │   Plugin    │         │
│  │ Middleware  │  │ Middleware  │  │ Middleware  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                  Core Components                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │    Auth     │  │  Security   │  │  Discovery  │         │
│  │   Manager   │  │   Manager   │  │   Manager   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │    Proxy    │  │   Plugin    │  │ Telemetry   │         │
│  │   Manager   │  │   Manager   │  │   System    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                 External Services                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Consul    │  │    Redis    │  │   Jaeger    │         │
│  │   Nacos     │  │  Database   │  │ Prometheus  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. Gateway Core (`internal/core/gateway.go`)

网关的核心控制器，负责：
- 组件初始化和生命周期管理
- 中间件链配置
- 路由设置
- 优雅关闭

```go
type Gateway struct {
    config    *config.Config
    logger    *telemetry.Logger
    metrics   *telemetry.Metrics
    tracer    opentracing.Tracer
    
    // Core components
    authManager      *auth.Manager
    securityManager  *security.Manager
    discoveryManager *discovery.Manager
    proxyManager     *proxy.Manager
    pluginManager    *plugin.Manager
    
    // HTTP router
    router *gin.Engine
}
```

#### 2. 认证管理器 (`pkg/auth/manager.go`)

提供多种认证方式的统一管理：

**支持的认证方式：**
- JWT Token认证
- API Key认证
- OAuth/OIDC认证
- mTLS证书认证

**核心接口：**
```go
type Authenticator interface {
    Authenticate(ctx context.Context, authCtx *AuthContext) (*AuthResult, error)
    Name() string
    Priority() int
    Enabled() bool
}
```

#### 3. 安全管理器 (`pkg/security/manager.go`)

集成多种安全防护功能：

**安全组件：**
- 限流器（Rate Limiter）
- WAF引擎（Web Application Firewall）
- IP过滤器（IP Filter）
- CORS处理器（CORS Handler）

#### 4. 服务发现管理器 (`pkg/discovery/manager.go`)

支持多种服务发现机制：

**支持的服务发现：**
- Consul
- Nacos
- Eureka（预留接口）

#### 5. 代理管理器 (`pkg/proxy/manager.go`)

处理请求转发和负载均衡：

**负载均衡算法：**
- 轮询（Round Robin）
- 加权轮询（Weighted Round Robin）
- 随机（Random）
- IP哈希（IP Hash）
- 最少连接（Least Connections）

#### 6. 插件管理器 (`pkg/plugin/manager.go`)

提供可扩展的插件系统：

**内置插件：**
- 日志插件
- 指标插件
- 转换插件
- 验证插件
- 缓存插件

## 中间件架构

### 中间件执行顺序

```
Request → RequestID → Logger → Recovery → Metrics → Tracing → 
         CORS → IPFilter → WAF → RateLimit → Authentication → 
         Plugin → Proxy → Response
```

### 中间件分类

#### 1. 核心中间件
- **RequestID**: 为每个请求生成唯一ID
- **Logger**: 记录请求日志
- **Recovery**: 异常恢复
- **Metrics**: 指标收集
- **Tracing**: 分布式追踪

#### 2. 安全中间件
- **CORS**: 跨域资源共享
- **IPFilter**: IP地址过滤
- **WAF**: Web应用防火墙
- **RateLimit**: 请求限流
- **Authentication**: 身份认证

#### 3. 功能中间件
- **Plugin**: 插件执行
- **Proxy**: 请求代理
- **Transform**: 请求/响应转换

## 配置系统

### 配置文件结构

```yaml
# 服务器配置
server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30
  tls:
    enabled: false
    cert_file: "certs/server.crt"
    key_file: "certs/server.key"

# 认证配置
auth:
  jwt:
    enabled: true
    secret: "your-secret-key"
    algorithm: "HS256"
  api_key:
    enabled: true
    header_name: "X-API-Key"

# 安全配置
security:
  rate_limit:
    enabled: true
    algorithm: "token_bucket"
    rules:
      - path: "/api/*"
        rate: 100
        burst: 200
  waf:
    enabled: true
    rules:
      - name: "sql_injection"
        pattern: "(?i)(union|select|insert)"
        action: "block"

# 路由配置
routes:
  - name: "user_service"
    path: "/api/v1/users/*"
    method: "*"
    upstream:
      type: "static"
      servers:
        - host: "localhost"
          port: 8081
```

### 环境变量支持

```bash
# 服务器配置
GATEWAY_SERVER_PORT=8080
GATEWAY_SERVER_ADDRESS=:8080

# 认证配置
JWT_SECRET=your-secret-key

# 服务发现配置
CONSUL_ADDRESS=localhost:8500
CONSUL_TOKEN=your-consul-token

# 日志配置
GATEWAY_LOG_LEVEL=info
GATEWAY_LOG_FORMAT=json
```

## 可观测性

### 指标系统

**Prometheus指标类型：**
- Counter: 请求计数、错误计数
- Histogram: 请求延迟、响应大小
- Gauge: 活跃连接数、内存使用

**核心指标：**
```go
// 请求指标
gateway_requests_total{method, path, status, upstream}
gateway_request_duration_seconds{method, path, upstream}
gateway_request_size_bytes{method, path}
gateway_response_size_bytes{method, path}

// 安全指标
gateway_auth_failures_total{type, reason}
gateway_rate_limit_hits_total{path, key_type}
gateway_waf_blocks_total{rule, action}

// 系统指标
gateway_active_connections
gateway_circuit_breaker_state{name}
```

### 日志系统

**日志级别：**
- DEBUG: 调试信息
- INFO: 一般信息
- WARN: 警告信息
- ERROR: 错误信息

**日志格式：**
```json
{
  "timestamp": "2023-01-01T00:00:00Z",
  "level": "info",
  "message": "Request processed",
  "request_id": "req-123",
  "method": "GET",
  "path": "/api/v1/users",
  "status": 200,
  "duration": "15ms",
  "client_ip": "*************",
  "user_agent": "curl/7.68.0"
}
```

### 分布式追踪

**追踪信息：**
- Span: 操作单元
- Trace: 完整请求链路
- Tags: 标签信息
- Logs: 事件日志

**集成组件：**
- Jaeger: 追踪收集和展示
- OpenTracing: 标准追踪接口

### 审计日志

**审计事件类型：**
- 认证成功/失败
- 授权拒绝
- 安全违规
- 配置变更
- 数据访问

**审计日志格式：**
```json
{
  "timestamp": "2023-01-01T00:00:00Z",
  "event_type": "authentication_failure",
  "event_id": "audit_123",
  "user_id": "user123",
  "client_ip": "*************",
  "resource": "/api/v1/users",
  "action": "GET",
  "result": "failure",
  "reason": "invalid token",
  "severity": "medium",
  "risk_score": 5
}
```

## 部署架构

### 单机部署

```yaml
version: '3.8'
services:
  gateway:
    image: api-gateway:latest
    ports:
      - "8080:8080"
      - "9090:9090"
    environment:
      - GATEWAY_LOG_LEVEL=info
    volumes:
      - ./configs:/app/configs
```

### 集群部署

```yaml
# Kubernetes部署示例
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: gateway
        image: api-gateway:latest
        ports:
        - containerPort: 8080
        - containerPort: 9090
        env:
        - name: GATEWAY_LOG_LEVEL
          value: "info"
        - name: CONSUL_ADDRESS
          value: "consul:8500"
```

### 高可用配置

**负载均衡器配置：**
```nginx
upstream api_gateway {
    server gateway1:8080 weight=1;
    server gateway2:8080 weight=1;
    server gateway3:8080 weight=1;
    
    # 健康检查
    check interval=3000 rise=2 fall=3 timeout=1000;
}

server {
    listen 80;
    location / {
        proxy_pass http://api_gateway;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 性能优化

### 连接池配置

```yaml
proxy:
  connection_pool:
    max_idle_conns: 100
    max_idle_conns_per_host: 10
    idle_conn_timeout: "90s"
    dial_timeout: "30s"
    keep_alive: "30s"
```

### 缓存配置

```yaml
cache:
  enabled: true
  type: "redis"
  redis:
    address: "localhost:6379"
    password: ""
    db: 0
  ttl: "5m"
  max_size: "100MB"
```

### 性能调优参数

```yaml
server:
  max_header_bytes: 1048576  # 1MB
  read_buffer_size: 4096
  write_buffer_size: 4096
  
performance:
  worker_processes: 4
  max_connections: 10000
  keepalive_timeout: 65
  client_body_timeout: 60
  client_header_timeout: 60
```

这个架构设计确保了网关的高性能、高可用性和可扩展性，同时提供了完整的安全防护和可观测性功能。
