# API Gateway API 文档

## 1. 概述

API网关提供了管理接口用于配置管理、监控和运维操作。所有管理API都需要适当的认证和授权。

### 1.1 基础信息

- **Base URL**: `http://localhost:8080/admin`
- **认证方式**: JWT Token 或 API Key
- **内容类型**: `application/json`
- **API版本**: v1

### 1.2 通用响应格式

**成功响应：**
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2023-01-01T00:00:00Z",
  "request_id": "req-123456"
}
```

**错误响应：**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "请求参数无效",
    "details": "字段 'name' 不能为空"
  },
  "timestamp": "2023-01-01T00:00:00Z",
  "request_id": "req-123456"
}
```

### 1.3 认证

**JWT Token认证：**
```bash
curl -H "Authorization: Bearer <jwt_token>" \
     http://localhost:8080/admin/api/v1/health
```

**API Key认证：**
```bash
curl -H "X-API-Key: <api_key>" \
     http://localhost:8080/admin/api/v1/health
```

## 2. 健康检查 API

### 2.1 系统健康检查

**端点**: `GET /health`

**描述**: 检查系统整体健康状态

**请求示例**:
```bash
curl http://localhost:8080/health
```

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2023-01-01T00:00:00Z",
  "version": "1.0.0",
  "uptime": "2h30m15s",
  "components": {
    "database": {
      "status": "healthy",
      "response_time": "2ms"
    },
    "redis": {
      "status": "healthy",
      "response_time": "1ms"
    },
    "consul": {
      "status": "healthy",
      "response_time": "5ms"
    }
  }
}
```

### 2.2 详细健康检查

**端点**: `GET /admin/api/v1/health/detailed`

**描述**: 获取详细的组件健康状态

**认证**: 需要

**响应示例**:
```json
{
  "success": true,
  "data": {
    "overall_status": "healthy",
    "components": {
      "auth_manager": {
        "status": "healthy",
        "last_check": "2023-01-01T00:00:00Z",
        "details": {
          "active_authenticators": 3,
          "jwt_keys_loaded": true
        }
      },
      "security_manager": {
        "status": "healthy",
        "details": {
          "rate_limiter": "active",
          "waf_rules": 15,
          "ip_filter": "enabled"
        }
      },
      "discovery_manager": {
        "status": "healthy",
        "details": {
          "consul_connected": true,
          "registered_services": 5
        }
      }
    }
  }
}
```

## 3. 指标 API

### 3.1 Prometheus指标

**端点**: `GET /metrics`

**描述**: 获取Prometheus格式的指标数据

**请求示例**:
```bash
curl http://localhost:9090/metrics
```

**响应示例**:
```
# HELP gateway_requests_total Total number of requests
# TYPE gateway_requests_total counter
gateway_requests_total{method="GET",path="/api/v1/users",status="200"} 1234

# HELP gateway_request_duration_seconds Request duration in seconds
# TYPE gateway_request_duration_seconds histogram
gateway_request_duration_seconds_bucket{method="GET",path="/api/v1/users",le="0.1"} 100
gateway_request_duration_seconds_bucket{method="GET",path="/api/v1/users",le="0.3"} 200
gateway_request_duration_seconds_sum{method="GET",path="/api/v1/users"} 45.6
gateway_request_duration_seconds_count{method="GET",path="/api/v1/users"} 300
```

### 3.2 系统指标

**端点**: `GET /admin/api/v1/metrics/system`

**描述**: 获取系统运行指标

**认证**: 需要

**响应示例**:
```json
{
  "success": true,
  "data": {
    "uptime": "2h30m15s",
    "memory": {
      "allocated": "45.2MB",
      "total_allocated": "128.5MB",
      "system": "67.8MB",
      "gc_cycles": 15
    },
    "goroutines": 45,
    "connections": {
      "active": 123,
      "total": 5678
    },
    "requests": {
      "total": 10000,
      "success": 9850,
      "errors": 150,
      "rate": "125.5/sec"
    }
  }
}
```

## 4. 配置管理 API

### 4.1 获取配置

**端点**: `GET /admin/api/v1/config`

**描述**: 获取当前配置信息

**认证**: 需要 `admin` 角色

**响应示例**:
```json
{
  "success": true,
  "data": {
    "server": {
      "address": ":8080",
      "read_timeout": 30,
      "write_timeout": 30
    },
    "auth": {
      "jwt": {
        "enabled": true,
        "algorithm": "HS256"
      }
    },
    "security": {
      "rate_limit": {
        "enabled": true,
        "rules_count": 5
      }
    }
  }
}
```

### 4.2 更新配置

**端点**: `PUT /admin/api/v1/config`

**描述**: 更新配置（热更新）

**认证**: 需要 `admin` 角色

**请求体**:
```json
{
  "logging": {
    "level": "debug"
  },
  "security": {
    "rate_limit": {
      "enabled": true,
      "rules": [
        {
          "name": "api_limit",
          "path": "/api/*",
          "rate": 200,
          "burst": 400
        }
      ]
    }
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "配置更新成功",
  "data": {
    "updated_sections": ["logging", "security.rate_limit"],
    "restart_required": false
  }
}
```

### 4.3 重载配置

**端点**: `POST /admin/api/v1/config/reload`

**描述**: 从文件重新加载配置

**认证**: 需要 `admin` 角色

**响应示例**:
```json
{
  "success": true,
  "message": "配置重载成功",
  "data": {
    "config_file": "/etc/api-gateway/gateway.yaml",
    "last_modified": "2023-01-01T00:00:00Z"
  }
}
```

## 5. 路由管理 API

### 5.1 获取路由列表

**端点**: `GET /admin/api/v1/routes`

**描述**: 获取所有路由配置

**认证**: 需要

**查询参数**:
- `page`: 页码（默认: 1）
- `size`: 每页大小（默认: 20）
- `search`: 搜索关键词

**响应示例**:
```json
{
  "success": true,
  "data": {
    "routes": [
      {
        "id": "route-001",
        "name": "user_service",
        "path": "/api/v1/users/*",
        "method": "*",
        "upstream": {
          "type": "static",
          "servers": [
            {
              "host": "localhost",
              "port": 8081,
              "weight": 1,
              "healthy": true
            }
          ]
        },
        "enabled": true,
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 5,
      "pages": 1
    }
  }
}
```

### 5.2 创建路由

**端点**: `POST /admin/api/v1/routes`

**描述**: 创建新路由

**认证**: 需要 `admin` 角色

**请求体**:
```json
{
  "name": "order_service",
  "path": "/api/v1/orders/*",
  "method": "*",
  "upstream": {
    "type": "discovery",
    "service_name": "order-service",
    "load_balancer": "round_robin"
  },
  "timeout": "30s",
  "retries": 3,
  "headers": {
    "X-Service": "order-service"
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "路由创建成功",
  "data": {
    "id": "route-002",
    "name": "order_service",
    "created_at": "2023-01-01T00:00:00Z"
  }
}
```

### 5.3 更新路由

**端点**: `PUT /admin/api/v1/routes/{id}`

**描述**: 更新指定路由

**认证**: 需要 `admin` 角色

**路径参数**:
- `id`: 路由ID

**请求体**:
```json
{
  "timeout": "60s",
  "retries": 5,
  "upstream": {
    "servers": [
      {
        "host": "localhost",
        "port": 8082,
        "weight": 2
      }
    ]
  }
}
```

### 5.4 删除路由

**端点**: `DELETE /admin/api/v1/routes/{id}`

**描述**: 删除指定路由

**认证**: 需要 `admin` 角色

**响应示例**:
```json
{
  "success": true,
  "message": "路由删除成功"
}
```

## 6. 认证管理 API

### 6.1 API Key管理

#### 创建API Key

**端点**: `POST /admin/api/v1/auth/api-keys`

**描述**: 创建新的API Key

**认证**: 需要 `admin` 角色

**请求体**:
```json
{
  "name": "mobile-app-key",
  "user_id": "user123",
  "roles": ["user"],
  "permissions": ["user:read", "user:write"],
  "expires_at": "2024-12-31T23:59:59Z"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "API Key创建成功",
  "data": {
    "id": "ak_1234567890abcdef",
    "key": "ak_1234567890abcdef1234567890abcdef",
    "name": "mobile-app-key",
    "created_at": "2023-01-01T00:00:00Z",
    "expires_at": "2024-12-31T23:59:59Z"
  }
}
```

#### 获取API Key列表

**端点**: `GET /admin/api/v1/auth/api-keys`

**描述**: 获取API Key列表

**认证**: 需要 `admin` 角色

**查询参数**:
- `user_id`: 按用户ID过滤
- `active`: 是否只显示活跃的Key

**响应示例**:
```json
{
  "success": true,
  "data": {
    "api_keys": [
      {
        "id": "ak_1234567890abcdef",
        "name": "mobile-app-key",
        "user_id": "user123",
        "roles": ["user"],
        "active": true,
        "created_at": "2023-01-01T00:00:00Z",
        "last_used_at": "2023-01-01T12:00:00Z"
      }
    ]
  }
}
```

#### 撤销API Key

**端点**: `DELETE /admin/api/v1/auth/api-keys/{id}`

**描述**: 撤销指定的API Key

**认证**: 需要 `admin` 角色

### 6.2 JWT Token管理

#### 签发Token

**端点**: `POST /admin/api/v1/auth/tokens`

**描述**: 为用户签发JWT Token

**认证**: 需要 `admin` 角色

**请求体**:
```json
{
  "user_id": "user123",
  "username": "john_doe",
  "roles": ["user"],
  "permissions": ["user:read"],
  "expires_in": 3600
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "expires_at": "2023-01-01T01:00:00Z"
  }
}
```

#### 验证Token

**端点**: `POST /admin/api/v1/auth/tokens/verify`

**描述**: 验证JWT Token

**请求体**:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "valid": true,
    "user_id": "user123",
    "username": "john_doe",
    "roles": ["user"],
    "expires_at": "2023-01-01T01:00:00Z"
  }
}
```

## 7. 安全管理 API

### 7.1 限流管理

#### 获取限流统计

**端点**: `GET /admin/api/v1/security/rate-limit/stats`

**描述**: 获取限流统计信息

**认证**: 需要

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total_requests": 10000,
    "allowed_requests": 9800,
    "blocked_requests": 200,
    "active_limiters": 15,
    "rules": [
      {
        "name": "global",
        "hits": 150,
        "blocks": 10
      }
    ]
  }
}
```

#### 重置限流计数器

**端点**: `POST /admin/api/v1/security/rate-limit/reset`

**描述**: 重置指定的限流计数器

**认证**: 需要 `admin` 角色

**请求体**:
```json
{
  "key": "ip:************0:/api/v1/users:GET"
}
```

### 7.2 WAF管理

#### 获取WAF统计

**端点**: `GET /admin/api/v1/security/waf/stats`

**描述**: 获取WAF统计信息

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total_requests": 10000,
    "blocked_requests": 25,
    "logged_requests": 50,
    "rules": [
      {
        "name": "sql_injection",
        "hits": 15,
        "blocks": 15
      },
      {
        "name": "xss_attack",
        "hits": 10,
        "blocks": 10
      }
    ]
  }
}
```

## 8. 服务发现 API

### 8.1 获取服务列表

**端点**: `GET /admin/api/v1/discovery/services`

**描述**: 获取已发现的服务列表

**认证**: 需要

**响应示例**:
```json
{
  "success": true,
  "data": {
    "services": [
      {
        "name": "user-service",
        "instances": [
          {
            "id": "user-service-1",
            "address": "************:8081",
            "healthy": true,
            "metadata": {
              "version": "1.0.0",
              "environment": "production"
            }
          }
        ]
      }
    ]
  }
}
```

### 8.2 服务健康检查

**端点**: `GET /admin/api/v1/discovery/services/{name}/health`

**描述**: 获取指定服务的健康状态

**路径参数**:
- `name`: 服务名称

**响应示例**:
```json
{
  "success": true,
  "data": {
    "service": "user-service",
    "healthy_instances": 2,
    "total_instances": 3,
    "instances": [
      {
        "id": "user-service-1",
        "address": "************:8081",
        "healthy": true,
        "last_check": "2023-01-01T00:00:00Z"
      }
    ]
  }
}
```

这个API文档涵盖了API网关的主要管理接口，提供了完整的请求和响应示例，便于开发者集成和使用。
