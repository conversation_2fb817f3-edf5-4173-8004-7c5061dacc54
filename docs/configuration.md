# API Gateway 配置参考

## 1. 配置文件结构

API网关使用YAML格式的配置文件，支持环境变量替换和多文件合并。

### 1.1 基本结构

```yaml
# 服务器配置
server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120
  tls:
    enabled: false
    cert_file: ""
    key_file: ""

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"

# 指标配置
metrics:
  enabled: true
  port: 9090
  path: "/metrics"

# 追踪配置
tracing:
  enabled: false
  service_name: "api-gateway"

# 认证配置
auth:
  jwt: {}
  api_key: {}
  oidc: {}
  mtls: {}
  policies: {}

# 安全配置
security:
  rate_limit: {}
  waf: {}
  ip_filter: {}
  cors: {}

# 服务发现配置
discovery:
  type: "consul"
  consul: {}
  nacos: {}

# 负载均衡配置
load_balancer:
  algorithm: "round_robin"
  health_check: {}

# 路由配置
routes: []

# 插件配置
plugins:
  directory: "plugins"
  plugins: {}
```

## 2. 服务器配置 (server)

### 2.1 基本配置

```yaml
server:
  # 监听地址，格式为 host:port
  address: ":8080"
  
  # 读取超时时间（秒）
  read_timeout: 30
  
  # 写入超时时间（秒）
  write_timeout: 30
  
  # 空闲连接超时时间（秒）
  idle_timeout: 120
  
  # 最大请求头大小（字节）
  max_header_bytes: 1048576
  
  # 是否启用HTTP/2
  enable_http2: true
```

### 2.2 TLS配置

```yaml
server:
  tls:
    # 是否启用TLS
    enabled: true
    
    # 证书文件路径
    cert_file: "certs/server.crt"
    
    # 私钥文件路径
    key_file: "certs/server.key"
    
    # 最小TLS版本 (1.0, 1.1, 1.2, 1.3)
    min_version: "1.2"
    
    # 最大TLS版本
    max_version: "1.3"
    
    # 客户端证书验证模式
    # none: 不验证客户端证书
    # request: 请求客户端证书但不验证
    # require: 要求并验证客户端证书
    client_auth: "none"
    
    # CA证书文件（用于验证客户端证书）
    ca_file: "certs/ca.crt"
    
    # 支持的加密套件
    cipher_suites:
      - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
      - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
      - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
    
    # 支持的椭圆曲线
    curve_preferences:
      - "CurveP256"
      - "CurveP384"
      - "CurveP521"
```

## 3. 日志配置 (logging)

```yaml
logging:
  # 日志级别: debug, info, warn, error
  level: "info"
  
  # 日志格式: json, text
  format: "json"
  
  # 输出目标: stdout, stderr, 或文件路径
  output: "stdout"
  
  # 文件输出配置
  file: "logs/gateway.log"
  
  # 日志文件最大大小（MB）
  max_size: 100
  
  # 保留的备份文件数量
  max_backups: 10
  
  # 日志文件保留天数
  max_age: 30
  
  # 是否压缩旧日志文件
  compress: true
  
  # 是否包含调用者信息
  caller: true
  
  # 是否包含堆栈跟踪（错误级别）
  stack_trace: true
  
  # 日志采样配置（用于高频日志）
  sampling:
    enabled: true
    initial: 100      # 初始采样数
    thereafter: 100   # 之后每N条采样1条
  
  # 自定义字段
  fields:
    service: "api-gateway"
    version: "1.0.0"
    environment: "production"
```

## 4. 指标配置 (metrics)

```yaml
metrics:
  # 是否启用指标收集
  enabled: true
  
  # 指标服务器端口
  port: 9090
  
  # 指标端点路径
  path: "/metrics"
  
  # 指标命名空间
  namespace: "gateway"
  
  # 指标子系统名称
  subsystem: "api"
  
  # 自定义标签
  labels:
    datacenter: "us-east-1"
    environment: "production"
  
  # 直方图桶配置
  histogram_buckets:
    request_duration: [0.1, 0.3, 1.2, 5.0, 10.0]
    request_size: [1024, 4096, 16384, 65536, 262144]
    response_size: [1024, 4096, 16384, 65536, 262144]
  
  # 指标收集器配置
  collectors:
    # 请求指标
    requests:
      enabled: true
      
    # 系统指标
    system:
      enabled: true
      collect_interval: "15s"
      
    # Go运行时指标
    go_runtime:
      enabled: true
      
    # 进程指标
    process:
      enabled: true
```

## 5. 分布式追踪配置 (tracing)

```yaml
tracing:
  # 是否启用分布式追踪
  enabled: true
  
  # 服务名称
  service_name: "api-gateway"
  
  # 追踪系统类型: jaeger, zipkin
  type: "jaeger"
  
  # 采样率 (0.0 - 1.0)
  sample_rate: 0.1
  
  # Jaeger配置
  jaeger:
    # Jaeger Agent地址
    agent_host: "localhost"
    agent_port: 6831
    
    # Jaeger Collector端点
    endpoint: "http://localhost:14268/api/traces"
    
    # 认证用户名
    username: ""
    
    # 认证密码
    password: ""
    
    # 标签值最大长度
    max_tag_value_length: 256
    
    # 自定义标签
    tags:
      version: "1.0.0"
      environment: "production"
      datacenter: "us-east-1"
  
  # Zipkin配置
  zipkin:
    # Zipkin端点
    endpoint: "http://localhost:9411/api/v2/spans"
    
    # 批量发送大小
    batch_size: 100
    
    # 发送超时时间
    timeout: "5s"
```

## 6. 认证配置 (auth)

### 6.1 JWT认证

```yaml
auth:
  jwt:
    # 是否启用JWT认证
    enabled: true
    
    # JWT密钥（HMAC算法）
    secret: "your-256-bit-secret-key"
    
    # 签名算法: HS256, HS384, HS512, RS256, RS384, RS512
    algorithm: "HS256"
    
    # Token过期时间（秒）
    expiration: 3600
    
    # RSA公钥（用于RS*算法）
    public_key: |
      -----BEGIN PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
      -----END PUBLIC KEY-----
    
    # RSA私钥（用于签发Token）
    private_key: |
      -----BEGIN PRIVATE KEY-----
      MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
      -----END PRIVATE KEY-----
    
    # Token发行者
    issuer: "api-gateway"
    
    # Token受众
    audience: "api-clients"
    
    # 时钟偏移容忍度（秒）
    clock_skew: 60
    
    # 是否验证过期时间
    verify_exp: true
    
    # 是否验证生效时间
    verify_nbf: true
    
    # 是否验证签发时间
    verify_iat: true
```

### 6.2 API Key认证

```yaml
auth:
  api_key:
    # 是否启用API Key认证
    enabled: true
    
    # Header名称
    header_name: "X-API-Key"
    
    # 查询参数名称
    query_param: "api_key"
    
    # 存储类型: memory, redis, database
    storage: "memory"
    
    # Redis配置（当storage为redis时）
    redis:
      address: "localhost:6379"
      password: ""
      db: 0
      key_prefix: "apikey:"
    
    # 数据库配置（当storage为database时）
    database:
      driver: "postgres"
      dsn: "postgres://user:pass@localhost/gateway?sslmode=disable"
      table: "api_keys"
    
    # 默认API Key配置
    default_keys:
      - key: "ak_1234567890abcdef"
        name: "admin-key"
        user_id: "admin"
        roles: ["admin"]
        permissions: ["*"]
        active: true
        expires_at: "2024-12-31T23:59:59Z"
```

### 6.3 OAuth/OIDC认证

```yaml
auth:
  oidc:
    # 是否启用OIDC认证
    enabled: true
    
    # OIDC提供商URL
    issuer: "https://auth.example.com"
    
    # 客户端ID
    client_id: "gateway-client"
    
    # 客户端密钥
    client_secret: "client-secret"
    
    # 重定向URL
    redirect_url: "https://gateway.example.com/auth/callback"
    
    # 请求的作用域
    scopes: ["openid", "profile", "email"]
    
    # 自定义端点（可选）
    endpoints:
      authorization: "https://auth.example.com/oauth/authorize"
      token: "https://auth.example.com/oauth/token"
      userinfo: "https://auth.example.com/oauth/userinfo"
      jwks: "https://auth.example.com/.well-known/jwks.json"
    
    # Token验证配置
    token_validation:
      # 是否验证签名
      verify_signature: true
      
      # 是否验证受众
      verify_audience: true
      
      # 是否验证发行者
      verify_issuer: true
      
      # 时钟偏移容忍度（秒）
      clock_skew: 60
    
    # 用户信息映射
    user_mapping:
      user_id: "sub"
      username: "preferred_username"
      email: "email"
      roles: "roles"
      groups: "groups"
```

### 6.4 mTLS认证

```yaml
auth:
  mtls:
    # 是否启用mTLS认证
    enabled: true
    
    # CA证书文件
    ca_file: "certs/ca.crt"
    
    # 是否验证客户端证书
    verify_client: true
    
    # 证书撤销列表（CRL）文件
    crl_file: "certs/crl.pem"
    
    # 证书验证深度
    verify_depth: 3
    
    # 用户信息提取配置
    user_extraction:
      # 从证书主题中提取用户ID
      user_id_field: "CN"  # CN, O, OU, etc.
      
      # 从证书扩展中提取角色
      roles_extension: "*******.4.1.12345.1"
      
      # 从组织单位中提取角色
      roles_from_ou: true
```

## 7. 安全配置 (security)

### 7.1 限流配置

```yaml
security:
  rate_limit:
    # 是否启用限流
    enabled: true
    
    # 限流算法: token_bucket, leaky_bucket
    algorithm: "token_bucket"
    
    # 存储后端: memory, redis
    storage: "memory"
    
    # Redis配置（当storage为redis时）
    redis:
      address: "localhost:6379"
      password: ""
      db: 1
      key_prefix: "ratelimit:"
    
    # 限流规则
    rules:
      # 全局限流
      - name: "global"
        path: "/*"
        method: "*"
        rate: 1000        # 每秒请求数
        burst: 2000       # 突发容量
        window: "1s"      # 时间窗口
        key_by: "ip"      # 限流键: ip, user, api_key, custom
        
        # 自定义键提取（当key_by为custom时）
        custom_key: "header:X-Client-ID"
        
        # 排除路径
        exclude_paths: ["/health", "/metrics"]
        
        # 排除IP
        exclude_ips: ["127.0.0.1", "::1"]
      
      # API特定限流
      - name: "api_limit"
        path: "/api/v1/*"
        method: "*"
        rate: 100
        burst: 200
        window: "1s"
        key_by: "user"
        
        # 条件限流
        conditions:
          headers:
            "X-Client-Type": "mobile"
          query_params:
            "version": "v1"
```

### 7.2 WAF配置

```yaml
security:
  waf:
    # 是否启用WAF
    enabled: true
    
    # WAF模式: blocking, monitoring, off
    mode: "blocking"
    
    # 规则集
    rule_sets:
      # OWASP核心规则集
      - name: "owasp_crs"
        enabled: true
        version: "3.3.0"
        
      # 自定义规则集
      - name: "custom_rules"
        enabled: true
        file: "rules/custom.yaml"
    
    # 内置规则
    rules:
      # SQL注入防护
      - name: "sql_injection"
        enabled: true
        pattern: "(?i)(union|select|insert|delete|update|drop|create|alter|exec|script|declare|cast|convert)"
        action: "block"
        description: "Block SQL injection attempts"
        severity: "high"
        
        # 规则应用范围
        scope:
          paths: ["/*"]
          methods: ["POST", "PUT", "PATCH"]
          content_types: ["application/json", "application/x-www-form-urlencoded"]
        
        # 排除条件
        exceptions:
          paths: ["/api/v1/search"]  # 搜索接口可能包含SQL关键词
          headers:
            "X-Bypass-WAF": "true"
      
      # XSS攻击防护
      - name: "xss_attack"
        enabled: true
        pattern: "(?i)(<script|javascript:|on\\w+\\s*=|<iframe|<object|<embed)"
        action: "block"
        description: "Block XSS attacks"
        severity: "high"
    
    # 异常分数配置
    anomaly_scoring:
      enabled: true
      inbound_threshold: 5    # 入站异常阈值
      outbound_threshold: 4   # 出站异常阈值
      
      # 分数权重
      weights:
        critical: 5
        error: 4
        warning: 3
        notice: 2
    
    # 响应配置
    responses:
      block:
        status_code: 403
        content_type: "application/json"
        body: '{"error": "Request blocked by security policy"}'
        
      captcha:
        status_code: 429
        content_type: "text/html"
        body_file: "templates/captcha.html"
```

这个配置参考文档提供了API网关所有主要配置选项的详细说明和示例，帮助用户根据具体需求进行配置。
