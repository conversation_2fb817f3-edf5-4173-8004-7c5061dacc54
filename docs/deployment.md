# API Gateway 部署指南

## 1. 部署环境准备

### 1.1 系统要求

**最低配置：**
- CPU: 2核心
- 内存: 4GB RAM
- 磁盘: 20GB 可用空间
- 操作系统: Linux (Ubuntu 18.04+, CentOS 7+, RHEL 7+)

**推荐配置：**
- CPU: 4核心或更多
- 内存: 8GB RAM或更多
- 磁盘: 50GB SSD
- 网络: 1Gbps带宽

**生产环境配置：**
- CPU: 8核心或更多
- 内存: 16GB RAM或更多
- 磁盘: 100GB SSD (分离日志存储)
- 网络: 10Gbps带宽
- 负载均衡器: 多实例部署

### 1.2 依赖服务

**必需服务：**
- Go 1.21+ (编译时)
- 无其他强制依赖

**可选服务：**
- Consul (服务发现)
- Redis (缓存、会话存储)
- PostgreSQL/MySQL (持久化存储)
- <PERSON><PERSON><PERSON> (分布式追踪)
- Prometheus (指标收集)
- Grafana (监控面板)

## 2. 单机部署

### 2.1 二进制部署

**步骤1: 下载和编译**
```bash
# 克隆代码
git clone https://github.com/your-org/api-gateway.git
cd api-gateway

# 编译
make build

# 或下载预编译二进制
wget https://github.com/your-org/api-gateway/releases/download/v1.0.0/gateway-linux-amd64
chmod +x gateway-linux-amd64
mv gateway-linux-amd64 /usr/local/bin/gateway
```

**步骤2: 配置文件**
```bash
# 创建配置目录
sudo mkdir -p /etc/api-gateway
sudo mkdir -p /var/log/api-gateway
sudo mkdir -p /var/lib/api-gateway

# 复制配置文件
sudo cp configs/gateway.yaml /etc/api-gateway/
sudo chown -R gateway:gateway /etc/api-gateway
sudo chown -R gateway:gateway /var/log/api-gateway
sudo chown -R gateway:gateway /var/lib/api-gateway
```

**步骤3: 创建系统用户**
```bash
# 创建专用用户
sudo useradd -r -s /bin/false -d /var/lib/api-gateway gateway
```

**步骤4: 系统服务配置**
```bash
# 创建systemd服务文件
sudo tee /etc/systemd/system/api-gateway.service > /dev/null <<EOF
[Unit]
Description=API Gateway
Documentation=https://github.com/your-org/api-gateway
After=network.target

[Service]
Type=simple
User=gateway
Group=gateway
ExecStart=/usr/local/bin/gateway -config /etc/api-gateway/gateway.yaml
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=api-gateway
KillMode=mixed
KillSignal=SIGTERM

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/api-gateway /var/lib/api-gateway

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable api-gateway
sudo systemctl start api-gateway
sudo systemctl status api-gateway
```

### 2.2 Docker部署

**步骤1: 构建镜像**
```bash
# 构建Docker镜像
docker build -t api-gateway:latest .

# 或使用预构建镜像
docker pull your-registry/api-gateway:latest
```

**步骤2: 运行容器**
```bash
# 创建数据目录
mkdir -p ./data/logs ./data/configs ./data/cache

# 复制配置文件
cp configs/gateway.yaml ./data/configs/

# 运行容器
docker run -d \
  --name api-gateway \
  --restart unless-stopped \
  -p 8080:8080 \
  -p 9090:9090 \
  -v $(pwd)/data/configs:/app/configs \
  -v $(pwd)/data/logs:/app/logs \
  -v $(pwd)/data/cache:/app/cache \
  -e GATEWAY_LOG_LEVEL=info \
  -e CONSUL_ADDRESS=consul:8500 \
  api-gateway:latest
```

**步骤3: Docker Compose部署**
```yaml
# docker-compose.yml
version: '3.8'

services:
  api-gateway:
    image: api-gateway:latest
    container_name: api-gateway
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "9090:9090"
    volumes:
      - ./configs:/app/configs:ro
      - ./logs:/app/logs
      - ./cache:/app/cache
    environment:
      - GATEWAY_LOG_LEVEL=info
      - CONSUL_ADDRESS=consul:8500
      - REDIS_URL=redis://redis:6379
    depends_on:
      - consul
      - redis
    networks:
      - gateway-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  consul:
    image: consul:1.16
    container_name: consul
    restart: unless-stopped
    ports:
      - "8500:8500"
    environment:
      - CONSUL_BIND_INTERFACE=eth0
    command: >
      consul agent -dev -ui -client=0.0.0.0
      -datacenter=dc1 -log-level=INFO
    volumes:
      - consul-data:/consul/data
    networks:
      - gateway-network

  redis:
    image: redis:7-alpine
    container_name: redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - gateway-network

volumes:
  consul-data:
  redis-data:

networks:
  gateway-network:
    driver: bridge
```

```bash
# 启动服务栈
docker-compose up -d

# 查看日志
docker-compose logs -f api-gateway

# 停止服务
docker-compose down
```

## 3. Kubernetes部署

### 3.1 基础部署

**步骤1: 创建命名空间**
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: api-gateway
  labels:
    name: api-gateway
```

**步骤2: 配置ConfigMap**
```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: api-gateway-config
  namespace: api-gateway
data:
  gateway.yaml: |
    server:
      address: ":8080"
      read_timeout: 30
      write_timeout: 30
    
    logging:
      level: "info"
      format: "json"
      output: "stdout"
    
    metrics:
      enabled: true
      port: 9090
      path: "/metrics"
    
    auth:
      jwt:
        enabled: true
        secret: "${JWT_SECRET}"
        algorithm: "HS256"
    
    discovery:
      type: "consul"
      consul:
        address: "consul:8500"
```

**步骤3: 创建Secret**
```yaml
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: api-gateway-secrets
  namespace: api-gateway
type: Opaque
data:
  jwt-secret: eW91ci1qd3Qtc2VjcmV0LWtleQ==  # base64编码的JWT密钥
  consul-token: ""  # Consul访问令牌（如需要）
```

**步骤4: 部署应用**
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: api-gateway
  labels:
    app: api-gateway
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: api-gateway
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: api-gateway
        image: api-gateway:1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 8080
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: api-gateway-secrets
              key: jwt-secret
        - name: CONSUL_TOKEN
          valueFrom:
            secretKeyRef:
              name: api-gateway-secrets
              key: consul-token
              optional: true
        - name: GATEWAY_LOG_LEVEL
          value: "info"
        volumeMounts:
        - name: config
          mountPath: /app/configs
          readOnly: true
        - name: cache
          mountPath: /app/cache
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: config
        configMap:
          name: api-gateway-config
      - name: cache
        emptyDir: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
```

**步骤5: 创建Service**
```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: api-gateway
  namespace: api-gateway
  labels:
    app: api-gateway
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8080
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  selector:
    app: api-gateway

---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-external
  namespace: api-gateway
  labels:
    app: api-gateway
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  - name: https
    port: 443
    targetPort: http
    protocol: TCP
  selector:
    app: api-gateway
```

**步骤6: 创建Ingress**
```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-gateway
  namespace: api-gateway
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.example.com
    secretName: api-gateway-tls
  rules:
  - host: api.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-gateway
            port:
              number: 8080
```

**步骤7: 部署到集群**
```bash
# 应用所有配置
kubectl apply -f namespace.yaml
kubectl apply -f configmap.yaml
kubectl apply -f secret.yaml
kubectl apply -f deployment.yaml
kubectl apply -f service.yaml
kubectl apply -f ingress.yaml

# 检查部署状态
kubectl get pods -n api-gateway
kubectl get services -n api-gateway
kubectl get ingress -n api-gateway

# 查看日志
kubectl logs -f deployment/api-gateway -n api-gateway

# 端口转发测试
kubectl port-forward service/api-gateway 8080:8080 -n api-gateway
```

### 3.2 高可用部署

**步骤1: HPA配置**
```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
  namespace: api-gateway
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
```

**步骤2: PDB配置**
```yaml
# pdb.yaml
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: api-gateway-pdb
  namespace: api-gateway
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: api-gateway
```

**步骤3: 多区域部署**
```yaml
# 在deployment.yaml中添加反亲和性
spec:
  template:
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - api-gateway
              topologyKey: kubernetes.io/hostname
          - weight: 50
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - api-gateway
              topologyKey: topology.kubernetes.io/zone
```

这个部署指南涵盖了从单机到Kubernetes集群的各种部署场景，确保API网关能够在不同环境中稳定运行。
