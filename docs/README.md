# API Gateway 文档中心

欢迎来到API Gateway文档中心！这里包含了完整的架构设计、功能特性、部署指南和API参考文档。

## 📚 文档导航

### 🏗️ [架构设计](architecture.md)
- **整体架构图**: 系统组件和交互关系
- **核心组件**: Gateway Core、认证管理器、安全管理器等
- **中间件架构**: 请求处理流程和中间件链
- **配置系统**: 配置文件结构和环境变量支持
- **可观测性**: 指标、日志、追踪和审计系统
- **部署架构**: 单机、集群和高可用部署方案

### ✨ [功能特性](features.md)
- **零信任安全框架**
  - 多因子认证系统 (JWT, API Key, OAuth/OIDC, mTLS)
  - 细粒度授权控制 (RBAC, ABAC)
- **流量控制与保护**
  - 高级限流系统 (令牌桶、漏桶算法)
  - Web应用防火墙 (WAF)
  - IP过滤和CORS管理
- **协议支持与弹性**
  - HTTP/1.1, HTTP/2, WebSocket支持
  - 熔断器模式和重试机制
  - 舱壁模式资源隔离
- **服务发现与负载均衡**
  - Consul、Nacos集成
  - 多种负载均衡算法
  - 健康检查机制
- **可观测性系统**
  - Prometheus指标监控
  - 分布式追踪 (Jaeger)
  - 结构化日志和审计日志
- **插件系统**
  - 内置插件 (日志、缓存、转换等)
  - 自定义插件开发

### 🚀 [部署指南](deployment.md)
- **部署环境准备**
  - 系统要求和依赖服务
- **单机部署**
  - 二进制部署
  - Docker部署
  - Docker Compose部署
- **Kubernetes部署**
  - 基础部署配置
  - 高可用部署
  - 多区域部署
- **生产环境优化**
  - 性能调优
  - 安全加固
  - 监控配置

### ⚙️ [配置参考](configuration.md)
- **配置文件结构**: YAML配置格式和组织方式
- **服务器配置**: 监听地址、超时设置、TLS配置
- **日志配置**: 日志级别、格式、输出和轮转
- **指标配置**: Prometheus指标收集和自定义标签
- **分布式追踪**: Jaeger和Zipkin集成配置
- **认证配置**: JWT、API Key、OAuth/OIDC、mTLS详细配置
- **安全配置**: 限流、WAF、IP过滤、CORS配置
- **服务发现**: Consul、Nacos连接和服务注册配置
- **路由配置**: 静态路由和动态路由配置
- **插件配置**: 内置插件和自定义插件配置

### 🔌 [API参考](api.md)
- **认证方式**: JWT Token和API Key认证
- **健康检查API**: 系统和组件健康状态
- **指标API**: Prometheus指标和系统统计
- **配置管理API**: 配置查看、更新和重载
- **路由管理API**: 路由的增删改查操作
- **认证管理API**: API Key和JWT Token管理
- **安全管理API**: 限流和WAF统计管理
- **服务发现API**: 服务列表和健康检查

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装Go 1.21+
go version

# 克隆项目
git clone https://github.com/your-org/api-gateway.git
cd api-gateway
```

### 2. 构建和运行
```bash
# 构建项目
make build

# 启动开发环境
make services-up
make dev
```

### 3. 验证部署
```bash
# 健康检查
curl http://localhost:8080/health

# 查看指标
curl http://localhost:9090/metrics
```

### 4. 访问管理界面
- **API网关**: http://localhost:8080
- **Consul UI**: http://localhost:8500
- **Jaeger UI**: http://localhost:16686
- **Grafana**: http://localhost:3000 (admin/admin)

## 📖 学习路径

### 初学者
1. 阅读 [架构设计](architecture.md) 了解系统整体结构
2. 查看 [功能特性](features.md) 了解核心功能
3. 按照 [部署指南](deployment.md) 进行本地部署
4. 使用 [API参考](api.md) 进行基本操作

### 进阶用户
1. 深入学习 [配置参考](configuration.md) 进行定制化配置
2. 了解安全配置和最佳实践
3. 学习插件开发和扩展
4. 掌握生产环境部署和运维

### 运维人员
1. 重点关注 [部署指南](deployment.md) 的生产环境部署
2. 学习监控和告警配置
3. 掌握故障排查和性能优化
4. 了解安全加固和合规要求

## 🔧 配置示例

### 基础配置
```yaml
server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30

auth:
  jwt:
    enabled: true
    secret: "your-secret-key"
    algorithm: "HS256"

security:
  rate_limit:
    enabled: true
    rules:
      - path: "/api/*"
        rate: 100
        burst: 200

routes:
  - name: "user_service"
    path: "/api/v1/users/*"
    upstream:
      type: "static"
      servers:
        - host: "localhost"
          port: 8081
```

### 生产环境配置
```yaml
server:
  tls:
    enabled: true
    cert_file: "/etc/ssl/certs/gateway.crt"
    key_file: "/etc/ssl/private/gateway.key"

logging:
  level: "info"
  format: "json"
  file: "/var/log/api-gateway/gateway.log"

metrics:
  enabled: true
  port: 9090

tracing:
  enabled: true
  endpoint: "http://jaeger:14268/api/traces"

discovery:
  type: "consul"
  consul:
    address: "consul.example.com:8500"
    token: "${CONSUL_TOKEN}"
```

## 🛠️ 开发工具

### Make命令
```bash
make build          # 构建应用
make run             # 运行应用
make test            # 运行测试
make docker-build    # 构建Docker镜像
make services-up     # 启动开发服务
make lint            # 代码检查
make fmt             # 代码格式化
```

### Docker命令
```bash
# 构建镜像
docker build -t api-gateway:latest .

# 运行容器
docker run -p 8080:8080 api-gateway:latest

# 使用Docker Compose
docker-compose up -d
```

### Kubernetes命令
```bash
# 部署到集群
kubectl apply -f deployments/k8s/

# 查看状态
kubectl get pods -n api-gateway

# 查看日志
kubectl logs -f deployment/api-gateway -n api-gateway
```

## 🤝 贡献指南

### 文档贡献
1. Fork项目仓库
2. 创建文档分支: `git checkout -b docs/feature-name`
3. 编写或更新文档
4. 提交变更: `git commit -m "docs: add feature documentation"`
5. 推送分支: `git push origin docs/feature-name`
6. 创建Pull Request

### 文档规范
- 使用Markdown格式
- 包含完整的代码示例
- 提供清晰的配置说明
- 添加适当的图表和流程图
- 保持文档结构一致

## 📞 支持与反馈

### 获取帮助
- **GitHub Issues**: [提交问题](https://github.com/your-org/api-gateway/issues)
- **GitHub Discussions**: [参与讨论](https://github.com/your-org/api-gateway/discussions)
- **Wiki**: [查看Wiki](https://github.com/your-org/api-gateway/wiki)

### 社区资源
- **官方文档**: https://api-gateway.example.com/docs
- **示例项目**: https://github.com/your-org/api-gateway-examples
- **最佳实践**: https://github.com/your-org/api-gateway-best-practices

## 📄 许可证

本项目采用 [MIT License](../LICENSE) 开源许可证。

---

**注意**: 本文档会持续更新，请关注最新版本。如有疑问或建议，欢迎提交Issue或Pull Request。
