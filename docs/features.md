# API Gateway 功能特性详解

## 1. 零信任安全框架

### 1.1 多因子认证系统

#### JWT Token 认证

**配置示例：**
```yaml
auth:
  jwt:
    enabled: true
    secret: "your-256-bit-secret-key"
    algorithm: "HS256"  # 支持 HS256, <PERSON>S384, <PERSON>S512, RS256, RS384, RS512
    expiration: 3600    # Token过期时间（秒）
    public_key: |       # RSA公钥（用于RS*算法）
      -----BEGIN PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
      -----END PUBLIC KEY-----
```

**使用示例：**
```bash
# 获取Token
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'

# 使用Token访问API
curl -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
     http://localhost:8080/api/v1/users
```

#### API Key 认证

**配置示例：**
```yaml
auth:
  api_key:
    enabled: true
    header_name: "X-API-Key"     # Header名称
    query_param: "api_key"       # 查询参数名称
    storage: "memory"            # 存储方式: memory, redis, database
```

**使用示例：**
```bash
# Header方式
curl -H "X-API-Key: ak_1234567890abcdef" \
     http://localhost:8080/api/v1/users

# 查询参数方式
curl "http://localhost:8080/api/v1/users?api_key=ak_1234567890abcdef"
```

#### OAuth/OIDC 认证

**配置示例：**
```yaml
auth:
  oidc:
    enabled: true
    issuer: "https://auth.example.com"
    client_id: "gateway-client"
    client_secret: "client-secret"
    redirect_url: "https://gateway.example.com/auth/callback"
    scopes: ["openid", "profile", "email"]
    
    # 可选：自定义端点
    endpoints:
      authorization: "https://auth.example.com/oauth/authorize"
      token: "https://auth.example.com/oauth/token"
      userinfo: "https://auth.example.com/oauth/userinfo"
```

#### mTLS 证书认证

**配置示例：**
```yaml
auth:
  mtls:
    enabled: true
    ca_file: "certs/ca.crt"      # CA证书文件
    verify_client: true          # 验证客户端证书
    
server:
  tls:
    enabled: true
    cert_file: "certs/server.crt"
    key_file: "certs/server.key"
    client_auth: "require"       # 要求客户端证书
```

### 1.2 细粒度授权控制

#### 基于角色的访问控制 (RBAC)

**策略配置：**
```yaml
auth:
  policies:
    engine: "builtin"  # 或 "opa"
    policies:
      - name: "admin_full_access"
        path: "/admin/*"
        method: "*"
        roles: ["admin"]
        permissions: ["admin:*"]
        
      - name: "user_read_only"
        path: "/api/v1/users/*"
        method: "GET"
        roles: ["user", "admin"]
        permissions: ["user:read"]
        
      - name: "user_profile_access"
        path: "/api/v1/users/{user_id}"
        method: "*"
        roles: ["user"]
        permissions: ["user:profile"]
        conditions:
          user_attribute: "user_id={user_id}"  # 只能访问自己的资源
```

#### 基于属性的访问控制 (ABAC)

**高级策略示例：**
```yaml
auth:
  policies:
    policies:
      - name: "time_based_access"
        path: "/api/v1/sensitive/*"
        method: "*"
        roles: ["employee"]
        conditions:
          time_range: "09:00-17:00"     # 工作时间访问
          ip_range: "***********/24"    # 内网访问
          user_attribute: "department=engineering"
          
      - name: "geo_restricted_access"
        path: "/api/v1/financial/*"
        method: "*"
        roles: ["finance"]
        conditions:
          geo_location: "country=US,CN"  # 地理位置限制
          device_type: "trusted"         # 可信设备
```

## 2. 流量控制与保护

### 2.1 高级限流系统

#### 令牌桶算法

**配置示例：**
```yaml
security:
  rate_limit:
    enabled: true
    algorithm: "token_bucket"
    rules:
      # 全局限流
      - name: "global_limit"
        path: "/*"
        method: "*"
        rate: 1000        # 每秒1000个请求
        burst: 2000       # 突发容量2000
        window: "1s"      # 时间窗口
        key_by: "ip"      # 按IP限流
        
      # API特定限流
      - name: "api_limit"
        path: "/api/v1/*"
        method: "*"
        rate: 100
        burst: 200
        window: "1s"
        key_by: "user"    # 按用户限流
        
      # 认证端点限流
      - name: "auth_limit"
        path: "/auth/*"
        method: "POST"
        rate: 10
        burst: 20
        window: "1m"      # 每分钟限制
        key_by: "ip"
```

#### 漏桶算法

**配置示例：**
```yaml
security:
  rate_limit:
    algorithm: "leaky_bucket"
    rules:
      - name: "smooth_limit"
        path: "/api/v1/upload/*"
        method: "POST"
        rate: 50          # 每秒处理50个请求
        capacity: 100     # 桶容量100
        leak_rate: "20ms" # 每20ms处理一个请求
        key_by: "user"
```

### 2.2 Web应用防火墙 (WAF)

#### 内置安全规则

**配置示例：**
```yaml
security:
  waf:
    enabled: true
    mode: "blocking"  # blocking, monitoring, off
    rules:
      # SQL注入防护
      - name: "sql_injection"
        pattern: "(?i)(union|select|insert|delete|update|drop|create|alter|exec|script|declare|cast|convert)"
        action: "block"
        description: "Block SQL injection attempts"
        severity: "high"
        
      # XSS攻击防护
      - name: "xss_attack"
        pattern: "(?i)(<script|javascript:|on\\w+\\s*=|<iframe|<object|<embed)"
        action: "block"
        description: "Block XSS attacks"
        severity: "high"
        
      # 路径遍历防护
      - name: "path_traversal"
        pattern: "(\\.\\./|\\.\\.\\\\/|%2e%2e%2f|%2e%2e%5c)"
        action: "block"
        description: "Block path traversal attempts"
        severity: "medium"
        
      # 命令注入防护
      - name: "command_injection"
        pattern: "(?i)(;|\\||&|`|\\$\\(|<\\(|>\\(|\\$\\{)"
        action: "block"
        description: "Block command injection attempts"
        severity: "high"
        
      # 可疑User-Agent
      - name: "suspicious_user_agent"
        pattern: "(?i)(sqlmap|nmap|nikto|burp|owasp|zap|w3af|acunetix|nessus)"
        action: "log"
        description: "Log suspicious user agents"
        severity: "medium"
```

#### 自定义规则

**高级WAF配置：**
```yaml
security:
  waf:
    custom_rules:
      # 业务逻辑保护
      - name: "business_logic_protection"
        conditions:
          - field: "request.body"
            operator: "contains"
            value: "admin"
          - field: "request.headers.user-agent"
            operator: "not_contains"
            value: "legitimate-client"
        action: "block"
        
      # 频率异常检测
      - name: "frequency_anomaly"
        conditions:
          - field: "request.path"
            operator: "equals"
            value: "/api/v1/login"
          - field: "request.frequency"
            operator: "greater_than"
            value: 10
            window: "1m"
        action: "captcha"  # 触发验证码
```

### 2.3 IP过滤系统

**配置示例：**
```yaml
security:
  ip_filter:
    enabled: true
    mode: "whitelist"  # whitelist, blacklist, mixed
    
    # 白名单配置
    whitelist:
      - "***********/24"    # 内网段
      - "10.0.0.0/8"        # 私有网络
      - "***********/24"    # 办公网络
      
    # 黑名单配置
    blacklist:
      - "*************/24"  # 恶意IP段
      - "*************"     # 特定恶意IP
      
    # 地理位置过滤
    geo_filter:
      enabled: true
      allowed_countries: ["US", "CN", "JP"]
      blocked_countries: ["XX"]  # 未知国家
      
    # 动态IP管理
    dynamic:
      enabled: true
      auto_block_threshold: 100   # 自动封禁阈值
      auto_block_duration: "1h"   # 封禁时长
      whitelist_duration: "24h"   # 白名单缓存时长
```

### 2.4 CORS管理

**配置示例：**
```yaml
security:
  cors:
    enabled: true
    
    # 基础配置
    allowed_origins:
      - "https://app.example.com"
      - "https://*.example.com"     # 通配符子域名
      - "http://localhost:3000"     # 开发环境
      
    allowed_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
      - "HEAD"
      - "PATCH"
      
    allowed_headers:
      - "Content-Type"
      - "Authorization"
      - "X-Requested-With"
      - "X-API-Key"
      - "X-Custom-Header"
      
    exposed_headers:
      - "Content-Length"
      - "Content-Type"
      - "X-Request-ID"
      - "X-Rate-Limit-Remaining"
      
    allow_credentials: true
    max_age: 86400  # 预检请求缓存时间（秒）
    
    # 高级配置
    vary_origin: true              # 根据Origin返回不同的CORS头
    options_passthrough: false     # 是否将OPTIONS请求传递给上游
    
    # 条件CORS
    conditional_cors:
      - origins: ["https://admin.example.com"]
        paths: ["/admin/*"]
        methods: ["GET", "POST"]
        credentials: true
        
      - origins: ["*"]
        paths: ["/public/*"]
        methods: ["GET"]
        credentials: false
```

## 3. 协议支持与弹性

### 3.1 协议支持

#### HTTP/2 配置

**配置示例：**
```yaml
server:
  http2:
    enabled: true
    max_concurrent_streams: 250
    max_frame_size: 16384
    max_header_list_size: 8192
    initial_window_size: 65535
    
    # 服务器推送
    server_push:
      enabled: true
      resources:
        - path: "/api/v1/users"
          push: ["/static/css/main.css", "/static/js/main.js"]
```

#### WebSocket 支持

**配置示例：**
```yaml
websocket:
  enabled: true
  read_buffer_size: 1024
  write_buffer_size: 1024
  check_origin: true
  
  # WebSocket路由
  routes:
    - path: "/ws/chat"
      upstream: "chat-service"
      subprotocols: ["chat", "v1.chat"]
      
    - path: "/ws/notifications"
      upstream: "notification-service"
      auth_required: true
```

### 3.2 熔断器模式

**配置示例：**
```yaml
circuit_breaker:
  enabled: true
  
  # 全局默认配置
  default:
    failure_threshold: 5        # 失败阈值
    recovery_timeout: "30s"     # 恢复超时
    timeout: "10s"              # 请求超时
    
  # 服务特定配置
  services:
    user-service:
      failure_threshold: 3
      recovery_timeout: "15s"
      timeout: "5s"
      
    order-service:
      failure_threshold: 10
      recovery_timeout: "60s"
      timeout: "30s"
```

### 3.3 重试机制

**配置示例：**
```yaml
retry:
  enabled: true
  
  # 默认重试策略
  default:
    max_retries: 3
    base_delay: "100ms"
    max_delay: "5s"
    backoff_multiplier: 2.0
    jitter: true
    
    # 重试条件
    retry_on:
      - "5xx"                   # 服务器错误
      - "timeout"               # 超时
      - "connection_error"      # 连接错误
      
    # 不重试条件
    no_retry_on:
      - "4xx"                   # 客户端错误
      - "401"                   # 认证错误
      - "403"                   # 授权错误
      
  # 路径特定配置
  paths:
    "/api/v1/orders":
      max_retries: 5
      base_delay: "200ms"
      
    "/api/v1/payments":
      max_retries: 1            # 支付接口谨慎重试
      base_delay: "500ms"
```

### 3.4 舱壁模式

**配置示例：**
```yaml
bulkhead:
  enabled: true
  
  # 资源池配置
  pools:
    critical:
      max_concurrent: 100       # 最大并发数
      queue_size: 200          # 队列大小
      timeout: "30s"           # 等待超时
      
    normal:
      max_concurrent: 50
      queue_size: 100
      timeout: "10s"
      
    background:
      max_concurrent: 20
      queue_size: 50
      timeout: "5s"
      
  # 路径分配
  path_assignments:
    "/api/v1/critical/*": "critical"
    "/api/v1/orders/*": "normal"
    "/api/v1/reports/*": "background"
```

## 4. 服务发现与负载均衡

### 4.1 Consul集成

**配置示例：**
```yaml
discovery:
  type: "consul"
  consul:
    address: "localhost:8500"
    datacenter: "dc1"
    token: "your-consul-token"
    
    # 健康检查配置
    health_check:
      enabled: true
      interval: "10s"
      timeout: "3s"
      deregister_critical_after: "30s"
      
    # 服务注册配置
    service_registration:
      enabled: true
      service_name: "api-gateway"
      service_id: "api-gateway-1"
      tags: ["gateway", "v1.0.0"]
      meta:
        version: "1.0.0"
        environment: "production"
```

### 4.2 Nacos集成

**配置示例：**
```yaml
discovery:
  type: "nacos"
  nacos:
    server_configs:
      - ip_addr: "localhost"
        port: 8848
      - ip_addr: "nacos2.example.com"
        port: 8848
        
    client_config:
      namespace_id: "public"
      timeout_ms: 5000
      not_load_cache_at_start: true
      log_dir: "logs/nacos"
      cache_dir: "cache/nacos"
      log_level: "info"
      
    # 服务配置
    service_config:
      group_name: "DEFAULT_GROUP"
      cluster_name: "DEFAULT"
      weight: 1.0
      ephemeral: true
```

### 4.3 负载均衡算法

**配置示例：**
```yaml
load_balancer:
  # 全局默认算法
  default_algorithm: "round_robin"
  
  # 算法特定配置
  algorithms:
    round_robin:
      enabled: true
      
    weighted_round_robin:
      enabled: true
      
    random:
      enabled: true
      
    ip_hash:
      enabled: true
      hash_key: "client_ip"     # client_ip, x_forwarded_for, custom_header
      
    least_connections:
      enabled: true
      connection_timeout: "30s"
      
    consistent_hash:
      enabled: true
      hash_key: "user_id"       # 从请求中提取的键
      virtual_nodes: 150        # 虚拟节点数
      
  # 服务特定配置
  services:
    user-service:
      algorithm: "consistent_hash"
      hash_key: "user_id"
      
    file-service:
      algorithm: "ip_hash"
      
    api-service:
      algorithm: "least_connections"
```

### 4.4 健康检查

**配置示例：**
```yaml
health_check:
  enabled: true
  
  # 全局配置
  global:
    interval: "30s"             # 检查间隔
    timeout: "5s"               # 检查超时
    healthy_threshold: 2        # 健康阈值
    unhealthy_threshold: 3      # 不健康阈值
    
  # 检查类型
  checks:
    http:
      enabled: true
      path: "/health"
      method: "GET"
      expected_status: [200, 204]
      expected_body: ""
      headers:
        User-Agent: "API-Gateway-HealthCheck/1.0"
        
    tcp:
      enabled: true
      
    grpc:
      enabled: false
      service: "health.v1.Health"
      
  # 服务特定配置
  services:
    user-service:
      interval: "15s"
      path: "/api/health"
      
    database-service:
      type: "tcp"
      interval: "10s"
      timeout: "3s"
```

## 5. 可观测性系统

### 5.1 指标监控

#### Prometheus指标

**核心指标类别：**
```yaml
metrics:
  enabled: true
  namespace: "gateway"
  subsystem: "api"

  # 指标配置
  collectors:
    # 请求指标
    requests:
      enabled: true
      buckets: [0.1, 0.3, 1.2, 5.0]  # 延迟分桶

    # 系统指标
    system:
      enabled: true
      collect_interval: "15s"

    # 业务指标
    business:
      enabled: true
      custom_labels: ["service", "version", "environment"]
```

**指标示例：**
```go
// 请求计数器
gateway_requests_total{method="GET", path="/api/v1/users", status="200", upstream="user-service"} 1234

// 请求延迟直方图
gateway_request_duration_seconds_bucket{method="GET", path="/api/v1/users", le="0.1"} 100
gateway_request_duration_seconds_bucket{method="GET", path="/api/v1/users", le="0.3"} 200
gateway_request_duration_seconds_sum{method="GET", path="/api/v1/users"} 45.6
gateway_request_duration_seconds_count{method="GET", path="/api/v1/users"} 300

// 活跃连接数
gateway_active_connections 45

// 限流命中
gateway_rate_limit_hits_total{path="/api/v1/users", key_type="ip"} 12

// WAF拦截
gateway_waf_blocks_total{rule="sql_injection", action="block"} 5

// 熔断器状态
gateway_circuit_breaker_state{name="user-service"} 0  # 0=closed, 1=open, 2=half-open
```

### 5.2 分布式追踪

#### Jaeger集成

**配置示例：**
```yaml
tracing:
  enabled: true
  service_name: "api-gateway"

  # Jaeger配置
  jaeger:
    endpoint: "http://localhost:14268/api/traces"
    agent_host: "localhost"
    agent_port: 6831
    sample_rate: 0.1            # 采样率
    max_tag_value_length: 256

    # 标签配置
    tags:
      version: "1.0.0"
      environment: "production"
      datacenter: "us-east-1"

  # 追踪配置
  trace_config:
    max_spans_per_trace: 1000
    span_timeout: "30s"

    # 自动追踪
    auto_trace:
      http_requests: true
      database_queries: true
      cache_operations: true
      external_calls: true
```

### 5.3 结构化日志

#### 日志配置

**配置示例：**
```yaml
logging:
  level: "info"
  format: "json"
  output: "stdout"

  # 文件输出配置
  file:
    enabled: true
    path: "logs/gateway.log"
    max_size: 100      # MB
    max_backups: 10
    max_age: 30        # 天
    compress: true

  # 日志字段配置
  fields:
    timestamp: true
    level: true
    caller: true
    stack_trace: true  # 错误时包含堆栈

  # 采样配置（高频日志采样）
  sampling:
    enabled: true
    initial: 100       # 初始采样数
    thereafter: 100    # 之后每N条采样1条

  # 日志过滤
  filters:
    - field: "path"
      operator: "equals"
      value: "/health"
      action: "drop"   # 丢弃健康检查日志

    - field: "status"
      operator: "greater_than"
      value: 499
      action: "keep"   # 保留错误日志
```

### 5.4 审计日志

#### 安全审计

**配置示例：**
```yaml
audit:
  enabled: true

  # 审计日志输出
  output:
    file:
      enabled: true
      path: "logs/audit.log"
      format: "json"

    syslog:
      enabled: false
      network: "udp"
      address: "localhost:514"
      facility: "local0"

    elasticsearch:
      enabled: false
      addresses: ["http://localhost:9200"]
      index: "gateway-audit"

  # 审计事件配置
  events:
    authentication:
      success: true
      failure: true

    authorization:
      granted: false     # 不记录成功授权
      denied: true

    security:
      violations: true
      rate_limits: true
      waf_blocks: true

    configuration:
      changes: true

    data_access:
      sensitive_paths: ["/api/v1/users/*/profile", "/api/v1/financial/*"]

  # 敏感数据脱敏
  sanitization:
    enabled: true
    fields: ["password", "token", "secret", "key"]
    replacement: "***REDACTED***"
```

## 6. 插件系统

### 6.1 内置插件

#### 日志插件

**配置示例：**
```yaml
plugins:
  logging:
    enabled: true
    level: "info"

    # 请求日志配置
    request_logging:
      enabled: true
      include_headers: true
      include_body: false      # 安全考虑
      max_body_size: 1024

    # 响应日志配置
    response_logging:
      enabled: true
      include_headers: true
      include_body: false

    # 错误日志配置
    error_logging:
      enabled: true
      include_stack_trace: true
```

#### 缓存插件

**配置示例：**
```yaml
plugins:
  cache:
    enabled: true

    # 缓存后端
    backend:
      type: "redis"
      redis:
        address: "localhost:6379"
        password: ""
        db: 0
        pool_size: 10

    # 缓存策略
    policies:
      - paths: ["/api/v1/users/*"]
        methods: ["GET"]
        ttl: "5m"
        vary_by: ["Authorization"]

      - paths: ["/api/v1/public/*"]
        methods: ["GET"]
        ttl: "1h"
        vary_by: []

    # 缓存控制
    cache_control:
      respect_cache_headers: true
      default_ttl: "5m"
      max_ttl: "1h"

    # 缓存键配置
    key_generator:
      include_query_params: true
      include_headers: ["Accept", "Accept-Language"]
      exclude_params: ["timestamp", "_"]
```

#### 转换插件

**配置示例：**
```yaml
plugins:
  transform:
    enabled: true

    # 请求转换
    request_transforms:
      - paths: ["/api/v1/*"]
        headers:
          add:
            X-Gateway-Version: "1.0.0"
            X-Request-Time: "${timestamp}"
          remove: ["X-Internal-Token"]
          rename:
            X-User-ID: "X-Authenticated-User"

        body:
          type: "json"
          transformations:
            - operation: "add"
              path: "/metadata/gateway_version"
              value: "1.0.0"
            - operation: "remove"
              path: "/internal_data"

    # 响应转换
    response_transforms:
      - paths: ["/api/v1/users/*"]
        headers:
          add:
            X-Response-Time: "${response_time}"
            Cache-Control: "private, max-age=300"
          remove: ["Server", "X-Powered-By"]

        body:
          type: "json"
          transformations:
            - operation: "remove"
              path: "/password"
            - operation: "add"
              path: "/links/self"
              value: "${request_url}"
```

### 6.2 自定义插件开发

#### 插件接口

**Go插件示例：**
```go
package main

import (
    "context"
    "encoding/json"
    "api-gateway/pkg/plugin"
)

type CustomPlugin struct {
    config map[string]interface{}
}

func (p *CustomPlugin) Name() string { return "custom-plugin" }
func (p *CustomPlugin) Version() string { return "1.0.0" }
func (p *CustomPlugin) Description() string { return "Custom business logic plugin" }
func (p *CustomPlugin) Priority() int { return 500 }

func (p *CustomPlugin) Phases() []plugin.PluginPhase {
    return []plugin.PluginPhase{
        plugin.PhasePreAuth,
        plugin.PhasePostProxy,
    }
}

func (p *CustomPlugin) Execute(ctx context.Context, phase plugin.PluginPhase, pluginCtx *plugin.PluginContext) (*plugin.PluginResult, error) {
    switch phase {
    case plugin.PhasePreAuth:
        return p.handlePreAuth(pluginCtx)
    case plugin.PhasePostProxy:
        return p.handlePostProxy(pluginCtx)
    }

    return &plugin.PluginResult{Continue: true}, nil
}

func (p *CustomPlugin) handlePreAuth(ctx *plugin.PluginContext) (*plugin.PluginResult, error) {
    // 自定义业务逻辑
    if ctx.Path == "/api/v1/special" {
        // 添加特殊处理
        ctx.Headers["X-Special-Processing"] = "true"
    }

    return &plugin.PluginResult{Continue: true}, nil
}

func (p *CustomPlugin) Configure(config map[string]interface{}) error {
    p.config = config
    return nil
}

func (p *CustomPlugin) Start() error { return nil }
func (p *CustomPlugin) Stop() error { return nil }
func (p *CustomPlugin) HealthCheck() error { return nil }

// 插件工厂函数
func NewCustomPlugin() plugin.Plugin {
    return &CustomPlugin{
        config: make(map[string]interface{}),
    }
}
```

这些功能特性确保了API网关能够提供企业级的安全性、可靠性和性能。每个功能都支持灵活的配置，可以根据具体的业务需求进行调整。
