# 动态配置管理示例

本示例展示了如何使用API网关的动态配置管理功能。

## 功能特性

### 1. 动态路由管理
- 添加新路由
- 更新现有路由
- 删除路由
- 无需重启网关

### 2. 安全规则动态配置
- 速率限制规则
- CORS配置
- WAF规则
- IP过滤规则

### 3. 插件动态管理
- 启用/禁用插件
- 更新插件配置
- 插件优先级调整

### 4. 认证配置动态更新
- JWT配置更新
- API Key配置
- OIDC配置

### 5. 配置版本管理
- 配置版本历史
- 配置回滚
- 变更追踪

## API接口

### 配置管理

#### 获取当前配置
```bash
curl -X GET http://localhost:8080/admin/api/v1/config
```

#### 更新完整配置
```bash
curl -X PUT http://localhost:8080/admin/api/v1/config \
  -H "Content-Type: application/json" \
  -d @config.json
```

#### 重载配置
```bash
curl -X POST http://localhost:8080/admin/api/v1/config/reload
```

### 路由管理

#### 获取所有路由
```bash
curl -X GET http://localhost:8080/admin/api/v1/config/routes
```

#### 添加新路由
```bash
curl -X POST http://localhost:8080/admin/api/v1/config/routes \
  -H "Content-Type: application/json" \
  -d '{
    "name": "new-service",
    "path": "/api/v1/new-service/*",
    "method": "*",
    "upstream": {
      "type": "static",
      "load_balancer": "round_robin",
      "servers": [
        {
          "host": "localhost",
          "port": 8082,
          "weight": 1
        }
      ]
    },
    "timeout": "30s",
    "retries": 3,
    "plugins": ["auth", "rate_limit"]
  }'
```

#### 更新路由
```bash
curl -X PUT http://localhost:8080/admin/api/v1/config/routes/new-service \
  -H "Content-Type: application/json" \
  -d '{
    "name": "new-service",
    "path": "/api/v1/new-service/*",
    "method": "*",
    "upstream": {
      "type": "static",
      "load_balancer": "weighted_round_robin",
      "servers": [
        {
          "host": "localhost",
          "port": 8082,
          "weight": 2
        },
        {
          "host": "localhost",
          "port": 8083,
          "weight": 1
        }
      ]
    },
    "timeout": "45s",
    "retries": 5,
    "plugins": ["auth", "rate_limit", "circuit_breaker"]
  }'
```

#### 删除路由
```bash
curl -X DELETE http://localhost:8080/admin/api/v1/config/routes/new-service
```

### 安全配置

#### 更新速率限制
```bash
curl -X PUT http://localhost:8080/admin/api/v1/config/security/rate-limit \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "algorithm": "token_bucket",
    "rules": [
      {
        "path": "/api/*",
        "method": "*",
        "rate": 200,
        "burst": 400,
        "window": "1m",
        "key_by": "ip"
      },
      {
        "path": "/auth/*",
        "method": "POST",
        "rate": 20,
        "burst": 40,
        "window": "1m",
        "key_by": "ip"
      }
    ]
  }'
```

#### 更新CORS配置
```bash
curl -X PUT http://localhost:8080/admin/api/v1/config/security/cors \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "allowed_origins": ["https://example.com", "https://app.example.com"],
    "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    "allowed_headers": ["Content-Type", "Authorization", "X-API-Key"],
    "exposed_headers": ["X-Request-ID"],
    "allow_credentials": true,
    "max_age": 86400
  }'
```

#### 更新WAF规则
```bash
curl -X PUT http://localhost:8080/admin/api/v1/config/security/waf \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "rules": [
      {
        "name": "sql_injection",
        "pattern": "(?i)(union|select|insert|delete|update|drop|create|alter|exec|script)",
        "action": "block",
        "description": "Block SQL injection attempts"
      },
      {
        "name": "xss_attack",
        "pattern": "(?i)(<script|javascript:|on\\w+\\s*=)",
        "action": "block",
        "description": "Block XSS attacks"
      },
      {
        "name": "suspicious_user_agent",
        "pattern": "(?i)(bot|crawler|spider|scraper)",
        "action": "log",
        "description": "Log suspicious user agents"
      }
    ]
  }'
```

### 插件管理

#### 更新插件配置
```bash
curl -X PUT http://localhost:8080/admin/api/v1/config/plugins/circuit_breaker \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "priority": 400,
    "failure_threshold": 10,
    "recovery_timeout": "60s",
    "timeout": "15s"
  }'
```

#### 删除插件配置
```bash
curl -X DELETE http://localhost:8080/admin/api/v1/config/plugins/circuit_breaker
```

### 认证配置

#### 更新JWT配置
```bash
curl -X PUT http://localhost:8080/admin/api/v1/config/auth/jwt \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "secret": "new-jwt-secret-key",
    "algorithm": "HS256",
    "expiration": 7200
  }'
```

### 版本管理

#### 获取版本历史
```bash
curl -X GET http://localhost:8080/admin/api/v1/config/versions?limit=20
```

#### 回滚到指定版本
```bash
curl -X POST http://localhost:8080/admin/api/v1/config/versions/rollback/v1640995200
```

## 使用场景

### 1. 蓝绿部署
在蓝绿部署过程中，可以动态切换路由指向：

```bash
# 切换到绿色环境
curl -X PUT http://localhost:8080/admin/api/v1/config/routes/user-service \
  -H "Content-Type: application/json" \
  -d '{
    "name": "user-service",
    "path": "/api/v1/users/*",
    "method": "*",
    "upstream": {
      "type": "static",
      "load_balancer": "round_robin",
      "servers": [
        {
          "host": "green-user-service",
          "port": 8081,
          "weight": 1
        }
      ]
    }
  }'
```

### 2. 流量控制
根据系统负载动态调整速率限制：

```bash
# 高峰期降低速率限制
curl -X PUT http://localhost:8080/admin/api/v1/config/security/rate-limit \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "algorithm": "token_bucket",
    "rules": [
      {
        "path": "/api/*",
        "method": "*",
        "rate": 50,
        "burst": 100,
        "window": "1m",
        "key_by": "ip"
      }
    ]
  }'
```

### 3. 安全响应
发现安全威胁时快速添加防护规则：

```bash
# 添加新的WAF规则
curl -X PUT http://localhost:8080/admin/api/v1/config/security/waf \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "rules": [
      {
        "name": "block_malicious_ip",
        "pattern": "192\\.168\\.1\\.100",
        "action": "block",
        "description": "Block malicious IP"
      }
    ]
  }'
```

### 4. A/B测试
动态配置路由权重进行A/B测试：

```bash
# 配置A/B测试路由
curl -X PUT http://localhost:8080/admin/api/v1/config/routes/ab-test-service \
  -H "Content-Type: application/json" \
  -d '{
    "name": "ab-test-service",
    "path": "/api/v1/test/*",
    "method": "*",
    "upstream": {
      "type": "static",
      "load_balancer": "weighted_round_robin",
      "servers": [
        {
          "host": "service-a",
          "port": 8081,
          "weight": 8
        },
        {
          "host": "service-b",
          "port": 8082,
          "weight": 2
        }
      ]
    }
  }'
```

## 监控和告警

动态配置变更会产生相应的日志和指标，可以通过以下方式监控：

### 日志监控
```bash
# 查看配置变更日志
tail -f logs/gateway.log | grep "configuration change"
```

### 指标监控
```bash
# 查看配置相关指标
curl http://localhost:9090/metrics | grep config
```

## 最佳实践

1. **配置验证**: 所有配置变更都会经过验证，确保配置的正确性
2. **版本管理**: 保留配置版本历史，支持快速回滚
3. **渐进式更新**: 对于重要配置，建议先在测试环境验证
4. **监控告警**: 设置配置变更的监控和告警
5. **权限控制**: 限制配置变更的权限，确保安全性

## 故障排除

### 配置更新失败
1. 检查配置格式是否正确
2. 查看验证错误信息
3. 检查相关组件状态

### 配置不生效
1. 确认配置已成功更新
2. 检查相关组件是否支持热更新
3. 查看组件更新日志

### 性能影响
1. 监控配置更新的性能影响
2. 避免频繁的配置变更
3. 使用批量更新减少影响
