package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"api-gateway/internal/core"
	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"

	"github.com/opentracing/opentracing-go"
)

func main() {
	// Load configuration
	cfg, err := config.LoadConfig("configs/gateway.yaml")
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize telemetry
	logger, err := telemetry.NewLogger(cfg.Logging)
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}

	metrics, err := telemetry.NewMetrics(cfg.Metrics)
	if err != nil {
		log.Fatalf("Failed to initialize metrics: %v", err)
	}

	tracer, err := telemetry.NewTracer(cfg.Tracing)
	if err != nil {
		log.Fatalf("Failed to initialize tracer: %v", err)
	}
	opentracing.SetGlobalTracer(tracer)

	// Create gateway instance
	gateway, err := core.NewGateway(cfg, logger, metrics, tracer)
	if err != nil {
		log.Fatalf("Failed to create gateway: %v", err)
	}

	// Start the gateway server
	server := &http.Server{
		Addr:    cfg.Server.Address,
		Handler: gateway.Handler(),
	}

	// Start server in a goroutine
	go func() {
		logger.Info("Starting API Gateway", "address", cfg.Server.Address)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Error("Failed to start server", "error", err)
			os.Exit(1)
		}
	}()

	// Demonstrate dynamic configuration usage
	go demonstrateDynamicConfig(logger)

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Create shutdown context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown gateway
	if err := gateway.Shutdown(ctx); err != nil {
		logger.Error("Gateway shutdown error", "error", err)
	}

	// Shutdown server
	if err := server.Shutdown(ctx); err != nil {
		logger.Error("Server shutdown error", "error", err)
	}

	logger.Info("Server stopped")
}

// demonstrateDynamicConfig demonstrates dynamic configuration features
func demonstrateDynamicConfig(logger *telemetry.Logger) {
	// Wait for the gateway to start
	time.Sleep(5 * time.Second)

	logger.Info("Starting dynamic configuration demonstration")

	// Example 1: Add a new route
	addNewRoute(logger)

	// Example 2: Update security configuration
	updateSecurityConfig(logger)

	// Example 3: Update plugin configuration
	updatePluginConfig(logger)

	// Example 4: Demonstrate configuration rollback
	demonstrateRollback(logger)
}

func addNewRoute(logger *telemetry.Logger) {
	logger.Info("Adding new route dynamically")

	routeConfig := `{
		"name": "dynamic-service",
		"path": "/api/v1/dynamic/*",
		"method": "*",
		"upstream": {
			"type": "static",
			"load_balancer": "round_robin",
			"servers": [
				{
					"host": "localhost",
					"port": 8090,
					"weight": 1,
					"backup": false
				}
			],
			"health_check": {
				"enabled": true,
				"path": "/health",
				"interval": "30s",
				"timeout": "5s",
				"retries": 3
			}
		},
		"rewrite": {
			"enabled": false
		},
		"timeout": "30s",
		"retries": 3,
		"headers": {
			"X-Gateway": "api-gateway",
			"X-Route": "dynamic"
		},
		"plugins": ["auth", "rate_limit", "metrics"]
	}`

	if err := makeAPICall("POST", "/admin/api/v1/config/routes", routeConfig); err != nil {
		logger.Error("Failed to add new route", "error", err)
	} else {
		logger.Info("New route added successfully")
	}
}

func updateSecurityConfig(logger *telemetry.Logger) {
	logger.Info("Updating security configuration dynamically")

	rateLimitConfig := `{
		"enabled": true,
		"algorithm": "token_bucket",
		"rules": [
			{
				"path": "/api/*",
				"method": "*",
				"rate": 150,
				"burst": 300,
				"window": "1m",
				"key_by": "ip"
			},
			{
				"path": "/auth/*",
				"method": "POST",
				"rate": 15,
				"burst": 30,
				"window": "1m",
				"key_by": "ip"
			},
			{
				"path": "/api/v1/dynamic/*",
				"method": "*",
				"rate": 50,
				"burst": 100,
				"window": "1m",
				"key_by": "ip"
			}
		]
	}`

	if err := makeAPICall("PUT", "/admin/api/v1/config/security/rate-limit", rateLimitConfig); err != nil {
		logger.Error("Failed to update rate limit configuration", "error", err)
	} else {
		logger.Info("Rate limit configuration updated successfully")
	}
}

func updatePluginConfig(logger *telemetry.Logger) {
	logger.Info("Updating plugin configuration dynamically")

	circuitBreakerConfig := `{
		"enabled": true,
		"priority": 400,
		"failure_threshold": 8,
		"recovery_timeout": "45s",
		"timeout": "12s"
	}`

	if err := makeAPICall("PUT", "/admin/api/v1/config/plugins/circuit_breaker", circuitBreakerConfig); err != nil {
		logger.Error("Failed to update circuit breaker configuration", "error", err)
	} else {
		logger.Info("Circuit breaker configuration updated successfully")
	}
}

func demonstrateRollback(logger *telemetry.Logger) {
	logger.Info("Demonstrating configuration rollback")

	// First, get version history
	if err := makeAPICall("GET", "/admin/api/v1/config/versions?limit=5", ""); err != nil {
		logger.Error("Failed to get version history", "error", err)
		return
	}

	// Wait a bit
	time.Sleep(2 * time.Second)

	// Make a bad configuration change
	badConfig := `{
		"enabled": true,
		"algorithm": "invalid_algorithm",
		"rules": []
	}`

	logger.Info("Making a bad configuration change to demonstrate rollback")
	if err := makeAPICall("PUT", "/admin/api/v1/config/security/rate-limit", badConfig); err != nil {
		logger.Info("Bad configuration rejected as expected", "error", err)
	}

	// Show that we can still rollback if needed
	// In a real scenario, you would get the version from the history API
	// For this demo, we'll just show the API call
	logger.Info("Configuration rollback API available at: POST /admin/api/v1/config/versions/rollback/{version}")
}

func makeAPICall(method, path, body string) error {
	// This is a simplified example - in a real implementation,
	// you would use proper HTTP client with error handling
	
	url := fmt.Sprintf("http://localhost:8080%s", path)
	
	// Log the API call for demonstration
	fmt.Printf("API Call: %s %s\n", method, url)
	if body != "" {
		fmt.Printf("Body: %s\n", body)
	}
	fmt.Println("---")
	
	// In a real implementation, you would make the actual HTTP request here
	// For this demo, we'll just simulate success
	return nil
}

// Additional helper functions for demonstration

func demonstrateAdvancedFeatures(logger *telemetry.Logger) {
	// Demonstrate A/B testing configuration
	demonstrateABTesting(logger)
	
	// Demonstrate blue-green deployment
	demonstrateBlueGreenDeployment(logger)
	
	// Demonstrate security incident response
	demonstrateSecurityResponse(logger)
}

func demonstrateABTesting(logger *telemetry.Logger) {
	logger.Info("Configuring A/B testing route")

	abTestConfig := `{
		"name": "ab-test-service",
		"path": "/api/v1/test/*",
		"method": "*",
		"upstream": {
			"type": "static",
			"load_balancer": "weighted_round_robin",
			"servers": [
				{
					"host": "service-a",
					"port": 8081,
					"weight": 7,
					"backup": false
				},
				{
					"host": "service-b",
					"port": 8082,
					"weight": 3,
					"backup": false
				}
			]
		},
		"timeout": "30s",
		"retries": 3,
		"plugins": ["auth", "rate_limit", "metrics"]
	}`

	if err := makeAPICall("POST", "/admin/api/v1/config/routes", abTestConfig); err != nil {
		logger.Error("Failed to configure A/B testing route", "error", err)
	} else {
		logger.Info("A/B testing route configured successfully")
	}
}

func demonstrateBlueGreenDeployment(logger *telemetry.Logger) {
	logger.Info("Demonstrating blue-green deployment switch")

	// Switch from blue to green
	greenConfig := `{
		"name": "user-service",
		"path": "/api/v1/users/*",
		"method": "*",
		"upstream": {
			"type": "static",
			"load_balancer": "round_robin",
			"servers": [
				{
					"host": "green-user-service",
					"port": 8081,
					"weight": 1,
					"backup": false
				}
			]
		},
		"timeout": "30s",
		"retries": 3,
		"plugins": ["auth", "rate_limit", "metrics"]
	}`

	if err := makeAPICall("PUT", "/admin/api/v1/config/routes/user-service", greenConfig); err != nil {
		logger.Error("Failed to switch to green deployment", "error", err)
	} else {
		logger.Info("Successfully switched to green deployment")
	}
}

func demonstrateSecurityResponse(logger *telemetry.Logger) {
	logger.Info("Demonstrating security incident response")

	// Add emergency WAF rule
	emergencyWAFConfig := `{
		"enabled": true,
		"rules": [
			{
				"name": "emergency_block",
				"pattern": "(?i)(malicious|attack|exploit)",
				"action": "block",
				"description": "Emergency block for security incident"
			},
			{
				"name": "suspicious_ip_block",
				"pattern": "192\\.168\\.1\\.100",
				"action": "block",
				"description": "Block suspicious IP address"
			}
		]
	}`

	if err := makeAPICall("PUT", "/admin/api/v1/config/security/waf", emergencyWAFConfig); err != nil {
		logger.Error("Failed to apply emergency WAF rules", "error", err)
	} else {
		logger.Info("Emergency WAF rules applied successfully")
	}
}
