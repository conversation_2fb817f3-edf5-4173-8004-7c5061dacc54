package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"api-gateway/internal/core"
	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
)

func main() {
	// Load configuration
	cfg, err := loadConfig()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize logger
	logger, err := telemetry.NewLogger(cfg.Logging)
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}

	// Initialize metrics
	metrics, err := telemetry.NewMetrics(cfg.Metrics)
	if err != nil {
		log.Fatalf("Failed to initialize metrics: %v", err)
	}

	// Initialize tracer
	tracer, err := telemetry.NewTracer(cfg.Tracing)
	if err != nil {
		log.Fatalf("Failed to initialize tracer: %v", err)
	}

	// Create gateway instance
	gateway, err := core.NewGateway(cfg, logger, metrics, tracer)
	if err != nil {
		log.Fatalf("Failed to create gateway: %v", err)
	}

	// Setup HTTP server
	server := &http.Server{
		Addr:         cfg.Server.Address,
		Handler:      gateway.Handler(),
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}

	// Start server in a goroutine
	go func() {
		logger.Info("Starting API Gateway", 
			"address", cfg.Server.Address,
			"version", "1.0.0")

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Error("Server failed to start", "error", err)
			os.Exit(1)
		}
	}()

	// Setup graceful shutdown
	setupGracefulShutdown(server, gateway, logger)
}

// loadConfig loads the gateway configuration
func loadConfig() (*config.Config, error) {
	// Try to load from file first
	configFile := os.Getenv("GATEWAY_CONFIG_FILE")
	if configFile == "" {
		configFile = "gateway.yaml"
	}

	cfg, err := config.LoadFromFile(configFile)
	if err != nil {
		// If file doesn't exist, use default configuration
		if os.IsNotExist(err) {
			cfg = getDefaultConfig()
		} else {
			return nil, err
		}
	}

	// Override with environment variables
	if err := config.LoadFromEnv(cfg); err != nil {
		return nil, err
	}

	return cfg, nil
}

// getDefaultConfig returns a default configuration for the simple gateway
func getDefaultConfig() *config.Config {
	return &config.Config{
		Server: config.ServerConfig{
			Address:      ":8080",
			ReadTimeout:  30,
			WriteTimeout: 30,
			IdleTimeout:  120,
		},
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "console",
			Output: "stdout",
		},
		Metrics: config.MetricsConfig{
			Enabled: true,
			Port:    9090,
			Path:    "/metrics",
		},
		Tracing: config.TracingConfig{
			Enabled:     false,
			ServiceName: "api-gateway",
			SampleRate:  0.1,
		},
		Auth: config.AuthConfig{
			JWT: config.JWTConfig{
				Enabled:   false, // Disabled for simple example
				Algorithm: "HS256",
			},
		},
		Security: config.SecurityConfig{
			RateLimit: config.RateLimitConfig{
				Enabled:   true,
				Algorithm: "token_bucket",
				Rules: []config.RateLimitRule{
					{
						Path:   "/api/*",
						Rate:   100,
						Burst:  200,
						Window: "1m",
					},
				},
			},
			CORS: config.CORSConfig{
				Enabled:          true,
				AllowedOrigins:   []string{"*"},
				AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
				AllowedHeaders:   []string{"Content-Type", "Authorization"},
				AllowCredentials: false,
				MaxAge:           86400,
			},
		},
		Routes: []config.RouteConfig{
			{
				Name:    "health",
				Path:    "/health",
				Methods: []string{"GET"},
				Upstream: config.UpstreamConfig{
					Type: "static",
					Servers: []config.ServerTarget{
						{
							Host: "localhost",
							Port: 8080,
						},
					},
				},
			},
			{
				Name:    "echo",
				Path:    "/api/echo/*",
				Methods: []string{"GET", "POST"},
				Upstream: config.UpstreamConfig{
					Type: "static",
					Servers: []config.ServerTarget{
						{
							Host: "httpbin.org",
							Port: 80,
						},
					},
				},
				Options: &config.RouteOptions{
					Timeout: 30,
					Retries: 3,
				},
			},
			{
				Name:    "admin",
				Path:    "/admin/*",
				Methods: []string{"GET", "POST"},
				Upstream: config.UpstreamConfig{
					Type: "static",
					Servers: []config.ServerTarget{
						{
							Host: "localhost",
							Port: 8080,
						},
					},
				},
			},
		},
		Discovery: config.DiscoveryConfig{
			Type: "", // No service discovery for simple example
		},
		Plugins: config.PluginConfig{
			Directory: "plugins",
			Plugins:   make(map[string]interface{}),
		},
	}
}

// setupGracefulShutdown sets up graceful shutdown handling
func setupGracefulShutdown(server *http.Server, gateway *core.Gateway, logger *telemetry.Logger) {
	// Create a channel to receive OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for signal
	sig := <-sigChan
	logger.Info("Received shutdown signal", "signal", sig)

	// Create a context with timeout for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown HTTP server
	logger.Info("Shutting down HTTP server...")
	if err := server.Shutdown(ctx); err != nil {
		logger.Error("HTTP server shutdown error", "error", err)
	}

	// Shutdown gateway components
	logger.Info("Shutting down gateway components...")
	if err := gateway.Shutdown(ctx); err != nil {
		logger.Error("Gateway shutdown error", "error", err)
	}

	logger.Info("Gateway shutdown completed")
}

// Example middleware for demonstration
func exampleMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add custom header
		c.Header("X-Gateway-Version", "1.0.0")
		
		// Log request
		fmt.Printf("Request: %s %s from %s\n", 
			c.Request.Method, 
			c.Request.URL.Path, 
			c.ClientIP())
		
		// Continue processing
		c.Next()
		
		// Log response
		fmt.Printf("Response: %d for %s %s\n", 
			c.Writer.Status(), 
			c.Request.Method, 
			c.Request.URL.Path)
	}
}

// Example custom handler
func customHandler(c *gin.Context) {
	response := map[string]interface{}{
		"message":   "Hello from API Gateway!",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
		"path":      c.Request.URL.Path,
		"method":    c.Request.Method,
		"headers":   c.Request.Header,
	}
	
	c.JSON(http.StatusOK, response)
}

// Health check handler
func healthHandler(c *gin.Context) {
	health := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
		"version":   "1.0.0",
		"uptime":    "unknown", // Would be calculated in real implementation
	}
	
	c.JSON(http.StatusOK, health)
}

// Example of how to add custom routes
func addCustomRoutes(router *gin.Engine) {
	// Add custom middleware
	router.Use(exampleMiddleware())
	
	// Add custom routes
	api := router.Group("/api/v1")
	{
		api.GET("/hello", customHandler)
		api.GET("/health", healthHandler)
		
		// Echo endpoint for testing
		api.Any("/echo", func(c *gin.Context) {
			response := map[string]interface{}{
				"method":  c.Request.Method,
				"path":    c.Request.URL.Path,
				"query":   c.Request.URL.RawQuery,
				"headers": c.Request.Header,
				"body":    "Request body would be here",
			}
			c.JSON(http.StatusOK, response)
		})
	}
	
	// Admin endpoints
	admin := router.Group("/admin")
	{
		admin.GET("/status", func(c *gin.Context) {
			c.JSON(http.StatusOK, map[string]string{
				"status": "running",
				"version": "1.0.0",
			})
		})
		
		admin.GET("/config", func(c *gin.Context) {
			c.JSON(http.StatusOK, map[string]string{
				"message": "Configuration endpoint",
			})
		})
	}
}
