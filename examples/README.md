# API Gateway Examples

This directory contains comprehensive examples demonstrating various features and use cases of the API Gateway.

## Structure

```
examples/
├── README.md                    # This file
├── basic/                       # Basic usage examples
│   ├── simple-gateway.go        # Simple gateway setup
│   ├── basic-config.yaml        # Basic configuration
│   └── docker-compose.yml       # Docker setup
├── advanced/                    # Advanced features
│   ├── custom-middleware.go     # Custom middleware example
│   ├── plugin-development.go    # Plugin development
│   ├── rate-limiting.go         # Rate limiting setup
│   └── authentication.go       # Authentication examples
├── integrations/                # Integration examples
│   ├── consul-discovery.go      # Consul service discovery
│   ├── redis-cache.go           # Redis caching
│   ├── prometheus-metrics.go    # Prometheus integration
│   └── jaeger-tracing.go        # Jaeger tracing
├── deployment/                  # Deployment examples
│   ├── kubernetes/              # Kubernetes manifests
│   ├── docker/                  # Docker configurations
│   └── terraform/               # Infrastructure as code
└── testing/                     # Testing examples
    ├── unit-tests.go            # Unit testing
    ├── integration-tests.go     # Integration testing
    └── load-tests.go            # Load testing
```

## Quick Start

### 1. Basic Gateway

The simplest way to get started:

```bash
cd examples/basic
go run simple-gateway.go
```

This starts a basic gateway on port 8080 with:
- Health check endpoint at `/health`
- Echo service at `/api/echo`
- Admin API at `/admin`

### 2. Docker Setup

Run with Docker Compose:

```bash
cd examples/basic
docker-compose up -d
```

This starts:
- API Gateway (port 8080)
- Consul (port 8500)
- Redis (port 6379)
- Prometheus (port 9090)
- Grafana (port 3000)

### 3. Kubernetes Deployment

Deploy to Kubernetes:

```bash
cd examples/deployment/kubernetes
kubectl apply -f .
```

## Examples Overview

### Basic Examples

#### Simple Gateway (`basic/simple-gateway.go`)
- Minimal gateway setup
- Basic routing
- Health checks
- Logging configuration

#### Basic Configuration (`basic/basic-config.yaml`)
- Server settings
- Route definitions
- Security configuration
- Logging setup

### Advanced Examples

#### Custom Middleware (`advanced/custom-middleware.go`)
- Creating custom middleware
- Middleware chaining
- Request/response modification
- Error handling

#### Plugin Development (`advanced/plugin-development.go`)
- Plugin architecture
- Plugin lifecycle
- Configuration management
- Hot reloading

#### Rate Limiting (`advanced/rate-limiting.go`)
- Token bucket algorithm
- Per-client rate limiting
- Dynamic rate limits
- Rate limit headers

#### Authentication (`advanced/authentication.go`)
- JWT authentication
- API key validation
- OAuth integration
- Multi-factor authentication

### Integration Examples

#### Service Discovery (`integrations/consul-discovery.go`)
- Consul integration
- Service registration
- Health checking
- Load balancing

#### Caching (`integrations/redis-cache.go`)
- Redis integration
- Response caching
- Cache invalidation
- Cache warming

#### Metrics (`integrations/prometheus-metrics.go`)
- Prometheus integration
- Custom metrics
- Grafana dashboards
- Alerting rules

#### Tracing (`integrations/jaeger-tracing.go`)
- Distributed tracing
- Span creation
- Context propagation
- Performance monitoring

### Deployment Examples

#### Kubernetes (`deployment/kubernetes/`)
- Deployment manifests
- Service definitions
- ConfigMaps and Secrets
- Ingress configuration

#### Docker (`deployment/docker/`)
- Multi-stage builds
- Production images
- Health checks
- Resource limits

#### Terraform (`deployment/terraform/`)
- Infrastructure provisioning
- AWS/GCP/Azure examples
- Load balancer setup
- Monitoring configuration

### Testing Examples

#### Unit Tests (`testing/unit-tests.go`)
- Handler testing
- Middleware testing
- Mock dependencies
- Test utilities

#### Integration Tests (`testing/integration-tests.go`)
- End-to-end testing
- Database integration
- External service mocking
- Test containers

#### Load Tests (`testing/load-tests.go`)
- Performance testing
- Stress testing
- Capacity planning
- Bottleneck identification

## Configuration Examples

### Development Configuration

```yaml
server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30

logging:
  level: "debug"
  format: "console"

auth:
  jwt:
    enabled: true
    secret: "dev-secret"

security:
  rate_limit:
    enabled: true
    rules:
      - path: "/api/*"
        rate: 100
        burst: 200

routes:
  - name: "echo"
    path: "/api/echo"
    upstream:
      type: "static"
      servers:
        - host: "localhost"
          port: 8081
```

### Production Configuration

```yaml
server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30
  tls:
    enabled: true
    cert_file: "/etc/ssl/certs/gateway.crt"
    key_file: "/etc/ssl/private/gateway.key"

logging:
  level: "info"
  format: "json"
  output: "/var/log/gateway/gateway.log"

auth:
  jwt:
    enabled: true
    secret: "${JWT_SECRET}"
    algorithm: "RS256"
    public_key_file: "/etc/ssl/certs/jwt-public.pem"

security:
  rate_limit:
    enabled: true
    algorithm: "token_bucket"
    rules:
      - path: "/api/*"
        rate: 1000
        burst: 2000
  waf:
    enabled: true
    rules_file: "/etc/gateway/waf-rules.yaml"

discovery:
  type: "consul"
  consul:
    address: "consul.service.consul:8500"
    token: "${CONSUL_TOKEN}"

metrics:
  enabled: true
  port: 9090

tracing:
  enabled: true
  endpoint: "http://jaeger-collector:14268/api/traces"
```

## Running Examples

### Prerequisites

- Go 1.21+
- Docker and Docker Compose
- kubectl (for Kubernetes examples)

### Environment Setup

1. Clone the repository
2. Navigate to the examples directory
3. Copy environment template:
   ```bash
   cp .env.example .env
   ```
4. Edit `.env` with your configuration

### Running Individual Examples

Each example can be run independently:

```bash
# Basic gateway
cd basic && go run simple-gateway.go

# Custom middleware
cd advanced && go run custom-middleware.go

# Consul integration
cd integrations && go run consul-discovery.go
```

### Running with Docker

Use Docker Compose for complete setups:

```bash
# Basic setup
cd basic && docker-compose up

# Advanced setup with all services
cd advanced && docker-compose up

# Production-like setup
cd deployment/docker && docker-compose -f docker-compose.prod.yml up
```

## Best Practices Demonstrated

1. **Configuration Management**: Environment-based configuration
2. **Security**: Authentication, authorization, and input validation
3. **Observability**: Logging, metrics, and tracing
4. **Resilience**: Circuit breakers, retries, and timeouts
5. **Performance**: Caching, connection pooling, and optimization
6. **Testing**: Comprehensive testing strategies
7. **Deployment**: Production-ready deployment patterns

## Contributing

To add new examples:

1. Create a new directory under the appropriate category
2. Include a README.md explaining the example
3. Provide complete, runnable code
4. Add configuration files and documentation
5. Include tests where applicable

## Support

For questions about examples:
- Check the main documentation
- Review existing examples
- Open an issue on GitHub
- Join our community discussions
