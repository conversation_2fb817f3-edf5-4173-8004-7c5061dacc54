package examples

import (
	"time"

	"api-gateway/internal/handlers"
	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
)

// EchoRequest represents an echo request
type EchoRequest struct {
	Message   string            `json:"message" validate:"required,min=1,max=1000"`
	Metadata  map[string]string `json:"metadata,omitempty"`
	Timestamp *time.Time        `json:"timestamp,omitempty"`
	Echo      bool              `json:"echo" validate:"omitempty"`
}

// EchoResponse represents an echo response
type EchoResponse struct {
	OriginalMessage string            `json:"original_message"`
	EchoedMessage   string            `json:"echoed_message"`
	Metadata        map[string]string `json:"metadata,omitempty"`
	RequestInfo     *RequestInfo      `json:"request_info"`
	ProcessedAt     time.Time         `json:"processed_at"`
}

// RequestInfo contains information about the request
type RequestInfo struct {
	Method    string            `json:"method"`
	Path      string            `json:"path"`
	Headers   map[string]string `json:"headers,omitempty"`
	ClientIP  string            `json:"client_ip"`
	UserAgent string            `json:"user_agent"`
	RequestID string            `json:"request_id"`
	UserID    string            `json:"user_id,omitempty"`
}

// EchoHandler handles echo requests for testing and demonstration
type EchoHandler struct {
	*handlers.BaseHandler
}

// NewEchoHandler creates a new echo handler
func NewEchoHandler(logger *telemetry.Logger) *EchoHandler {
	return &EchoHandler{
		BaseHandler: handlers.NewBaseHandler(logger),
	}
}

// Name returns the handler name
func (h *EchoHandler) Name() string {
	return "echo"
}

// Path returns the handler path
func (h *EchoHandler) Path() string {
	return "/api/v1/echo"
}

// Method returns the handler HTTP method
func (h *EchoHandler) Method() string {
	return "POST"
}

// Handle handles the echo request
func (h *EchoHandler) Handle(c *gin.Context) {
	var req EchoRequest
	if err := h.ValidateAndBind(c, &req); err != nil {
		return
	}

	h.LogRequest(c, "Echo request received", "message_length", len(req.Message))

	// Build request info
	requestInfo := &RequestInfo{
		Method:    c.Request.Method,
		Path:      c.Request.URL.Path,
		ClientIP:  h.GetClientIP(c),
		UserAgent: c.Request.UserAgent(),
		RequestID: h.GetRequestID(c),
		UserID:    h.GetUserID(c),
		Headers:   make(map[string]string),
	}

	// Add selected headers
	importantHeaders := []string{"Content-Type", "Authorization", "X-Forwarded-For", "X-Real-IP"}
	for _, header := range importantHeaders {
		if value := c.GetHeader(header); value != "" {
			requestInfo.Headers[header] = value
		}
	}

	// Process the message
	echoedMessage := req.Message
	if req.Echo {
		echoedMessage = "Echo: " + req.Message
	}

	// Build response
	response := &EchoResponse{
		OriginalMessage: req.Message,
		EchoedMessage:   echoedMessage,
		Metadata:        req.Metadata,
		RequestInfo:     requestInfo,
		ProcessedAt:     time.Now().UTC(),
	}

	h.SuccessResponseWithMessage(c, response, "Message echoed successfully")
}

// SimpleEchoHandler handles simple echo requests (GET)
type SimpleEchoHandler struct {
	*handlers.BaseHandler
}

// NewSimpleEchoHandler creates a new simple echo handler
func NewSimpleEchoHandler(logger *telemetry.Logger) *SimpleEchoHandler {
	return &SimpleEchoHandler{
		BaseHandler: handlers.NewBaseHandler(logger),
	}
}

// Name returns the handler name
func (h *SimpleEchoHandler) Name() string {
	return "simple_echo"
}

// Path returns the handler path
func (h *SimpleEchoHandler) Path() string {
	return "/api/v1/echo/:message"
}

// Method returns the handler HTTP method
func (h *SimpleEchoHandler) Method() string {
	return "GET"
}

// Handle handles the simple echo request
func (h *SimpleEchoHandler) Handle(c *gin.Context) {
	message := c.Param("message")
	if message == "" {
		h.BadRequestResponse(c, "Message parameter is required")
		return
	}

	h.LogRequest(c, "Simple echo request received", "message", message)

	response := gin.H{
		"message":      message,
		"echoed_at":    time.Now().UTC().Format(time.RFC3339),
		"request_id":   h.GetRequestID(c),
		"client_ip":    h.GetClientIP(c),
		"method":       c.Request.Method,
		"path":         c.Request.URL.Path,
	}

	h.SuccessResponse(c, response)
}

// DelayedEchoHandler handles echo requests with artificial delay
type DelayedEchoHandler struct {
	*handlers.BaseHandler
}

// DelayedEchoRequest represents a delayed echo request
type DelayedEchoRequest struct {
	Message string `json:"message" validate:"required,min=1,max=1000"`
	Delay   int    `json:"delay" validate:"min=0,max=30"` // Max 30 seconds delay
}

// NewDelayedEchoHandler creates a new delayed echo handler
func NewDelayedEchoHandler(logger *telemetry.Logger) *DelayedEchoHandler {
	return &DelayedEchoHandler{
		BaseHandler: handlers.NewBaseHandler(logger),
	}
}

// Name returns the handler name
func (h *DelayedEchoHandler) Name() string {
	return "delayed_echo"
}

// Path returns the handler path
func (h *DelayedEchoHandler) Path() string {
	return "/api/v1/echo/delayed"
}

// Method returns the handler HTTP method
func (h *DelayedEchoHandler) Method() string {
	return "POST"
}

// Handle handles the delayed echo request
func (h *DelayedEchoHandler) Handle(c *gin.Context) {
	var req DelayedEchoRequest
	if err := h.ValidateAndBind(c, &req); err != nil {
		return
	}

	h.LogRequest(c, "Delayed echo request received", 
		"message", req.Message, 
		"delay", req.Delay)

	// Check context before delay
	if !h.CheckContext(c) {
		return
	}

	// Apply delay if specified
	if req.Delay > 0 {
		delay := time.Duration(req.Delay) * time.Second
		
		// Create a timeout context
		ctx, cancel := h.WithTimeout(c, delay+5*time.Second)
		defer cancel()
		
		select {
		case <-time.After(delay):
			// Delay completed
		case <-ctx.Done():
			h.ErrorResponse(c, 408, "request_timeout", "Request timed out during delay")
			return
		}
	}

	// Check context again after delay
	if !h.CheckContext(c) {
		return
	}

	response := gin.H{
		"message":     req.Message,
		"delay":       req.Delay,
		"processed_at": time.Now().UTC().Format(time.RFC3339),
		"request_id":  h.GetRequestID(c),
	}

	h.SuccessResponseWithMessage(c, response, "Delayed echo completed")
}

// ErrorEchoHandler demonstrates error handling
type ErrorEchoHandler struct {
	*handlers.BaseHandler
}

// ErrorEchoRequest represents an error echo request
type ErrorEchoRequest struct {
	Message    string `json:"message" validate:"required"`
	ErrorType  string `json:"error_type" validate:"omitempty,oneof=validation unauthorized forbidden not_found conflict internal"`
	ShouldFail bool   `json:"should_fail"`
}

// NewErrorEchoHandler creates a new error echo handler
func NewErrorEchoHandler(logger *telemetry.Logger) *ErrorEchoHandler {
	return &ErrorEchoHandler{
		BaseHandler: handlers.NewBaseHandler(logger),
	}
}

// Name returns the handler name
func (h *ErrorEchoHandler) Name() string {
	return "error_echo"
}

// Path returns the handler path
func (h *ErrorEchoHandler) Path() string {
	return "/api/v1/echo/error"
}

// Method returns the handler HTTP method
func (h *ErrorEchoHandler) Method() string {
	return "POST"
}

// Handle handles the error echo request
func (h *ErrorEchoHandler) Handle(c *gin.Context) {
	var req ErrorEchoRequest
	if err := h.ValidateAndBind(c, &req); err != nil {
		return
	}

	h.LogRequest(c, "Error echo request received", 
		"should_fail", req.ShouldFail, 
		"error_type", req.ErrorType)

	// Simulate different types of errors
	if req.ShouldFail {
		switch req.ErrorType {
		case "unauthorized":
			h.UnauthorizedResponse(c, "Simulated unauthorized error")
		case "forbidden":
			h.ForbiddenResponse(c, "Simulated forbidden error")
		case "not_found":
			h.NotFoundResponse(c, "Simulated not found error")
		case "conflict":
			h.ConflictResponse(c, "Simulated conflict error")
		case "internal":
			h.InternalServerErrorResponse(c, "Simulated internal server error")
		default:
			h.BadRequestResponse(c, "Simulated validation error")
		}
		return
	}

	response := gin.H{
		"message":     req.Message,
		"error_type":  req.ErrorType,
		"should_fail": req.ShouldFail,
		"status":      "success",
		"processed_at": time.Now().UTC().Format(time.RFC3339),
	}

	h.SuccessResponse(c, response)
}
