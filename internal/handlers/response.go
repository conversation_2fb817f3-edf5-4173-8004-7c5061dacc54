package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

// StandardResponse represents the standard API response format
type StandardResponse struct {
	Success   bool        `json:"success"`
	Data      interface{} `json:"data,omitempty"`
	Message   string      `json:"message,omitempty"`
	Error     *ErrorInfo  `json:"error,omitempty"`
	RequestID string      `json:"request_id"`
	Timestamp string      `json:"timestamp"`
}

// ErrorInfo represents error information in the response
type ErrorInfo struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
}

// PaginationInfo represents pagination information
type PaginationInfo struct {
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}

// PaginatedResponse represents a paginated response
type PaginatedResponse struct {
	Data       interface{}     `json:"data"`
	Pagination *PaginationInfo `json:"pagination"`
}

// SuccessResponse sends a successful response
func (h *BaseHandler) SuccessResponse(c *gin.Context, data interface{}) {
	response := StandardResponse{
		Success:   true,
		Data:      data,
		RequestID: h.GetRequestID(c),
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
	c.JSON(http.StatusOK, response)
}

// SuccessResponseWithMessage sends a successful response with a message
func (h *BaseHandler) SuccessResponseWithMessage(c *gin.Context, data interface{}, message string) {
	response := StandardResponse{
		Success:   true,
		Data:      data,
		Message:   message,
		RequestID: h.GetRequestID(c),
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
	c.JSON(http.StatusOK, response)
}

// CreatedResponse sends a 201 Created response
func (h *BaseHandler) CreatedResponse(c *gin.Context, data interface{}) {
	response := StandardResponse{
		Success:   true,
		Data:      data,
		Message:   "Resource created successfully",
		RequestID: h.GetRequestID(c),
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
	c.JSON(http.StatusCreated, response)
}

// NoContentResponse sends a 204 No Content response
func (h *BaseHandler) NoContentResponse(c *gin.Context) {
	c.Status(http.StatusNoContent)
}

// ErrorResponse sends an error response
func (h *BaseHandler) ErrorResponse(c *gin.Context, statusCode int, errorCode, message string) {
	response := StandardResponse{
		Success: false,
		Error: &ErrorInfo{
			Code:    errorCode,
			Message: message,
		},
		RequestID: h.GetRequestID(c),
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
	c.JSON(statusCode, response)
}

// ErrorResponseWithDetails sends an error response with details
func (h *BaseHandler) ErrorResponseWithDetails(c *gin.Context, statusCode int, errorCode, message string, details interface{}) {
	response := StandardResponse{
		Success: false,
		Error: &ErrorInfo{
			Code:    errorCode,
			Message: message,
			Details: details,
		},
		RequestID: h.GetRequestID(c),
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
	c.JSON(statusCode, response)
}

// ValidationErrorResponse sends a validation error response
func (h *BaseHandler) ValidationErrorResponse(c *gin.Context, err error) {
	var details interface{}
	
	// Handle validator errors
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		details = h.formatValidationErrors(validationErrors)
	} else {
		details = err.Error()
	}

	h.ErrorResponseWithDetails(c, http.StatusBadRequest, "validation_error", "Request validation failed", details)
}

// BadRequestResponse sends a 400 Bad Request response
func (h *BaseHandler) BadRequestResponse(c *gin.Context, message string) {
	h.ErrorResponse(c, http.StatusBadRequest, "bad_request", message)
}

// UnauthorizedResponse sends a 401 Unauthorized response
func (h *BaseHandler) UnauthorizedResponse(c *gin.Context, message string) {
	h.ErrorResponse(c, http.StatusUnauthorized, "unauthorized", message)
}

// ForbiddenResponse sends a 403 Forbidden response
func (h *BaseHandler) ForbiddenResponse(c *gin.Context, message string) {
	h.ErrorResponse(c, http.StatusForbidden, "forbidden", message)
}

// NotFoundResponse sends a 404 Not Found response
func (h *BaseHandler) NotFoundResponse(c *gin.Context, message string) {
	h.ErrorResponse(c, http.StatusNotFound, "not_found", message)
}

// ConflictResponse sends a 409 Conflict response
func (h *BaseHandler) ConflictResponse(c *gin.Context, message string) {
	h.ErrorResponse(c, http.StatusConflict, "conflict", message)
}

// InternalServerErrorResponse sends a 500 Internal Server Error response
func (h *BaseHandler) InternalServerErrorResponse(c *gin.Context, message string) {
	h.ErrorResponse(c, http.StatusInternalServerError, "internal_server_error", message)
}

// ServiceUnavailableResponse sends a 503 Service Unavailable response
func (h *BaseHandler) ServiceUnavailableResponse(c *gin.Context, message string) {
	h.ErrorResponse(c, http.StatusServiceUnavailable, "service_unavailable", message)
}

// PaginatedSuccessResponse sends a paginated success response
func (h *BaseHandler) PaginatedSuccessResponse(c *gin.Context, data interface{}, pagination *PaginationInfo) {
	response := StandardResponse{
		Success: true,
		Data: &PaginatedResponse{
			Data:       data,
			Pagination: pagination,
		},
		RequestID: h.GetRequestID(c),
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
	c.JSON(http.StatusOK, response)
}

// formatValidationErrors formats validator.ValidationErrors into a readable format
func (h *BaseHandler) formatValidationErrors(errs validator.ValidationErrors) map[string]string {
	errors := make(map[string]string)
	
	for _, err := range errs {
		field := err.Field()
		tag := err.Tag()
		
		switch tag {
		case "required":
			errors[field] = "This field is required"
		case "email":
			errors[field] = "Must be a valid email address"
		case "min":
			errors[field] = "Value is too short (minimum: " + err.Param() + ")"
		case "max":
			errors[field] = "Value is too long (maximum: " + err.Param() + ")"
		case "len":
			errors[field] = "Value must be exactly " + err.Param() + " characters"
		case "numeric":
			errors[field] = "Must be a number"
		case "alpha":
			errors[field] = "Must contain only letters"
		case "alphanum":
			errors[field] = "Must contain only letters and numbers"
		case "url":
			errors[field] = "Must be a valid URL"
		case "uuid":
			errors[field] = "Must be a valid UUID"
		default:
			errors[field] = "Invalid value"
		}
	}
	
	return errors
}

// HealthResponse sends a health check response
func (h *BaseHandler) HealthResponse(c *gin.Context, status string, components map[string]interface{}) {
	data := gin.H{
		"status":     status,
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}
	
	if components != nil {
		data["components"] = components
	}
	
	statusCode := http.StatusOK
	if status != "healthy" {
		statusCode = http.StatusServiceUnavailable
	}
	
	response := StandardResponse{
		Success:   status == "healthy",
		Data:      data,
		RequestID: h.GetRequestID(c),
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
	
	c.JSON(statusCode, response)
}
