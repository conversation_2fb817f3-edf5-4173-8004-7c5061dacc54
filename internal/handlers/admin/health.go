package admin

import (
	"fmt"
	"net/http"
	"runtime"
	"time"

	"api-gateway/internal/handlers"
	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
)

// HealthHandler handles health check requests
type HealthHandler struct {
	*handlers.BaseHandler
	startTime time.Time
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(logger *telemetry.Logger) *HealthHandler {
	return &HealthHandler{
		BaseHandler: handlers.NewBaseHandler(logger),
		startTime:   time.Now(),
	}
}

// Name returns the handler name
func (h *HealthHandler) Name() string {
	return "health"
}

// Path returns the handler path
func (h *HealthHandler) Path() string {
	return "/health"
}

// Method returns the handler HTTP method
func (h *HealthHandler) Method() string {
	return "GET"
}

// <PERSON>le handles the health check request
func (h *HealthHandler) Handle(c *gin.Context) {
	var req handlers.HealthCheckRequest
	if err := h.ValidateAndBindQuery(c, &req); err != nil {
		return
	}

	h.LogRequest(c, "Health check requested", "component", req.Component, "deep", req.Deep)

	// Basic health status
	status := "healthy"
	components := make(map[string]interface{})

	// System information
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	systemInfo := gin.H{
		"uptime":     time.Since(h.startTime).String(),
		"goroutines": runtime.NumGoroutine(),
		"memory": gin.H{
			"allocated":       formatBytes(memStats.Alloc),
			"total_allocated": formatBytes(memStats.TotalAlloc),
			"system":          formatBytes(memStats.Sys),
			"gc_cycles":       memStats.NumGC,
		},
	}

	// If specific component requested
	if req.Component != "" {
		componentStatus := h.checkComponentHealth(req.Component, req.Deep)
		components[req.Component] = componentStatus
		if componentStatus == "unhealthy" {
			status = "unhealthy"
		}
	} else {
		// Check all components
		allComponents := []string{"auth", "security", "discovery", "proxy", "plugins", "metrics", "storage"}
		for _, component := range allComponents {
			componentStatus := h.checkComponentHealth(component, req.Deep)
			components[component] = componentStatus
			if componentStatus == "unhealthy" {
				status = "degraded"
			}
		}
	}

	// Add system info to components
	components["system"] = systemInfo

	h.HealthResponse(c, status, components)
}

// checkComponentHealth checks the health of a specific component
func (h *HealthHandler) checkComponentHealth(component string, deep bool) string {
	// This would typically check actual component health
	// For now, we'll simulate basic health checks
	
	switch component {
	case "auth":
		return "healthy"
	case "security":
		return "healthy"
	case "discovery":
		// This would check service discovery connectivity
		return "healthy"
	case "proxy":
		return "healthy"
	case "plugins":
		return "healthy"
	case "metrics":
		return "healthy"
	case "storage":
		// This would check storage connectivity
		if deep {
			// Perform deep health check (e.g., database ping)
			return "healthy"
		}
		return "healthy"
	default:
		return "unknown"
	}
}

// LivenessHandler handles Kubernetes liveness probe
type LivenessHandler struct {
	*handlers.BaseHandler
}

// NewLivenessHandler creates a new liveness handler
func NewLivenessHandler(logger *telemetry.Logger) *LivenessHandler {
	return &LivenessHandler{
		BaseHandler: handlers.NewBaseHandler(logger),
	}
}

// Name returns the handler name
func (h *LivenessHandler) Name() string {
	return "liveness"
}

// Path returns the handler path
func (h *LivenessHandler) Path() string {
	return "/health/live"
}

// Method returns the handler HTTP method
func (h *LivenessHandler) Method() string {
	return "GET"
}

// Handle handles the liveness probe request
func (h *LivenessHandler) Handle(c *gin.Context) {
	// Simple liveness check - if we can respond, we're alive
	c.JSON(http.StatusOK, gin.H{
		"status":    "alive",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	})
}

// ReadinessHandler handles Kubernetes readiness probe
type ReadinessHandler struct {
	*handlers.BaseHandler
}

// NewReadinessHandler creates a new readiness handler
func NewReadinessHandler(logger *telemetry.Logger) *ReadinessHandler {
	return &ReadinessHandler{
		BaseHandler: handlers.NewBaseHandler(logger),
	}
}

// Name returns the handler name
func (h *ReadinessHandler) Name() string {
	return "readiness"
}

// Path returns the handler path
func (h *ReadinessHandler) Path() string {
	return "/health/ready"
}

// Method returns the handler HTTP method
func (h *ReadinessHandler) Method() string {
	return "GET"
}

// Handle handles the readiness probe request
func (h *ReadinessHandler) Handle(c *gin.Context) {
	// Check if the application is ready to serve traffic
	// This would typically check dependencies like database, cache, etc.
	
	ready := true
	components := gin.H{}
	
	// Check critical dependencies
	dependencies := []string{"storage", "discovery"}
	for _, dep := range dependencies {
		// Simulate dependency check
		if h.checkDependency(dep) {
			components[dep] = "ready"
		} else {
			components[dep] = "not_ready"
			ready = false
		}
	}
	
	status := "ready"
	statusCode := http.StatusOK
	
	if !ready {
		status = "not_ready"
		statusCode = http.StatusServiceUnavailable
	}
	
	c.JSON(statusCode, gin.H{
		"status":     status,
		"components": components,
		"timestamp":  time.Now().UTC().Format(time.RFC3339),
	})
}

// checkDependency checks if a dependency is ready
func (h *ReadinessHandler) checkDependency(dependency string) bool {
	// This would implement actual dependency checks
	// For now, we'll simulate that all dependencies are ready
	return true
}

// formatBytes formats bytes into human readable format
func formatBytes(bytes uint64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}

	div, exp := uint64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}

	units := []string{"KB", "MB", "GB", "TB", "PB"}
	return fmt.Sprintf("%.1f %s", float64(bytes)/float64(div), units[exp])
}

// StartupHandler handles startup probe
type StartupHandler struct {
	*handlers.BaseHandler
	isReady bool
}

// NewStartupHandler creates a new startup handler
func NewStartupHandler(logger *telemetry.Logger) *StartupHandler {
	return &StartupHandler{
		BaseHandler: handlers.NewBaseHandler(logger),
		isReady:     false,
	}
}

// Name returns the handler name
func (h *StartupHandler) Name() string {
	return "startup"
}

// Path returns the handler path
func (h *StartupHandler) Path() string {
	return "/health/startup"
}

// Method returns the handler HTTP method
func (h *StartupHandler) Method() string {
	return "GET"
}

// Handle handles the startup probe request
func (h *StartupHandler) Handle(c *gin.Context) {
	// Check if the application has finished starting up
	if h.isReady {
		c.JSON(http.StatusOK, gin.H{
			"status":    "started",
			"timestamp": time.Now().UTC().Format(time.RFC3339),
		})
	} else {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status":    "starting",
			"timestamp": time.Now().UTC().Format(time.RFC3339),
		})
	}
}

// SetReady marks the application as ready
func (h *StartupHandler) SetReady() {
	h.isReady = true
}
