# HTTP Handlers

This directory contains HTTP request handlers for the API Gateway.

## Architecture

The handlers follow a layered architecture:

1. **Handler Interface**: Defines the contract for all handlers
2. **Base Handler**: Provides common functionality like validation, response formatting
3. **Specific Handlers**: Implement business logic for different endpoints
4. **Middleware Integration**: Seamless integration with gateway middleware

## Structure

```
internal/handlers/
├── README.md           # This file
├── handler.go          # Handler interface and base implementation
├── response.go         # Response formatting utilities
├── validation.go       # Request validation utilities
├── admin/              # Admin API handlers
│   ├── config.go       # Configuration management
│   ├── health.go       # Health check handlers
│   ├── metrics.go      # Metrics handlers
│   └── routes.go       # Route management
├── auth/               # Authentication handlers
│   ├── login.go        # Login handler
│   ├── logout.go       # Logout handler
│   └── refresh.go      # Token refresh handler
└── examples/           # Example handlers for demonstration
    ├── echo.go         # Simple echo handler
    ├── transform.go    # Request/response transformation
    └── proxy.go        # Custom proxy handler
```

## Usage

### Creating a New Handler

```go
type MyHandler struct {
    *BaseHandler
    service MyService
}

func NewMyHandler(service MyService, logger *telemetry.Logger) *MyHandler {
    return &MyHandler{
        BaseHandler: NewBaseHandler(logger),
        service:     service,
    }
}

func (h *MyHandler) Handle(c *gin.Context) {
    // Validate request
    var req MyRequest
    if err := h.ValidateAndBind(c, &req); err != nil {
        return // Error response already sent
    }

    // Process request
    result, err := h.service.Process(req)
    if err != nil {
        h.ErrorResponse(c, http.StatusInternalServerError, "processing_error", err.Error())
        return
    }

    // Send success response
    h.SuccessResponse(c, result)
}
```

### Response Format

All handlers use a consistent response format:

```json
{
  "success": true,
  "data": {...},
  "message": "Operation completed successfully",
  "request_id": "req-123",
  "timestamp": "2023-01-01T00:00:00Z"
}
```

For errors:

```json
{
  "success": false,
  "error": {
    "code": "validation_error",
    "message": "Invalid request parameters",
    "details": {...}
  },
  "request_id": "req-123",
  "timestamp": "2023-01-01T00:00:00Z"
}
```

## Features

- **Consistent Response Format**: All handlers use standardized response structure
- **Request Validation**: Built-in validation with detailed error messages
- **Error Handling**: Centralized error handling with proper HTTP status codes
- **Logging Integration**: Automatic request/response logging
- **Metrics Collection**: Built-in metrics collection for all handlers
- **Context Propagation**: Proper context handling for tracing and cancellation
- **Rate Limiting**: Integration with gateway rate limiting
- **Authentication**: Seamless integration with auth middleware

## Best Practices

1. **Use BaseHandler**: Always extend BaseHandler for consistent behavior
2. **Validate Input**: Use ValidateAndBind for request validation
3. **Handle Errors**: Use appropriate error response methods
4. **Log Operations**: Log important operations and errors
5. **Return Early**: Return early on validation or processing errors
6. **Use Context**: Respect context cancellation and timeouts
