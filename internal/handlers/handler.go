package handlers

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

// <PERSON>ler interface defines the contract for all HTTP handlers
type Hand<PERSON> interface {
	Handle(c *gin.Context)
	Name() string
	Path() string
	Method() string
}

// BaseHandler provides common functionality for all handlers
type BaseHandler struct {
	logger    *telemetry.Logger
	validator *validator.Validate
}

// NewBaseHandler creates a new base handler
func NewBaseHandler(logger *telemetry.Logger) *BaseHandler {
	return &BaseHandler{
		logger:    logger,
		validator: validator.New(),
	}
}

// ValidateAndBind validates and binds request data
func (h *BaseHandler) ValidateAndBind(c *gin.Context, obj interface{}) error {
	// Bind request data
	if err := c.ShouldBindJSON(obj); err != nil {
		h.ValidationErrorResponse(c, err)
		return err
	}

	// Validate struct
	if err := h.validator.Struct(obj); err != nil {
		h.ValidationErrorResponse(c, err)
		return err
	}

	return nil
}

// ValidateAndBindQuery validates and binds query parameters
func (h *BaseHandler) ValidateAndBindQuery(c *gin.Context, obj interface{}) error {
	// Bind query parameters
	if err := c.ShouldBindQuery(obj); err != nil {
		h.ValidationErrorResponse(c, err)
		return err
	}

	// Validate struct
	if err := h.validator.Struct(obj); err != nil {
		h.ValidationErrorResponse(c, err)
		return err
	}

	return nil
}

// ValidateAndBindURI validates and binds URI parameters
func (h *BaseHandler) ValidateAndBindURI(c *gin.Context, obj interface{}) error {
	// Bind URI parameters
	if err := c.ShouldBindUri(obj); err != nil {
		h.ValidationErrorResponse(c, err)
		return err
	}

	// Validate struct
	if err := h.validator.Struct(obj); err != nil {
		h.ValidationErrorResponse(c, err)
		return err
	}

	return nil
}

// GetRequestID extracts request ID from context
func (h *BaseHandler) GetRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		return requestID.(string)
	}
	return ""
}

// GetUserID extracts user ID from context
func (h *BaseHandler) GetUserID(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		return userID.(string)
	}
	return ""
}

// GetUserRoles extracts user roles from context
func (h *BaseHandler) GetUserRoles(c *gin.Context) []string {
	if roles, exists := c.Get("user_roles"); exists {
		return roles.([]string)
	}
	return nil
}

// GetClientIP extracts client IP from context
func (h *BaseHandler) GetClientIP(c *gin.Context) string {
	if realIP, exists := c.Get("real_ip"); exists {
		return realIP.(string)
	}
	return c.ClientIP()
}

// LogRequest logs the incoming request
func (h *BaseHandler) LogRequest(c *gin.Context, message string, fields ...interface{}) {
	allFields := []interface{}{
		"request_id", h.GetRequestID(c),
		"method", c.Request.Method,
		"path", c.Request.URL.Path,
		"client_ip", h.GetClientIP(c),
		"user_id", h.GetUserID(c),
	}
	allFields = append(allFields, fields...)
	h.logger.Info(message, allFields...)
}

// LogError logs an error with request context
func (h *BaseHandler) LogError(c *gin.Context, message string, err error, fields ...interface{}) {
	allFields := []interface{}{
		"request_id", h.GetRequestID(c),
		"method", c.Request.Method,
		"path", c.Request.URL.Path,
		"client_ip", h.GetClientIP(c),
		"user_id", h.GetUserID(c),
		"error", err,
	}
	allFields = append(allFields, fields...)
	h.logger.Error(message, allFields...)
}

// WithTimeout creates a context with timeout
func (h *BaseHandler) WithTimeout(c *gin.Context, timeout time.Duration) (context.Context, context.CancelFunc) {
	return context.WithTimeout(c.Request.Context(), timeout)
}

// CheckContext checks if context is cancelled or timed out
func (h *BaseHandler) CheckContext(c *gin.Context) bool {
	select {
	case <-c.Request.Context().Done():
		h.ErrorResponse(c, http.StatusRequestTimeout, "request_timeout", "Request was cancelled or timed out")
		return false
	default:
		return true
	}
}

// RequireAuth checks if user is authenticated
func (h *BaseHandler) RequireAuth(c *gin.Context) bool {
	userID := h.GetUserID(c)
	if userID == "" {
		h.ErrorResponse(c, http.StatusUnauthorized, "authentication_required", "Authentication is required")
		return false
	}
	return true
}

// RequireRole checks if user has required role
func (h *BaseHandler) RequireRole(c *gin.Context, requiredRole string) bool {
	if !h.RequireAuth(c) {
		return false
	}

	roles := h.GetUserRoles(c)
	for _, role := range roles {
		if role == requiredRole || role == "admin" {
			return true
		}
	}

	h.ErrorResponse(c, http.StatusForbidden, "insufficient_permissions", "Required role: "+requiredRole)
	return false
}

// RequireAnyRole checks if user has any of the required roles
func (h *BaseHandler) RequireAnyRole(c *gin.Context, requiredRoles []string) bool {
	if !h.RequireAuth(c) {
		return false
	}

	userRoles := h.GetUserRoles(c)
	for _, userRole := range userRoles {
		if userRole == "admin" {
			return true
		}
		for _, requiredRole := range requiredRoles {
			if userRole == requiredRole {
				return true
			}
		}
	}

	h.ErrorResponse(c, http.StatusForbidden, "insufficient_permissions", "Required roles: one of "+fmt.Sprintf("%v", requiredRoles))
	return false
}
