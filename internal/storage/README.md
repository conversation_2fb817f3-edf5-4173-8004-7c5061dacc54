# Storage Layer

This directory contains the storage layer abstraction for the API Gateway, providing unified interfaces for different storage backends.

## Architecture

The storage layer follows a plugin-based architecture with the following components:

1. **Storage Interface**: Defines the contract for all storage operations
2. **Backend Implementations**: Specific implementations for different storage systems
3. **Connection Management**: Connection pooling and lifecycle management
4. **Serialization**: Data serialization and deserialization
5. **Caching**: Multi-level caching strategies

## Structure

```
internal/storage/
├── README.md           # This file
├── storage.go          # Main storage interface and types
├── manager.go          # Storage manager for multiple backends
├── serializer.go       # Data serialization utilities
├── backends/           # Storage backend implementations
│   ├── memory.go       # In-memory storage
│   ├── redis.go        # Redis storage
│   ├── database.go     # Database storage (PostgreSQL, MySQL)
│   └── file.go         # File-based storage
├── cache/              # Caching implementations
│   ├── lru.go          # LRU cache implementation
│   ├── ttl.go          # TTL-based cache
│   └── multi_level.go  # Multi-level cache
└── examples/           # Example storage usage
    ├── session.go      # Session storage example
    ├── config.go       # Configuration storage example
    └── rate_limit.go   # Rate limiting storage example
```

## Features

- **Multiple Backends**: Support for Redis, databases, in-memory, and file storage
- **Unified Interface**: Consistent API across all storage backends
- **Connection Pooling**: Efficient connection management
- **Serialization**: Automatic serialization/deserialization
- **TTL Support**: Time-to-live for cached data
- **Transactions**: Transaction support where available
- **Health Monitoring**: Storage backend health checks
- **Metrics Integration**: Built-in metrics collection

## Storage Types

### Key-Value Storage
- Simple key-value operations
- TTL support
- Atomic operations
- Batch operations

### Document Storage
- JSON document storage
- Query capabilities
- Indexing support
- Schema validation

### Session Storage
- User session management
- Automatic expiration
- Session data encryption
- Distributed sessions

### Configuration Storage
- Dynamic configuration
- Configuration versioning
- Change notifications
- Rollback capabilities

## Usage

### Basic Operations

```go
// Create storage manager
manager := storage.NewManager(config, logger)

// Get a storage backend
store, err := manager.GetStorage("redis")
if err != nil {
    log.Fatal(err)
}

// Set a value
err = store.Set(ctx, "key", "value", 5*time.Minute)

// Get a value
value, err := store.Get(ctx, "key")

// Delete a value
err = store.Delete(ctx, "key")
```

### Session Management

```go
// Create session storage
sessionStore := storage.NewSessionStorage(store, logger)

// Create a session
session := &Session{
    UserID: "user123",
    Data:   map[string]interface{}{"role": "admin"},
}
sessionID, err := sessionStore.CreateSession(ctx, session, 24*time.Hour)

// Get session
session, err := sessionStore.GetSession(ctx, sessionID)

// Update session
err = sessionStore.UpdateSession(ctx, sessionID, session)

// Delete session
err = sessionStore.DeleteSession(ctx, sessionID)
```

### Configuration Storage

```go
// Create config storage
configStore := storage.NewConfigStorage(store, logger)

// Set configuration
config := map[string]interface{}{
    "rate_limit": 100,
    "timeout":    30,
}
err = configStore.SetConfig(ctx, "gateway", config)

// Get configuration
config, err := configStore.GetConfig(ctx, "gateway")

// Watch for changes
changes := configStore.WatchConfig(ctx, "gateway")
for change := range changes {
    log.Printf("Config changed: %+v", change)
}
```

## Configuration

```yaml
storage:
  default_backend: "redis"
  backends:
    redis:
      type: "redis"
      address: "localhost:6379"
      password: ""
      db: 0
      pool_size: 10
      max_retries: 3
    database:
      type: "postgres"
      dsn: "postgres://user:pass@localhost/db"
      max_connections: 20
      max_idle: 5
    memory:
      type: "memory"
      max_size: "100MB"
      cleanup_interval: "5m"
    file:
      type: "file"
      directory: "/var/lib/gateway/storage"
      sync_interval: "1s"
  
  cache:
    enabled: true
    type: "multi_level"
    levels:
      - type: "memory"
        size: "50MB"
        ttl: "5m"
      - type: "redis"
        ttl: "1h"
  
  serialization:
    format: "json"  # json, msgpack, protobuf
    compression: "gzip"
```

## Backend Comparison

| Backend  | Performance | Persistence | Scalability | Use Cases |
|----------|-------------|-------------|-------------|-----------|
| Memory   | Highest     | No          | Single Node | Cache, Temp Data |
| Redis    | High        | Optional    | Horizontal  | Cache, Sessions |
| Database | Medium      | Yes         | Vertical    | Config, Audit |
| File     | Low         | Yes         | Single Node | Backup, Archive |

## Best Practices

1. **Choose Right Backend**: Select backend based on use case requirements
2. **Use Caching**: Implement multi-level caching for better performance
3. **Handle Failures**: Implement proper error handling and fallbacks
4. **Monitor Health**: Regular health checks for storage backends
5. **Data Lifecycle**: Implement proper TTL and cleanup strategies
6. **Security**: Encrypt sensitive data at rest and in transit
7. **Backup**: Regular backups for persistent storage
8. **Connection Pooling**: Use connection pooling for better resource utilization
