package storage

import (
	"context"
	"errors"
	"time"
)

var (
	ErrKeyNotFound     = errors.New("key not found")
	ErrKeyExists       = errors.New("key already exists")
	ErrInvalidValue    = errors.New("invalid value")
	ErrStorageNotFound = errors.New("storage backend not found")
	ErrConnectionFailed = errors.New("connection failed")
	ErrOperationFailed = errors.New("operation failed")
)

// Storage defines the interface for storage operations
type Storage interface {
	// Basic operations
	Get(ctx context.Context, key string) ([]byte, error)
	Set(ctx context.Context, key string, value []byte, ttl time.Duration) error
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
	
	// Batch operations
	GetMulti(ctx context.Context, keys []string) (map[string][]byte, error)
	SetMulti(ctx context.Context, items map[string][]byte, ttl time.Duration) error
	DeleteMulti(ctx context.Context, keys []string) error
	
	// Advanced operations
	Increment(ctx context.Context, key string, delta int64) (int64, error)
	Decrement(ctx context.Context, key string, delta int64) (int64, error)
	SetNX(ctx context.Context, key string, value []byte, ttl time.Duration) (bool, error)
	GetSet(ctx context.Context, key string, value []byte) ([]byte, error)
	
	// List operations
	ListPush(ctx context.Context, key string, values ...[]byte) error
	ListPop(ctx context.Context, key string) ([]byte, error)
	ListLen(ctx context.Context, key string) (int64, error)
	ListRange(ctx context.Context, key string, start, stop int64) ([][]byte, error)
	
	// Hash operations
	HashGet(ctx context.Context, key, field string) ([]byte, error)
	HashSet(ctx context.Context, key, field string, value []byte) error
	HashDelete(ctx context.Context, key, field string) error
	HashGetAll(ctx context.Context, key string) (map[string][]byte, error)
	HashExists(ctx context.Context, key, field string) (bool, error)
	
	// Set operations
	SetAdd(ctx context.Context, key string, members ...[]byte) error
	SetRemove(ctx context.Context, key string, members ...[]byte) error
	SetMembers(ctx context.Context, key string) ([][]byte, error)
	SetIsMember(ctx context.Context, key string, member []byte) (bool, error)
	
	// Expiration operations
	Expire(ctx context.Context, key string, ttl time.Duration) error
	TTL(ctx context.Context, key string) (time.Duration, error)
	Persist(ctx context.Context, key string) error
	
	// Utility operations
	Keys(ctx context.Context, pattern string) ([]string, error)
	FlushAll(ctx context.Context) error
	Size(ctx context.Context) (int64, error)
	
	// Connection management
	Ping(ctx context.Context) error
	Close() error
	
	// Information
	Info() StorageInfo
}

// StorageInfo contains information about the storage backend
type StorageInfo struct {
	Type        string            `json:"type"`
	Version     string            `json:"version"`
	Status      string            `json:"status"`
	Connections int               `json:"connections"`
	Memory      int64             `json:"memory_usage"`
	Stats       map[string]int64  `json:"stats"`
	Config      map[string]string `json:"config"`
}

// TransactionalStorage defines the interface for transactional operations
type TransactionalStorage interface {
	Storage
	
	// Transaction operations
	BeginTx(ctx context.Context) (Transaction, error)
}

// Transaction defines the interface for transaction operations
type Transaction interface {
	// Basic operations within transaction
	Get(ctx context.Context, key string) ([]byte, error)
	Set(ctx context.Context, key string, value []byte, ttl time.Duration) error
	Delete(ctx context.Context, key string) error
	
	// Transaction control
	Commit(ctx context.Context) error
	Rollback(ctx context.Context) error
}

// WatchableStorage defines the interface for watching key changes
type WatchableStorage interface {
	Storage
	
	// Watch operations
	Watch(ctx context.Context, keys ...string) (<-chan WatchEvent, error)
	WatchPrefix(ctx context.Context, prefix string) (<-chan WatchEvent, error)
}

// WatchEvent represents a change event
type WatchEvent struct {
	Type     WatchEventType `json:"type"`
	Key      string         `json:"key"`
	Value    []byte         `json:"value,omitempty"`
	OldValue []byte         `json:"old_value,omitempty"`
}

// WatchEventType represents the type of watch event
type WatchEventType string

const (
	WatchEventSet    WatchEventType = "set"
	WatchEventDelete WatchEventType = "delete"
	WatchEventExpire WatchEventType = "expire"
)

// StorageConfig contains configuration for storage backends
type StorageConfig struct {
	Type     string                 `yaml:"type"`
	Address  string                 `yaml:"address"`
	Username string                 `yaml:"username"`
	Password string                 `yaml:"password"`
	Database int                    `yaml:"database"`
	Options  map[string]interface{} `yaml:"options"`
	
	// Connection settings
	MaxConnections  int           `yaml:"max_connections"`
	MaxIdle         int           `yaml:"max_idle"`
	ConnectTimeout  time.Duration `yaml:"connect_timeout"`
	ReadTimeout     time.Duration `yaml:"read_timeout"`
	WriteTimeout    time.Duration `yaml:"write_timeout"`
	IdleTimeout     time.Duration `yaml:"idle_timeout"`
	
	// Retry settings
	MaxRetries    int           `yaml:"max_retries"`
	RetryInterval time.Duration `yaml:"retry_interval"`
	
	// Health check settings
	HealthCheckInterval time.Duration `yaml:"health_check_interval"`
	HealthCheckTimeout  time.Duration `yaml:"health_check_timeout"`
}

// CacheConfig contains configuration for caching
type CacheConfig struct {
	Enabled    bool          `yaml:"enabled"`
	Type       string        `yaml:"type"`
	MaxSize    int64         `yaml:"max_size"`
	TTL        time.Duration `yaml:"ttl"`
	Levels     []CacheLevel  `yaml:"levels"`
	Serializer string        `yaml:"serializer"`
}

// CacheLevel represents a cache level configuration
type CacheLevel struct {
	Type    string        `yaml:"type"`
	Size    int64         `yaml:"size"`
	TTL     time.Duration `yaml:"ttl"`
	Options map[string]interface{} `yaml:"options"`
}

// SerializationConfig contains serialization configuration
type SerializationConfig struct {
	Format      string `yaml:"format"`      // json, msgpack, protobuf
	Compression string `yaml:"compression"` // none, gzip, lz4, snappy
}

// StorageMetrics contains metrics for storage operations
type StorageMetrics struct {
	Operations    map[string]int64 `json:"operations"`
	Errors        map[string]int64 `json:"errors"`
	ResponseTimes map[string]int64 `json:"response_times"` // in microseconds
	CacheHits     int64            `json:"cache_hits"`
	CacheMisses   int64            `json:"cache_misses"`
	Connections   int              `json:"connections"`
	Memory        int64            `json:"memory_usage"`
}

// HealthStatus represents the health status of a storage backend
type HealthStatus struct {
	Healthy   bool              `json:"healthy"`
	Status    string            `json:"status"`
	Message   string            `json:"message,omitempty"`
	Timestamp time.Time         `json:"timestamp"`
	Metrics   *StorageMetrics   `json:"metrics,omitempty"`
	Details   map[string]string `json:"details,omitempty"`
}

// StorageFactory defines the interface for creating storage instances
type StorageFactory interface {
	Create(config StorageConfig) (Storage, error)
	Type() string
	ValidateConfig(config StorageConfig) error
}

// Item represents a storage item with metadata
type Item struct {
	Key       string            `json:"key"`
	Value     []byte            `json:"value"`
	TTL       time.Duration     `json:"ttl"`
	CreatedAt time.Time         `json:"created_at"`
	UpdatedAt time.Time         `json:"updated_at"`
	Metadata  map[string]string `json:"metadata,omitempty"`
}

// Query represents a query for searching storage items
type Query struct {
	Pattern   string            `json:"pattern"`
	Prefix    string            `json:"prefix"`
	Limit     int               `json:"limit"`
	Offset    int               `json:"offset"`
	SortBy    string            `json:"sort_by"`
	SortOrder string            `json:"sort_order"` // asc, desc
	Filters   map[string]string `json:"filters"`
}

// QueryResult represents the result of a query
type QueryResult struct {
	Items      []*Item `json:"items"`
	Total      int64   `json:"total"`
	HasMore    bool    `json:"has_more"`
	NextOffset int     `json:"next_offset"`
}

// SearchableStorage defines the interface for searchable storage
type SearchableStorage interface {
	Storage
	
	// Search operations
	Search(ctx context.Context, query Query) (*QueryResult, error)
	Index(ctx context.Context, key string, fields map[string]interface{}) error
	RemoveIndex(ctx context.Context, key string) error
}

// BackupStorage defines the interface for backup operations
type BackupStorage interface {
	Storage
	
	// Backup operations
	Backup(ctx context.Context, path string) error
	Restore(ctx context.Context, path string) error
	ListBackups(ctx context.Context) ([]BackupInfo, error)
}

// BackupInfo contains information about a backup
type BackupInfo struct {
	Name      string    `json:"name"`
	Path      string    `json:"path"`
	Size      int64     `json:"size"`
	CreatedAt time.Time `json:"created_at"`
	Checksum  string    `json:"checksum"`
}
