package backends

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"api-gateway/internal/storage"
)

// MemoryStorage implements in-memory storage
type MemoryStorage struct {
	data      map[string]*memoryItem
	mu        sync.RWMutex
	config    storage.StorageConfig
	metrics   *storage.StorageMetrics
	
	// Cleanup
	stopCleanup chan struct{}
	wg          sync.WaitGroup
}

// memoryItem represents an item in memory storage
type memoryItem struct {
	value     []byte
	expiresAt time.Time
	createdAt time.Time
	updatedAt time.Time
}

// NewMemoryStorage creates a new memory storage instance
func NewMemoryStorage(config storage.StorageConfig) *MemoryStorage {
	ms := &MemoryStorage{
		data:        make(map[string]*memoryItem),
		config:      config,
		stopCleanup: make(chan struct{}),
		metrics: &storage.StorageMetrics{
			Operations:    make(map[string]int64),
			Errors:        make(map[string]int64),
			ResponseTimes: make(map[string]int64),
		},
	}
	
	// Start cleanup goroutine
	ms.startCleanup()
	
	return ms
}

// Get retrieves a value by key
func (ms *MemoryStorage) Get(ctx context.Context, key string) ([]byte, error) {
	start := time.Now()
	defer func() {
		ms.recordMetric("get", time.Since(start))
	}()
	
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	
	item, exists := ms.data[key]
	if !exists {
		ms.recordError("get")
		return nil, storage.ErrKeyNotFound
	}
	
	// Check expiration
	if !item.expiresAt.IsZero() && time.Now().After(item.expiresAt) {
		ms.recordError("get")
		return nil, storage.ErrKeyNotFound
	}
	
	return item.value, nil
}

// Set stores a value with optional TTL
func (ms *MemoryStorage) Set(ctx context.Context, key string, value []byte, ttl time.Duration) error {
	start := time.Now()
	defer func() {
		ms.recordMetric("set", time.Since(start))
	}()
	
	ms.mu.Lock()
	defer ms.mu.Unlock()
	
	now := time.Now()
	item := &memoryItem{
		value:     make([]byte, len(value)),
		createdAt: now,
		updatedAt: now,
	}
	copy(item.value, value)
	
	if ttl > 0 {
		item.expiresAt = now.Add(ttl)
	}
	
	ms.data[key] = item
	return nil
}

// Delete removes a key
func (ms *MemoryStorage) Delete(ctx context.Context, key string) error {
	start := time.Now()
	defer func() {
		ms.recordMetric("delete", time.Since(start))
	}()
	
	ms.mu.Lock()
	defer ms.mu.Unlock()
	
	if _, exists := ms.data[key]; !exists {
		ms.recordError("delete")
		return storage.ErrKeyNotFound
	}
	
	delete(ms.data, key)
	return nil
}

// Exists checks if a key exists
func (ms *MemoryStorage) Exists(ctx context.Context, key string) (bool, error) {
	start := time.Now()
	defer func() {
		ms.recordMetric("exists", time.Since(start))
	}()
	
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	
	item, exists := ms.data[key]
	if !exists {
		return false, nil
	}
	
	// Check expiration
	if !item.expiresAt.IsZero() && time.Now().After(item.expiresAt) {
		return false, nil
	}
	
	return true, nil
}

// GetMulti retrieves multiple values
func (ms *MemoryStorage) GetMulti(ctx context.Context, keys []string) (map[string][]byte, error) {
	result := make(map[string][]byte)
	
	for _, key := range keys {
		if value, err := ms.Get(ctx, key); err == nil {
			result[key] = value
		}
	}
	
	return result, nil
}

// SetMulti stores multiple values
func (ms *MemoryStorage) SetMulti(ctx context.Context, items map[string][]byte, ttl time.Duration) error {
	for key, value := range items {
		if err := ms.Set(ctx, key, value, ttl); err != nil {
			return err
		}
	}
	return nil
}

// DeleteMulti removes multiple keys
func (ms *MemoryStorage) DeleteMulti(ctx context.Context, keys []string) error {
	for _, key := range keys {
		ms.Delete(ctx, key) // Ignore individual errors
	}
	return nil
}

// Increment increments a numeric value
func (ms *MemoryStorage) Increment(ctx context.Context, key string, delta int64) (int64, error) {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	
	item, exists := ms.data[key]
	var currentValue int64 = 0
	
	if exists && (!item.expiresAt.IsZero() && time.Now().Before(item.expiresAt) || item.expiresAt.IsZero()) {
		// Parse current value
		if len(item.value) > 0 {
			fmt.Sscanf(string(item.value), "%d", &currentValue)
		}
	}
	
	newValue := currentValue + delta
	valueBytes := []byte(fmt.Sprintf("%d", newValue))
	
	now := time.Now()
	newItem := &memoryItem{
		value:     valueBytes,
		createdAt: now,
		updatedAt: now,
	}
	
	if exists && !item.expiresAt.IsZero() {
		newItem.expiresAt = item.expiresAt
		newItem.createdAt = item.createdAt
	}
	
	ms.data[key] = newItem
	return newValue, nil
}

// Decrement decrements a numeric value
func (ms *MemoryStorage) Decrement(ctx context.Context, key string, delta int64) (int64, error) {
	return ms.Increment(ctx, key, -delta)
}

// SetNX sets a key only if it doesn't exist
func (ms *MemoryStorage) SetNX(ctx context.Context, key string, value []byte, ttl time.Duration) (bool, error) {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	
	if item, exists := ms.data[key]; exists {
		// Check if not expired
		if item.expiresAt.IsZero() || time.Now().Before(item.expiresAt) {
			return false, nil
		}
	}
	
	now := time.Now()
	item := &memoryItem{
		value:     make([]byte, len(value)),
		createdAt: now,
		updatedAt: now,
	}
	copy(item.value, value)
	
	if ttl > 0 {
		item.expiresAt = now.Add(ttl)
	}
	
	ms.data[key] = item
	return true, nil
}

// GetSet atomically sets a key and returns the old value
func (ms *MemoryStorage) GetSet(ctx context.Context, key string, value []byte) ([]byte, error) {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	
	var oldValue []byte
	if item, exists := ms.data[key]; exists {
		if item.expiresAt.IsZero() || time.Now().Before(item.expiresAt) {
			oldValue = make([]byte, len(item.value))
			copy(oldValue, item.value)
		}
	}
	
	now := time.Now()
	ms.data[key] = &memoryItem{
		value:     make([]byte, len(value)),
		createdAt: now,
		updatedAt: now,
	}
	copy(ms.data[key].value, value)
	
	return oldValue, nil
}

// Keys returns all keys matching a pattern
func (ms *MemoryStorage) Keys(ctx context.Context, pattern string) ([]string, error) {
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	
	var keys []string
	now := time.Now()
	
	for key, item := range ms.data {
		// Check expiration
		if !item.expiresAt.IsZero() && now.After(item.expiresAt) {
			continue
		}
		
		// Simple pattern matching (supports * wildcard)
		if ms.matchPattern(key, pattern) {
			keys = append(keys, key)
		}
	}
	
	return keys, nil
}

// FlushAll removes all keys
func (ms *MemoryStorage) FlushAll(ctx context.Context) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	
	ms.data = make(map[string]*memoryItem)
	return nil
}

// Size returns the number of keys
func (ms *MemoryStorage) Size(ctx context.Context) (int64, error) {
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	
	count := int64(0)
	now := time.Now()
	
	for _, item := range ms.data {
		// Check expiration
		if item.expiresAt.IsZero() || now.Before(item.expiresAt) {
			count++
		}
	}
	
	return count, nil
}

// Ping checks if the storage is accessible
func (ms *MemoryStorage) Ping(ctx context.Context) error {
	return nil // Memory storage is always accessible
}

// Close closes the storage
func (ms *MemoryStorage) Close() error {
	close(ms.stopCleanup)
	ms.wg.Wait()
	return nil
}

// Info returns storage information
func (ms *MemoryStorage) Info() storage.StorageInfo {
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	
	return storage.StorageInfo{
		Type:        "memory",
		Version:     "1.0.0",
		Status:      "healthy",
		Connections: 1,
		Memory:      ms.calculateMemoryUsage(),
		Stats:       ms.getStats(),
		Config: map[string]string{
			"type": "memory",
		},
	}
}

// Helper methods

// startCleanup starts the cleanup goroutine
func (ms *MemoryStorage) startCleanup() {
	ms.wg.Add(1)
	go func() {
		defer ms.wg.Done()
		
		ticker := time.NewTicker(time.Minute)
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				ms.cleanup()
			case <-ms.stopCleanup:
				return
			}
		}
	}()
}

// cleanup removes expired items
func (ms *MemoryStorage) cleanup() {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	
	now := time.Now()
	for key, item := range ms.data {
		if !item.expiresAt.IsZero() && now.After(item.expiresAt) {
			delete(ms.data, key)
		}
	}
}

// matchPattern performs simple pattern matching
func (ms *MemoryStorage) matchPattern(text, pattern string) bool {
	if pattern == "*" {
		return true
	}
	
	if !strings.Contains(pattern, "*") {
		return text == pattern
	}
	
	// Simple wildcard matching
	parts := strings.Split(pattern, "*")
	if len(parts) == 2 {
		return strings.HasPrefix(text, parts[0]) && strings.HasSuffix(text, parts[1])
	}
	
	return false
}

// recordMetric records operation metrics
func (ms *MemoryStorage) recordMetric(operation string, duration time.Duration) {
	ms.metrics.Operations[operation]++
	ms.metrics.ResponseTimes[operation] = duration.Microseconds()
}

// recordError records error metrics
func (ms *MemoryStorage) recordError(operation string) {
	ms.metrics.Errors[operation]++
}

// calculateMemoryUsage calculates approximate memory usage
func (ms *MemoryStorage) calculateMemoryUsage() int64 {
	var total int64
	for key, item := range ms.data {
		total += int64(len(key) + len(item.value) + 64) // Approximate overhead
	}
	return total
}

// getStats returns operation statistics
func (ms *MemoryStorage) getStats() map[string]int64 {
	stats := make(map[string]int64)
	for op, count := range ms.metrics.Operations {
		stats[op+"_count"] = count
	}
	for op, count := range ms.metrics.Errors {
		stats[op+"_errors"] = count
	}
	return stats
}
