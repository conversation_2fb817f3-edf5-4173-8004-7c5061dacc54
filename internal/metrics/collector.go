package metrics

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	"api-gateway/pkg/telemetry"
)

var (
	ErrMetricNotFound = errors.New("metric not found")
)

// MetricType represents the type of metric
type MetricType string

const (
	MetricTypeCounter   MetricType = "counter"
	MetricTypeGauge     MetricType = "gauge"
	MetricTypeHistogram MetricType = "histogram"
	MetricTypeSummary   MetricType = "summary"
)

// Metric represents a single metric
type Metric struct {
	Name        string            `json:"name"`
	Type        MetricType        `json:"type"`
	Value       float64           `json:"value"`
	Labels      map[string]string `json:"labels,omitempty"`
	Timestamp   time.Time         `json:"timestamp"`
	Description string            `json:"description,omitempty"`
}

// MetricCollector interface defines the contract for metrics collection
type MetricCollector interface {
	// Counter operations
	IncCounter(name string, labels map[string]string)
	AddCounter(name string, value float64, labels map[string]string)
	
	// Gauge operations
	SetGauge(name string, value float64, labels map[string]string)
	IncGauge(name string, labels map[string]string)
	DecGauge(name string, labels map[string]string)
	AddGauge(name string, value float64, labels map[string]string)
	
	// Histogram operations
	ObserveHistogram(name string, value float64, labels map[string]string)
	
	// Summary operations
	ObserveSummary(name string, value float64, labels map[string]string)
	
	// Retrieval operations
	GetMetric(name string, labels map[string]string) (*Metric, error)
	GetAllMetrics() []*Metric
	GetMetricsByType(metricType MetricType) []*Metric
	GetMetricsByPrefix(prefix string) []*Metric
	
	// Management operations
	RegisterMetric(name string, metricType MetricType, description string) error
	UnregisterMetric(name string) error
	Reset() error
	
	// Export operations
	Export(format string) ([]byte, error)
	ExportPrometheus() []byte
	ExportJSON() []byte
}

// Collector implements the MetricCollector interface
type Collector struct {
	logger      *telemetry.Logger
	metrics     map[string]*MetricFamily
	mu          sync.RWMutex
	
	// Configuration
	config      *CollectorConfig
	
	// Background processing
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
}

// MetricFamily represents a family of metrics with the same name but different labels
type MetricFamily struct {
	Name        string            `json:"name"`
	Type        MetricType        `json:"type"`
	Description string            `json:"description"`
	Metrics     map[string]*Metric `json:"metrics"` // Key is label hash
	mu          sync.RWMutex
}

// CollectorConfig contains configuration for the metrics collector
type CollectorConfig struct {
	CollectionInterval time.Duration `yaml:"collection_interval"`
	RetentionPeriod    time.Duration `yaml:"retention_period"`
	MaxMetrics         int           `yaml:"max_metrics"`
	EnableAggregation  bool          `yaml:"enable_aggregation"`
	AggregationWindow  time.Duration `yaml:"aggregation_window"`
}

// NewCollector creates a new metrics collector
func NewCollector(logger *telemetry.Logger, config *CollectorConfig) *Collector {
	if config == nil {
		config = &CollectorConfig{
			CollectionInterval: 10 * time.Second,
			RetentionPeriod:    24 * time.Hour,
			MaxMetrics:         10000,
			EnableAggregation:  true,
			AggregationWindow:  time.Minute,
		}
	}
	
	ctx, cancel := context.WithCancel(context.Background())
	
	collector := &Collector{
		logger:  logger,
		metrics: make(map[string]*MetricFamily),
		config:  config,
		ctx:     ctx,
		cancel:  cancel,
	}
	
	// Start background processing
	collector.startBackgroundProcessing()
	
	return collector
}

// RegisterMetric registers a new metric
func (c *Collector) RegisterMetric(name string, metricType MetricType, description string) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	if _, exists := c.metrics[name]; exists {
		return nil // Already registered
	}
	
	c.metrics[name] = &MetricFamily{
		Name:        name,
		Type:        metricType,
		Description: description,
		Metrics:     make(map[string]*Metric),
	}
	
	c.logger.Debug("Metric registered", "name", name, "type", metricType)
	return nil
}

// UnregisterMetric unregisters a metric
func (c *Collector) UnregisterMetric(name string) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	delete(c.metrics, name)
	c.logger.Debug("Metric unregistered", "name", name)
	return nil
}

// IncCounter increments a counter metric
func (c *Collector) IncCounter(name string, labels map[string]string) {
	c.AddCounter(name, 1, labels)
}

// AddCounter adds a value to a counter metric
func (c *Collector) AddCounter(name string, value float64, labels map[string]string) {
	c.updateMetric(name, MetricTypeCounter, value, labels, func(current float64) float64 {
		return current + value
	})
}

// SetGauge sets a gauge metric value
func (c *Collector) SetGauge(name string, value float64, labels map[string]string) {
	c.updateMetric(name, MetricTypeGauge, value, labels, func(current float64) float64 {
		return value
	})
}

// IncGauge increments a gauge metric
func (c *Collector) IncGauge(name string, labels map[string]string) {
	c.AddGauge(name, 1, labels)
}

// DecGauge decrements a gauge metric
func (c *Collector) DecGauge(name string, labels map[string]string) {
	c.AddGauge(name, -1, labels)
}

// AddGauge adds a value to a gauge metric
func (c *Collector) AddGauge(name string, value float64, labels map[string]string) {
	c.updateMetric(name, MetricTypeGauge, value, labels, func(current float64) float64 {
		return current + value
	})
}

// ObserveHistogram observes a value for a histogram metric
func (c *Collector) ObserveHistogram(name string, value float64, labels map[string]string) {
	c.updateMetric(name, MetricTypeHistogram, value, labels, func(current float64) float64 {
		// For histograms, we might want to implement bucketing logic here
		// For now, we'll just store the latest value
		return value
	})
}

// ObserveSummary observes a value for a summary metric
func (c *Collector) ObserveSummary(name string, value float64, labels map[string]string) {
	c.updateMetric(name, MetricTypeSummary, value, labels, func(current float64) float64 {
		// For summaries, we might want to implement quantile calculation here
		// For now, we'll just store the latest value
		return value
	})
}

// updateMetric updates a metric with the given value
func (c *Collector) updateMetric(name string, metricType MetricType, value float64, labels map[string]string, updateFunc func(float64) float64) {
	c.mu.RLock()
	family, exists := c.metrics[name]
	c.mu.RUnlock()
	
	if !exists {
		// Auto-register metric
		c.RegisterMetric(name, metricType, "")
		c.mu.RLock()
		family = c.metrics[name]
		c.mu.RUnlock()
	}
	
	labelHash := c.hashLabels(labels)
	
	family.mu.Lock()
	defer family.mu.Unlock()
	
	metric, exists := family.Metrics[labelHash]
	if !exists {
		metric = &Metric{
			Name:      name,
			Type:      metricType,
			Labels:    labels,
			Timestamp: time.Now(),
		}
		family.Metrics[labelHash] = metric
	}
	
	metric.Value = updateFunc(metric.Value)
	metric.Timestamp = time.Now()
}

// GetMetric retrieves a specific metric
func (c *Collector) GetMetric(name string, labels map[string]string) (*Metric, error) {
	c.mu.RLock()
	family, exists := c.metrics[name]
	c.mu.RUnlock()
	
	if !exists {
		return nil, ErrMetricNotFound
	}
	
	labelHash := c.hashLabels(labels)
	
	family.mu.RLock()
	defer family.mu.RUnlock()
	
	metric, exists := family.Metrics[labelHash]
	if !exists {
		return nil, ErrMetricNotFound
	}
	
	// Return a copy to avoid race conditions
	return &Metric{
		Name:      metric.Name,
		Type:      metric.Type,
		Value:     metric.Value,
		Labels:    metric.Labels,
		Timestamp: metric.Timestamp,
	}, nil
}

// GetAllMetrics retrieves all metrics
func (c *Collector) GetAllMetrics() []*Metric {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	var allMetrics []*Metric
	
	for _, family := range c.metrics {
		family.mu.RLock()
		for _, metric := range family.Metrics {
			allMetrics = append(allMetrics, &Metric{
				Name:      metric.Name,
				Type:      metric.Type,
				Value:     metric.Value,
				Labels:    metric.Labels,
				Timestamp: metric.Timestamp,
			})
		}
		family.mu.RUnlock()
	}
	
	return allMetrics
}

// GetMetricsByType retrieves metrics by type
func (c *Collector) GetMetricsByType(metricType MetricType) []*Metric {
	allMetrics := c.GetAllMetrics()
	var filteredMetrics []*Metric
	
	for _, metric := range allMetrics {
		if metric.Type == metricType {
			filteredMetrics = append(filteredMetrics, metric)
		}
	}
	
	return filteredMetrics
}

// GetMetricsByPrefix retrieves metrics by name prefix
func (c *Collector) GetMetricsByPrefix(prefix string) []*Metric {
	allMetrics := c.GetAllMetrics()
	var filteredMetrics []*Metric
	
	for _, metric := range allMetrics {
		if len(metric.Name) >= len(prefix) && metric.Name[:len(prefix)] == prefix {
			filteredMetrics = append(filteredMetrics, metric)
		}
	}
	
	return filteredMetrics
}

// Reset clears all metrics
func (c *Collector) Reset() error {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.metrics = make(map[string]*MetricFamily)
	c.logger.Info("All metrics reset")
	return nil
}

// hashLabels creates a hash of labels for use as a map key
func (c *Collector) hashLabels(labels map[string]string) string {
	if len(labels) == 0 {
		return ""
	}

	// Sort labels for consistent hashing
	keys := make([]string, 0, len(labels))
	for k := range labels {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var parts []string
	for _, k := range keys {
		parts = append(parts, fmt.Sprintf("%s=%s", k, labels[k]))
	}

	labelStr := strings.Join(parts, ",")
	hash := md5.Sum([]byte(labelStr))
	return hex.EncodeToString(hash[:])
}

// startBackgroundProcessing starts background processing routines
func (c *Collector) startBackgroundProcessing() {
	if c.config.EnableAggregation {
		c.wg.Add(1)
		go c.aggregationWorker()
	}

	c.wg.Add(1)
	go c.cleanupWorker()
}

// aggregationWorker performs periodic metric aggregation
func (c *Collector) aggregationWorker() {
	defer c.wg.Done()

	ticker := time.NewTicker(c.config.AggregationWindow)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.performAggregation()
		case <-c.ctx.Done():
			return
		}
	}
}

// cleanupWorker performs periodic cleanup of old metrics
func (c *Collector) cleanupWorker() {
	defer c.wg.Done()

	ticker := time.NewTicker(c.config.CollectionInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.performCleanup()
		case <-c.ctx.Done():
			return
		}
	}
}

// performAggregation performs metric aggregation
func (c *Collector) performAggregation() {
	// Implementation would depend on specific aggregation requirements
	c.logger.Debug("Performing metric aggregation")
}

// performCleanup removes old metrics based on retention policy
func (c *Collector) performCleanup() {
	c.mu.Lock()
	defer c.mu.Unlock()

	cutoff := time.Now().Add(-c.config.RetentionPeriod)
	cleaned := 0

	for _, family := range c.metrics {
		family.mu.Lock()
		for hash, metric := range family.Metrics {
			if metric.Timestamp.Before(cutoff) {
				delete(family.Metrics, hash)
				cleaned++
			}
		}
		family.mu.Unlock()
	}

	if cleaned > 0 {
		c.logger.Debug("Cleaned up old metrics", "count", cleaned)
	}
}

// Close stops the collector and cleans up resources
func (c *Collector) Close() error {
	c.cancel()
	c.wg.Wait()
	return nil
}
