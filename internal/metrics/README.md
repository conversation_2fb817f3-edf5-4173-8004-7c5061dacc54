# Internal Metrics

This directory contains internal metrics collection and aggregation systems for the API Gateway.

## Architecture

The metrics system is designed to collect, aggregate, and expose various types of metrics:

1. **Business Metrics**: Request counts, response times, error rates
2. **Performance Metrics**: CPU usage, memory consumption, goroutine counts
3. **Security Metrics**: Authentication failures, rate limit hits, WAF blocks
4. **System Metrics**: Connection counts, circuit breaker states, plugin execution times

## Structure

```
internal/metrics/
├── README.md           # This file
├── collector.go        # Main metrics collector interface
├── business.go         # Business metrics collection
├── performance.go      # Performance metrics collection
├── security.go         # Security metrics collection
├── system.go           # System metrics collection
├── aggregator.go       # Metrics aggregation logic
├── exporter.go         # Metrics export functionality
└── examples/           # Example metrics implementations
    ├── custom.go       # Custom metrics example
    └── dashboard.go    # Dashboard metrics example
```

## Features

- **Multi-dimensional Metrics**: Support for labels and tags
- **Real-time Collection**: Low-latency metrics collection
- **Aggregation**: Time-based and count-based aggregation
- **Export Formats**: Prometheus, JSON, and custom formats
- **Alerting Integration**: Support for alerting rules
- **Performance Optimized**: Minimal overhead on request processing

## Metric Types

### Business Metrics
- Request count by endpoint, method, status code
- Response time percentiles (P50, P90, P95, P99)
- Error rates and error types
- Throughput (requests per second)
- User activity metrics

### Performance Metrics
- CPU usage and load average
- Memory usage (heap, stack, GC stats)
- Goroutine counts and states
- Network I/O statistics
- Disk I/O statistics

### Security Metrics
- Authentication success/failure rates
- Authorization denials
- Rate limiting hits
- WAF rule triggers
- Suspicious activity detection

### System Metrics
- Active connections
- Circuit breaker states
- Plugin execution times
- Service discovery health
- Cache hit/miss rates

## Usage

### Basic Metrics Collection

```go
// Create a metrics collector
collector := metrics.NewCollector(logger)

// Collect business metrics
collector.RecordRequest("GET", "/api/users", 200, time.Since(start))

// Collect security metrics
collector.RecordAuthFailure("jwt", "invalid_token")

// Collect performance metrics
collector.RecordMemoryUsage(memStats.Alloc)
```

### Custom Metrics

```go
// Define custom metric
customMetric := metrics.NewCounter("custom_operations_total", "Total custom operations")

// Record custom metric
customMetric.WithLabels(map[string]string{
    "operation": "data_processing",
    "status":    "success",
}).Inc()
```

### Metrics Export

```go
// Export to Prometheus format
prometheusData := collector.ExportPrometheus()

// Export to JSON format
jsonData := collector.ExportJSON()

// Export custom format
customData := collector.Export("custom", options)
```

## Integration

The metrics system integrates with:

- **Telemetry System**: Uses existing telemetry infrastructure
- **Middleware**: Automatic metrics collection from middleware
- **Handlers**: Manual metrics collection from handlers
- **Background Services**: Metrics from background processes
- **External Systems**: Metrics from external service calls

## Configuration

```yaml
metrics:
  internal:
    enabled: true
    collection_interval: "10s"
    retention_period: "24h"
    aggregation:
      enabled: true
      window_size: "1m"
    export:
      prometheus:
        enabled: true
        endpoint: "/internal/metrics"
      json:
        enabled: false
    alerts:
      enabled: true
      rules_file: "alerts.yaml"
```

## Best Practices

1. **Use Appropriate Metric Types**: Counter for cumulative values, Gauge for current values, Histogram for distributions
2. **Label Cardinality**: Keep label cardinality low to avoid memory issues
3. **Sampling**: Use sampling for high-frequency metrics
4. **Aggregation**: Pre-aggregate metrics when possible
5. **Performance**: Minimize metrics collection overhead
6. **Naming**: Use consistent naming conventions for metrics
