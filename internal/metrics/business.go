package metrics

import (
	"time"

	"api-gateway/pkg/telemetry"
)

// BusinessMetrics handles business-related metrics collection
type BusinessMetrics struct {
	collector MetricCollector
	logger    *telemetry.Logger
}

// NewBusinessMetrics creates a new business metrics collector
func NewBusinessMetrics(collector MetricCollector, logger *telemetry.Logger) *BusinessMetrics {
	bm := &BusinessMetrics{
		collector: collector,
		logger:    logger,
	}
	
	// Register business metrics
	bm.registerMetrics()
	
	return bm
}

// registerMetrics registers all business metrics
func (bm *BusinessMetrics) registerMetrics() {
	metrics := []struct {
		name        string
		metricType  MetricType
		description string
	}{
		{"gateway_requests_total", MetricTypeCounter, "Total number of requests processed"},
		{"gateway_request_duration_seconds", MetricTypeHistogram, "Request duration in seconds"},
		{"gateway_request_size_bytes", MetricTypeHistogram, "Request size in bytes"},
		{"gateway_response_size_bytes", MetricTypeHistogram, "Response size in bytes"},
		{"gateway_active_requests", MetricTypeGauge, "Number of active requests"},
		{"gateway_error_rate", MetricTypeGauge, "Current error rate"},
		{"gateway_throughput_rps", MetricTypeGauge, "Current throughput in requests per second"},
		{"gateway_user_sessions_active", MetricTypeGauge, "Number of active user sessions"},
		{"gateway_endpoint_popularity", MetricTypeCounter, "Popularity of endpoints"},
		{"gateway_client_requests", MetricTypeCounter, "Requests by client"},
	}
	
	for _, metric := range metrics {
		bm.collector.RegisterMetric(metric.name, metric.metricType, metric.description)
	}
}

// RecordRequest records a request metric
func (bm *BusinessMetrics) RecordRequest(method, path, status, upstream string, duration time.Duration, requestSize, responseSize int64) {
	labels := map[string]string{
		"method":   method,
		"path":     path,
		"status":   status,
		"upstream": upstream,
	}
	
	// Record request count
	bm.collector.IncCounter("gateway_requests_total", labels)
	
	// Record request duration
	bm.collector.ObserveHistogram("gateway_request_duration_seconds", duration.Seconds(), labels)
	
	// Record request size
	if requestSize > 0 {
		bm.collector.ObserveHistogram("gateway_request_size_bytes", float64(requestSize), labels)
	}
	
	// Record response size
	if responseSize > 0 {
		bm.collector.ObserveHistogram("gateway_response_size_bytes", float64(responseSize), labels)
	}
	
	bm.logger.Debug("Request metrics recorded",
		"method", method,
		"path", path,
		"status", status,
		"duration", duration,
		"request_size", requestSize,
		"response_size", responseSize)
}

// RecordActiveRequest increments active request count
func (bm *BusinessMetrics) RecordActiveRequest() {
	bm.collector.IncGauge("gateway_active_requests", nil)
}

// RecordCompletedRequest decrements active request count
func (bm *BusinessMetrics) RecordCompletedRequest() {
	bm.collector.DecGauge("gateway_active_requests", nil)
}

// RecordErrorRate records the current error rate
func (bm *BusinessMetrics) RecordErrorRate(rate float64) {
	bm.collector.SetGauge("gateway_error_rate", rate, nil)
}

// RecordThroughput records the current throughput
func (bm *BusinessMetrics) RecordThroughput(rps float64) {
	bm.collector.SetGauge("gateway_throughput_rps", rps, nil)
}

// RecordUserSession records user session metrics
func (bm *BusinessMetrics) RecordUserSession(userID string, action string) {
	labels := map[string]string{
		"user_id": userID,
		"action":  action,
	}
	
	switch action {
	case "login":
		bm.collector.IncGauge("gateway_user_sessions_active", labels)
	case "logout":
		bm.collector.DecGauge("gateway_user_sessions_active", labels)
	}
}

// RecordEndpointPopularity records endpoint popularity
func (bm *BusinessMetrics) RecordEndpointPopularity(endpoint string) {
	labels := map[string]string{
		"endpoint": endpoint,
	}
	bm.collector.IncCounter("gateway_endpoint_popularity", labels)
}

// RecordClientRequest records client-specific request metrics
func (bm *BusinessMetrics) RecordClientRequest(clientID, clientType string) {
	labels := map[string]string{
		"client_id":   clientID,
		"client_type": clientType,
	}
	bm.collector.IncCounter("gateway_client_requests", labels)
}

// GetRequestStats returns current request statistics
func (bm *BusinessMetrics) GetRequestStats() map[string]interface{} {
	stats := make(map[string]interface{})
	
	// Get total requests
	if metric, err := bm.collector.GetMetric("gateway_requests_total", nil); err == nil {
		stats["total_requests"] = metric.Value
	}
	
	// Get active requests
	if metric, err := bm.collector.GetMetric("gateway_active_requests", nil); err == nil {
		stats["active_requests"] = metric.Value
	}
	
	// Get error rate
	if metric, err := bm.collector.GetMetric("gateway_error_rate", nil); err == nil {
		stats["error_rate"] = metric.Value
	}
	
	// Get throughput
	if metric, err := bm.collector.GetMetric("gateway_throughput_rps", nil); err == nil {
		stats["throughput_rps"] = metric.Value
	}
	
	return stats
}

// GetTopEndpoints returns the most popular endpoints
func (bm *BusinessMetrics) GetTopEndpoints(limit int) []EndpointStat {
	metrics := bm.collector.GetMetricsByPrefix("gateway_endpoint_popularity")
	
	var endpoints []EndpointStat
	for _, metric := range metrics {
		if endpoint, exists := metric.Labels["endpoint"]; exists {
			endpoints = append(endpoints, EndpointStat{
				Endpoint: endpoint,
				Count:    int64(metric.Value),
			})
		}
	}
	
	// Sort by count (descending)
	for i := 0; i < len(endpoints)-1; i++ {
		for j := i + 1; j < len(endpoints); j++ {
			if endpoints[i].Count < endpoints[j].Count {
				endpoints[i], endpoints[j] = endpoints[j], endpoints[i]
			}
		}
	}
	
	if limit > 0 && len(endpoints) > limit {
		endpoints = endpoints[:limit]
	}
	
	return endpoints
}

// GetClientStats returns client statistics
func (bm *BusinessMetrics) GetClientStats() []ClientStat {
	metrics := bm.collector.GetMetricsByPrefix("gateway_client_requests")
	
	clientStats := make(map[string]*ClientStat)
	
	for _, metric := range metrics {
		clientID, hasClientID := metric.Labels["client_id"]
		clientType, hasClientType := metric.Labels["client_type"]
		
		if hasClientID {
			if stat, exists := clientStats[clientID]; exists {
				stat.RequestCount += int64(metric.Value)
			} else {
				clientStats[clientID] = &ClientStat{
					ClientID:     clientID,
					ClientType:   clientType,
					RequestCount: int64(metric.Value),
				}
			}
		}
	}
	
	var stats []ClientStat
	for _, stat := range clientStats {
		stats = append(stats, *stat)
	}
	
	return stats
}

// EndpointStat represents endpoint statistics
type EndpointStat struct {
	Endpoint string `json:"endpoint"`
	Count    int64  `json:"count"`
}

// ClientStat represents client statistics
type ClientStat struct {
	ClientID     string `json:"client_id"`
	ClientType   string `json:"client_type"`
	RequestCount int64  `json:"request_count"`
}

// RequestMetrics represents aggregated request metrics
type RequestMetrics struct {
	TotalRequests   int64   `json:"total_requests"`
	ActiveRequests  int64   `json:"active_requests"`
	ErrorRate       float64 `json:"error_rate"`
	ThroughputRPS   float64 `json:"throughput_rps"`
	AvgResponseTime float64 `json:"avg_response_time"`
}

// GetAggregatedMetrics returns aggregated business metrics
func (bm *BusinessMetrics) GetAggregatedMetrics() *RequestMetrics {
	stats := bm.GetRequestStats()
	
	metrics := &RequestMetrics{}
	
	if val, ok := stats["total_requests"].(float64); ok {
		metrics.TotalRequests = int64(val)
	}
	
	if val, ok := stats["active_requests"].(float64); ok {
		metrics.ActiveRequests = int64(val)
	}
	
	if val, ok := stats["error_rate"].(float64); ok {
		metrics.ErrorRate = val
	}
	
	if val, ok := stats["throughput_rps"].(float64); ok {
		metrics.ThroughputRPS = val
	}
	
	// Calculate average response time from histogram metrics
	durationMetrics := bm.collector.GetMetricsByPrefix("gateway_request_duration_seconds")
	if len(durationMetrics) > 0 {
		var totalDuration float64
		var count int64
		
		for _, metric := range durationMetrics {
			totalDuration += metric.Value
			count++
		}
		
		if count > 0 {
			metrics.AvgResponseTime = totalDuration / float64(count)
		}
	}
	
	return metrics
}
