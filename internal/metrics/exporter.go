package metrics

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

// Export exports metrics in the specified format
func (c *Collector) Export(format string) ([]byte, error) {
	switch strings.ToLower(format) {
	case "prometheus":
		return c.ExportPrometheus(), nil
	case "json":
		return c.ExportJSON(), nil
	default:
		return nil, fmt.Errorf("unsupported export format: %s", format)
	}
}

// ExportPrometheus exports metrics in Prometheus format
func (c *Collector) ExportPrometheus() []byte {
	var output strings.Builder
	
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	for _, family := range c.metrics {
		// Write help comment
		if family.Description != "" {
			output.WriteString(fmt.Sprintf("# HELP %s %s\n", family.Name, family.Description))
		}
		
		// Write type comment
		prometheusType := c.getPrometheusType(family.Type)
		output.WriteString(fmt.Sprintf("# TYPE %s %s\n", family.Name, prometheusType))
		
		// Write metrics
		family.mu.RLock()
		for _, metric := range family.Metrics {
			if len(metric.Labels) > 0 {
				labelStr := c.formatPrometheusLabels(metric.Labels)
				output.WriteString(fmt.Sprintf("%s{%s} %g %d\n", 
					metric.Name, labelStr, metric.Value, metric.Timestamp.UnixMilli()))
			} else {
				output.WriteString(fmt.Sprintf("%s %g %d\n", 
					metric.Name, metric.Value, metric.Timestamp.UnixMilli()))
			}
		}
		family.mu.RUnlock()
		
		output.WriteString("\n")
	}
	
	return []byte(output.String())
}

// ExportJSON exports metrics in JSON format
func (c *Collector) ExportJSON() []byte {
	metrics := c.GetAllMetrics()
	
	export := struct {
		Timestamp time.Time `json:"timestamp"`
		Metrics   []*Metric `json:"metrics"`
	}{
		Timestamp: time.Now().UTC(),
		Metrics:   metrics,
	}
	
	data, _ := json.MarshalIndent(export, "", "  ")
	return data
}

// getPrometheusType converts internal metric type to Prometheus type
func (c *Collector) getPrometheusType(metricType MetricType) string {
	switch metricType {
	case MetricTypeCounter:
		return "counter"
	case MetricTypeGauge:
		return "gauge"
	case MetricTypeHistogram:
		return "histogram"
	case MetricTypeSummary:
		return "summary"
	default:
		return "untyped"
	}
}

// formatPrometheusLabels formats labels for Prometheus export
func (c *Collector) formatPrometheusLabels(labels map[string]string) string {
	if len(labels) == 0 {
		return ""
	}
	
	var parts []string
	for key, value := range labels {
		// Escape quotes in values
		escapedValue := strings.ReplaceAll(value, "\"", "\\\"")
		parts = append(parts, fmt.Sprintf("%s=\"%s\"", key, escapedValue))
	}
	
	return strings.Join(parts, ",")
}

// ExportSummary exports a summary of metrics
func (c *Collector) ExportSummary() []byte {
	summary := struct {
		Timestamp    time.Time `json:"timestamp"`
		TotalMetrics int       `json:"total_metrics"`
		MetricTypes  map[string]int `json:"metric_types"`
		Families     []string  `json:"families"`
	}{
		Timestamp:   time.Now().UTC(),
		MetricTypes: make(map[string]int),
	}
	
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	for name, family := range c.metrics {
		summary.Families = append(summary.Families, name)
		
		family.mu.RLock()
		metricCount := len(family.Metrics)
		summary.TotalMetrics += metricCount
		summary.MetricTypes[string(family.Type)] += metricCount
		family.mu.RUnlock()
	}
	
	data, _ := json.MarshalIndent(summary, "", "  ")
	return data
}
