# Testing Framework

This directory contains comprehensive testing infrastructure for the API Gateway, including unit tests, integration tests, performance tests, and end-to-end tests.

## Structure

```
tests/
├── README.md                    # This file
├── unit/                        # Unit tests
│   ├── handlers_test.go         # Handler unit tests
│   ├── middleware_test.go       # Middleware unit tests
│   ├── auth_test.go             # Authentication tests
│   ├── security_test.go         # Security component tests
│   └── utils_test.go            # Utility function tests
├── integration/                 # Integration tests
│   ├── gateway_test.go          # Gateway integration tests
│   ├── storage_test.go          # Storage integration tests
│   ├── discovery_test.go        # Service discovery tests
│   └── plugins_test.go          # Plugin integration tests
├── e2e/                         # End-to-end tests
│   ├── basic_flow_test.go       # Basic request flow tests
│   ├── auth_flow_test.go        # Authentication flow tests
│   ├── rate_limit_test.go       # Rate limiting tests
│   └── failover_test.go         # Failover scenario tests
├── performance/                 # Performance tests
│   ├── load_test.go             # Load testing
│   ├── stress_test.go           # Stress testing
│   ├── benchmark_test.go        # Benchmark tests
│   └── memory_test.go           # Memory usage tests
├── fixtures/                    # Test fixtures and data
│   ├── configs/                 # Test configurations
│   ├── certificates/            # Test certificates
│   ├── data/                    # Test data files
│   └── mocks/                   # Mock services
├── helpers/                     # Test helper functions
│   ├── test_server.go           # Test server utilities
│   ├── mock_services.go         # Mock service implementations
│   ├── assertions.go            # Custom assertions
│   └── containers.go            # Test container management
└── scripts/                     # Test scripts
    ├── run_tests.sh             # Test runner script
    ├── setup_test_env.sh        # Test environment setup
    └── cleanup.sh               # Test cleanup
```

## Test Categories

### Unit Tests (`unit/`)

Unit tests focus on testing individual components in isolation:

- **Handler Tests**: Test HTTP handlers with mocked dependencies
- **Middleware Tests**: Test middleware functionality
- **Authentication Tests**: Test auth components
- **Security Tests**: Test security features
- **Utility Tests**: Test utility functions

**Running Unit Tests:**
```bash
go test ./tests/unit/... -v
```

### Integration Tests (`integration/`)

Integration tests verify component interactions:

- **Gateway Integration**: Test gateway with real components
- **Storage Integration**: Test with actual storage backends
- **Service Discovery**: Test with real discovery services
- **Plugin Integration**: Test plugin loading and execution

**Running Integration Tests:**
```bash
go test ./tests/integration/... -v
```

### End-to-End Tests (`e2e/`)

E2E tests verify complete user scenarios:

- **Basic Flow**: Simple request/response scenarios
- **Authentication Flow**: Complete auth workflows
- **Rate Limiting**: Rate limit enforcement
- **Failover**: Service failure scenarios

**Running E2E Tests:**
```bash
go test ./tests/e2e/... -v
```

### Performance Tests (`performance/`)

Performance tests measure system performance:

- **Load Tests**: Normal load scenarios
- **Stress Tests**: High load scenarios
- **Benchmarks**: Performance benchmarks
- **Memory Tests**: Memory usage analysis

**Running Performance Tests:**
```bash
go test ./tests/performance/... -bench=. -benchmem
```

## Test Configuration

### Test Environment Variables

```bash
# Test database
TEST_DB_URL=postgres://test:test@localhost:5432/gateway_test

# Test Redis
TEST_REDIS_URL=redis://localhost:6379/1

# Test Consul
TEST_CONSUL_URL=http://localhost:8500

# Test timeouts
TEST_TIMEOUT=30s
TEST_SLOW_THRESHOLD=5s

# Test parallelism
TEST_PARALLEL=4
```

### Test Configuration Files

Test configurations are stored in `fixtures/configs/`:

- `test.yaml`: Basic test configuration
- `integration.yaml`: Integration test configuration
- `performance.yaml`: Performance test configuration

## Test Utilities

### Test Server (`helpers/test_server.go`)

Provides utilities for creating test servers:

```go
// Create test gateway
gateway := helpers.NewTestGateway(config)

// Create mock upstream
upstream := helpers.NewMockUpstream()

// Create test client
client := helpers.NewTestClient(gateway.URL())
```

### Mock Services (`helpers/mock_services.go`)

Provides mock implementations:

```go
// Mock auth service
authService := mocks.NewAuthService()
authService.On("Authenticate", mock.Anything).Return(authResult, nil)

// Mock storage
storage := mocks.NewStorage()
storage.On("Get", "key").Return([]byte("value"), nil)
```

### Custom Assertions (`helpers/assertions.go`)

Provides custom test assertions:

```go
// Assert HTTP response
helpers.AssertHTTPStatus(t, response, http.StatusOK)
helpers.AssertJSONResponse(t, response, expectedJSON)

// Assert metrics
helpers.AssertMetricValue(t, metrics, "requests_total", 100)

// Assert logs
helpers.AssertLogContains(t, logs, "request processed")
```

## Running Tests

### All Tests

```bash
# Run all tests
make test

# Run with coverage
make test-coverage

# Run with race detection
make test-race
```

### Specific Test Categories

```bash
# Unit tests only
make test-unit

# Integration tests only
make test-integration

# E2E tests only
make test-e2e

# Performance tests only
make test-performance
```

### Test Environment Setup

```bash
# Setup test environment
make test-setup

# Start test dependencies
make test-deps-up

# Stop test dependencies
make test-deps-down

# Clean test environment
make test-clean
```

## Test Data Management

### Fixtures

Test fixtures are organized by category:

```
fixtures/
├── configs/
│   ├── minimal.yaml
│   ├── full-featured.yaml
│   └── invalid.yaml
├── certificates/
│   ├── server.crt
│   ├── server.key
│   └── ca.crt
├── data/
│   ├── users.json
│   ├── routes.json
│   └── policies.json
└── mocks/
    ├── auth-responses.json
    └── service-responses.json
```

### Test Database

Test database setup and teardown:

```go
func setupTestDB(t *testing.T) *sql.DB {
    db := helpers.CreateTestDB(t)
    helpers.RunMigrations(t, db)
    helpers.SeedTestData(t, db)
    return db
}

func teardownTestDB(t *testing.T, db *sql.DB) {
    helpers.CleanTestData(t, db)
    db.Close()
}
```

## Continuous Integration

### GitHub Actions

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: test
      redis:
        image: redis:6
      consul:
        image: consul:1.9
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-go@v2
        with:
          go-version: 1.21
      - name: Run tests
        run: make test-ci
```

### Test Reports

Generate test reports:

```bash
# Generate coverage report
make test-coverage-html

# Generate test report
make test-report

# Generate performance report
make test-performance-report
```

## Best Practices

### Test Organization

1. **Arrange-Act-Assert**: Structure tests clearly
2. **Test Isolation**: Each test should be independent
3. **Descriptive Names**: Use clear, descriptive test names
4. **Test Data**: Use fixtures and factories for test data

### Mocking

1. **Mock External Dependencies**: Mock external services
2. **Interface Mocking**: Mock at interface boundaries
3. **Behavior Verification**: Verify mock interactions
4. **State vs Behavior**: Test both state and behavior

### Performance Testing

1. **Baseline Measurements**: Establish performance baselines
2. **Load Patterns**: Test realistic load patterns
3. **Resource Monitoring**: Monitor CPU, memory, and I/O
4. **Regression Detection**: Detect performance regressions

### Test Maintenance

1. **Regular Updates**: Keep tests updated with code changes
2. **Flaky Test Detection**: Identify and fix flaky tests
3. **Test Coverage**: Maintain good test coverage
4. **Documentation**: Document complex test scenarios

## Troubleshooting

### Common Issues

1. **Test Timeouts**: Increase timeout values for slow tests
2. **Port Conflicts**: Use random ports for test services
3. **Race Conditions**: Use proper synchronization
4. **Resource Leaks**: Ensure proper cleanup

### Debugging Tests

```bash
# Run tests with verbose output
go test -v ./tests/...

# Run specific test
go test -run TestSpecificFunction ./tests/unit/

# Run tests with race detection
go test -race ./tests/...

# Run tests with CPU profiling
go test -cpuprofile=cpu.prof ./tests/performance/
```

## Contributing

When adding new tests:

1. Follow the existing test structure
2. Add appropriate test categories
3. Include test documentation
4. Update CI configuration if needed
5. Ensure tests are deterministic and fast
