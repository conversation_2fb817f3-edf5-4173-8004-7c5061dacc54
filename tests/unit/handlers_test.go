package unit

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"api-gateway/internal/handlers"
	"api-gateway/internal/handlers/admin"
	"api-gateway/internal/handlers/examples"
	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func init() {
	gin.SetMode(gin.TestMode)
}

func TestBaseHandler_ValidateAndBind(t *testing.T) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "debug",
		Format: "json",
		Output: "stdout",
	})
	
	handler := handlers.NewBaseHandler(logger)
	
	tests := []struct {
		name        string
		requestBody string
		target      interface{}
		expectError bool
	}{
		{
			name:        "valid JSON",
			requestBody: `{"name": "test", "age": 25}`,
			target:      &struct {
				Name string `json:"name" validate:"required"`
				Age  int    `json:"age" validate:"min=0,max=120"`
			}{},
			expectError: false,
		},
		{
			name:        "invalid JSON",
			requestBody: `{"name": "test", "age": "invalid"}`,
			target:      &struct {
				Name string `json:"name" validate:"required"`
				Age  int    `json:"age" validate:"min=0,max=120"`
			}{},
			expectError: true,
		},
		{
			name:        "validation error",
			requestBody: `{"name": "", "age": 150}`,
			target:      &struct {
				Name string `json:"name" validate:"required"`
				Age  int    `json:"age" validate:"min=0,max=120"`
			}{},
			expectError: true,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("POST", "/test", bytes.NewBufferString(tt.requestBody))
			c.Request.Header.Set("Content-Type", "application/json")
			c.Set("request_id", "test-123")
			
			err := handler.ValidateAndBind(c, tt.target)
			
			if tt.expectError {
				assert.Error(t, err)
				assert.Equal(t, http.StatusBadRequest, w.Code)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestBaseHandler_ResponseMethods(t *testing.T) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "debug",
		Format: "json",
		Output: "stdout",
	})
	
	handler := handlers.NewBaseHandler(logger)
	
	t.Run("SuccessResponse", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("request_id", "test-123")
		
		data := map[string]string{"message": "success"}
		handler.SuccessResponse(c, data)
		
		assert.Equal(t, http.StatusOK, w.Code)
		
		var response handlers.StandardResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		
		assert.True(t, response.Success)
		assert.Equal(t, "test-123", response.RequestID)
		assert.NotEmpty(t, response.Timestamp)
		assert.Nil(t, response.Error)
	})
	
	t.Run("ErrorResponse", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("request_id", "test-456")
		
		handler.ErrorResponse(c, http.StatusBadRequest, "test_error", "Test error message")
		
		assert.Equal(t, http.StatusBadRequest, w.Code)
		
		var response handlers.StandardResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		
		assert.False(t, response.Success)
		assert.Equal(t, "test-456", response.RequestID)
		assert.NotNil(t, response.Error)
		assert.Equal(t, "test_error", response.Error.Code)
		assert.Equal(t, "Test error message", response.Error.Message)
	})
}

func TestHealthHandler(t *testing.T) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "debug",
		Format: "json",
		Output: "stdout",
	})
	
	handler := admin.NewHealthHandler(logger)
	
	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
	}{
		{
			name:           "basic health check",
			queryParams:    "",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "component health check",
			queryParams:    "?component=auth",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "deep health check",
			queryParams:    "?deep=true",
			expectedStatus: http.StatusOK,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("GET", "/health"+tt.queryParams, nil)
			c.Set("request_id", "health-test")
			
			handler.Handle(c)
			
			assert.Equal(t, tt.expectedStatus, w.Code)
			
			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)
			
			assert.Contains(t, response, "success")
			assert.Contains(t, response, "data")
		})
	}
}

func TestEchoHandler(t *testing.T) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "debug",
		Format: "json",
		Output: "stdout",
	})
	
	handler := examples.NewEchoHandler(logger)
	
	tests := []struct {
		name           string
		requestBody    string
		expectedStatus int
		expectError    bool
	}{
		{
			name: "valid echo request",
			requestBody: `{
				"message": "Hello, World!",
				"echo": true,
				"metadata": {"key": "value"}
			}`,
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name: "echo without flag",
			requestBody: `{
				"message": "Hello, World!",
				"echo": false
			}`,
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name: "missing message",
			requestBody: `{
				"echo": true
			}`,
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name: "message too long",
			requestBody: `{
				"message": "` + string(make([]byte, 1001)) + `",
				"echo": true
			}`,
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("POST", "/api/v1/echo", bytes.NewBufferString(tt.requestBody))
			c.Request.Header.Set("Content-Type", "application/json")
			c.Request.Header.Set("User-Agent", "test-client")
			c.Set("request_id", "echo-test")
			
			handler.Handle(c)
			
			assert.Equal(t, tt.expectedStatus, w.Code)
			
			var response handlers.StandardResponse
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)
			
			if tt.expectError {
				assert.False(t, response.Success)
				assert.NotNil(t, response.Error)
			} else {
				assert.True(t, response.Success)
				assert.Nil(t, response.Error)
				
				// Verify echo response structure
				data, ok := response.Data.(*examples.EchoResponse)
				if ok {
					assert.NotEmpty(t, data.OriginalMessage)
					assert.NotEmpty(t, data.EchoedMessage)
					assert.NotNil(t, data.RequestInfo)
					assert.NotZero(t, data.ProcessedAt)
				}
			}
		})
	}
}

func TestDelayedEchoHandler(t *testing.T) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "debug",
		Format: "json",
		Output: "stdout",
	})
	
	handler := examples.NewDelayedEchoHandler(logger)
	
	t.Run("no delay", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("POST", "/api/v1/echo/delayed", 
			bytes.NewBufferString(`{"message": "test", "delay": 0}`))
		c.Request.Header.Set("Content-Type", "application/json")
		c.Set("request_id", "delayed-test")
		
		start := time.Now()
		handler.Handle(c)
		duration := time.Since(start)
		
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Less(t, duration, 100*time.Millisecond) // Should be fast
	})
	
	t.Run("with delay", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("POST", "/api/v1/echo/delayed", 
			bytes.NewBufferString(`{"message": "test", "delay": 1}`))
		c.Request.Header.Set("Content-Type", "application/json")
		c.Set("request_id", "delayed-test")
		
		start := time.Now()
		handler.Handle(c)
		duration := time.Since(start)
		
		assert.Equal(t, http.StatusOK, w.Code)
		assert.GreaterOrEqual(t, duration, 1*time.Second) // Should take at least 1 second
	})
	
	t.Run("invalid delay", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("POST", "/api/v1/echo/delayed", 
			bytes.NewBufferString(`{"message": "test", "delay": 31}`))
		c.Request.Header.Set("Content-Type", "application/json")
		c.Set("request_id", "delayed-test")
		
		handler.Handle(c)
		
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestErrorEchoHandler(t *testing.T) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "debug",
		Format: "json",
		Output: "stdout",
	})
	
	handler := examples.NewErrorEchoHandler(logger)
	
	tests := []struct {
		name           string
		requestBody    string
		expectedStatus int
	}{
		{
			name: "success case",
			requestBody: `{
				"message": "test",
				"should_fail": false
			}`,
			expectedStatus: http.StatusOK,
		},
		{
			name: "unauthorized error",
			requestBody: `{
				"message": "test",
				"should_fail": true,
				"error_type": "unauthorized"
			}`,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name: "forbidden error",
			requestBody: `{
				"message": "test",
				"should_fail": true,
				"error_type": "forbidden"
			}`,
			expectedStatus: http.StatusForbidden,
		},
		{
			name: "not found error",
			requestBody: `{
				"message": "test",
				"should_fail": true,
				"error_type": "not_found"
			}`,
			expectedStatus: http.StatusNotFound,
		},
		{
			name: "internal error",
			requestBody: `{
				"message": "test",
				"should_fail": true,
				"error_type": "internal"
			}`,
			expectedStatus: http.StatusInternalServerError,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("POST", "/api/v1/echo/error", 
				bytes.NewBufferString(tt.requestBody))
			c.Request.Header.Set("Content-Type", "application/json")
			c.Set("request_id", "error-test")
			
			handler.Handle(c)
			
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Benchmark tests
func BenchmarkBaseHandler_ValidateAndBind(b *testing.B) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error", // Reduce logging for benchmarks
		Format: "json",
		Output: "stdout",
	})
	
	handler := handlers.NewBaseHandler(logger)
	requestBody := `{"name": "test", "age": 25}`
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("POST", "/test", bytes.NewBufferString(requestBody))
		c.Request.Header.Set("Content-Type", "application/json")
		c.Set("request_id", "bench-test")
		
		target := &struct {
			Name string `json:"name" validate:"required"`
			Age  int    `json:"age" validate:"min=0,max=120"`
		}{}
		
		handler.ValidateAndBind(c, target)
	}
}

func BenchmarkEchoHandler(b *testing.B) {
	logger, _ := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "error",
		Format: "json",
		Output: "stdout",
	})
	
	handler := examples.NewEchoHandler(logger)
	requestBody := `{"message": "Hello, World!", "echo": true}`
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("POST", "/api/v1/echo", bytes.NewBufferString(requestBody))
		c.Request.Header.Set("Content-Type", "application/json")
		c.Set("request_id", "bench-test")
		
		handler.Handle(c)
	}
}
