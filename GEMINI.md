# 项目: 基于 ai 的文本风格角色扮演游戏

## 一般说明

- 创建新模块或者新的类时需要在 docs 目录下新建中文说明文档, 使用 markdown 格式
- 关键的代码和配置必须有详细的中文说明
- 接口和暴露的方法需要使用方法级别的注释说明功能, 入参出参也需要使用中文注释
- 输出日志采用中文格式

## 工作流程

- 接收到任务时, 分析任务的复杂度, 复杂的任务请先做好规划后再实施, 最后验证
- 编写工具类时, 需要添加完整的单元测试
- 编写测试脚本时, 需要调用后端接口使用 Mock 模式
- 功能实现后总结修改的内容并生成中文描述的 git commit 并提交

## 优先使用以下提供的大模型接口

## 生成文本接口说明

- POST: https://wm.atjog.com/api/w/my-workspace/jobs/run/p/f/gemini/text_generation
- 参数:
  - prompt: 提示词, 必填

## 生成结构化文本接口说明

- POST: https://wm.atjog.com/api/w/my-workspace/jobs/run/p/f/gemini/js_structured_output
- 参数:
  - prompt: 提示词, 必填
  - system_instruction: 系统提示词, 可选
  - responseSchema: 结构化输出格式, 必填
