# API Gateway Makefile

# Variables
APP_NAME := api-gateway
VERSION := 1.0.0
BUILD_DIR := build
BINARY_NAME := gateway
CONFIG_FILE := configs/gateway.yaml

# Go variables
GO := go
GOOS := $(shell go env GOOS)
GOARCH := $(shell go env GOARCH)
GOVERSION := $(shell go version | awk '{print $$3}')

# Build flags
LDFLAGS := -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(shell date -u '+%Y-%m-%d_%H:%M:%S') -X main.GitCommit=$(shell git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"

# Default target
.PHONY: all
all: clean deps build

# Help target
.PHONY: help
help:
	@echo "API Gateway Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  build          Build the application"
	@echo "  run            Run the application"
	@echo "  dev            Run in development mode with auto-reload"
	@echo "  test           Run tests"
	@echo "  test-coverage  Run tests with coverage"
	@echo "  bench          Run benchmarks"
	@echo "  deps           Download dependencies"
	@echo "  tidy           Tidy dependencies"
	@echo "  fmt            Format code"
	@echo "  lint           Run linter"
	@echo "  vet            Run go vet"
	@echo "  clean          Clean build artifacts"
	@echo "  docker-build   Build Docker image"
	@echo "  docker-run     Run Docker container"
	@echo "  install        Install the binary"
	@echo "  uninstall      Uninstall the binary"
	@echo "  release        Create release builds for all platforms"
	@echo "  help           Show this help message"

# Build the application
.PHONY: build
build: deps
	@echo "Building $(APP_NAME) v$(VERSION)..."
	@mkdir -p $(BUILD_DIR)
	$(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) cmd/gateway/main.go
	@echo "Build complete: $(BUILD_DIR)/$(BINARY_NAME)"

# Run the application
.PHONY: run
run: build
	@echo "Starting $(APP_NAME)..."
	./$(BUILD_DIR)/$(BINARY_NAME) -config $(CONFIG_FILE)

# Development mode with auto-reload
.PHONY: dev
dev:
	@echo "Starting development mode..."
	@which air > /dev/null || (echo "Installing air..." && go install github.com/cosmtrek/air@latest)
	air -c .air.toml

# Run tests
.PHONY: test
test:
	@echo "Running tests..."
	$(GO) test -v ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	$(GO) test -v -coverprofile=coverage.out ./...
	$(GO) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Run benchmarks
.PHONY: bench
bench:
	@echo "Running benchmarks..."
	$(GO) test -bench=. -benchmem ./...

# Download dependencies
.PHONY: deps
deps:
	@echo "Downloading dependencies..."
	$(GO) mod download

# Tidy dependencies
.PHONY: tidy
tidy:
	@echo "Tidying dependencies..."
	$(GO) mod tidy

# Format code
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	$(GO) fmt ./...

# Run linter
.PHONY: lint
lint:
	@echo "Running linter..."
	@which golangci-lint > /dev/null || (echo "Installing golangci-lint..." && go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest)
	golangci-lint run

# Run go vet
.PHONY: vet
vet:
	@echo "Running go vet..."
	$(GO) vet ./...

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR)
	rm -f coverage.out coverage.html
	$(GO) clean

# Build Docker image
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest

# Run Docker container
.PHONY: docker-run
docker-run: docker-build
	@echo "Running Docker container..."
	docker run -p 8080:8080 -v $(PWD)/configs:/app/configs $(APP_NAME):latest

# Install the binary
.PHONY: install
install: build
	@echo "Installing $(BINARY_NAME)..."
	sudo cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/
	@echo "Installed to /usr/local/bin/$(BINARY_NAME)"

# Uninstall the binary
.PHONY: uninstall
uninstall:
	@echo "Uninstalling $(BINARY_NAME)..."
	sudo rm -f /usr/local/bin/$(BINARY_NAME)
	@echo "Uninstalled $(BINARY_NAME)"

# Create release builds for all platforms
.PHONY: release
release: clean
	@echo "Creating release builds..."
	@mkdir -p $(BUILD_DIR)/release
	
	# Linux AMD64
	GOOS=linux GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/release/$(BINARY_NAME)-linux-amd64 cmd/gateway/main.go
	
	# Linux ARM64
	GOOS=linux GOARCH=arm64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/release/$(BINARY_NAME)-linux-arm64 cmd/gateway/main.go
	
	# macOS AMD64
	GOOS=darwin GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/release/$(BINARY_NAME)-darwin-amd64 cmd/gateway/main.go
	
	# macOS ARM64
	GOOS=darwin GOARCH=arm64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/release/$(BINARY_NAME)-darwin-arm64 cmd/gateway/main.go
	
	# Windows AMD64
	GOOS=windows GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/release/$(BINARY_NAME)-windows-amd64.exe cmd/gateway/main.go
	
	@echo "Release builds created in $(BUILD_DIR)/release/"

# Generate configuration template
.PHONY: config-template
config-template:
	@echo "Generating configuration template..."
	./$(BUILD_DIR)/$(BINARY_NAME) -generate-config > configs/template.yaml
	@echo "Configuration template generated: configs/template.yaml"

# Validate configuration
.PHONY: config-validate
config-validate:
	@echo "Validating configuration..."
	./$(BUILD_DIR)/$(BINARY_NAME) -config $(CONFIG_FILE) -validate

# Start services (for development)
.PHONY: services-up
services-up:
	@echo "Starting development services..."
	docker-compose -f docker-compose.dev.yml up -d

# Stop services
.PHONY: services-down
services-down:
	@echo "Stopping development services..."
	docker-compose -f docker-compose.dev.yml down

# Show logs
.PHONY: logs
logs:
	@echo "Showing application logs..."
	tail -f logs/gateway.log

# Security scan
.PHONY: security-scan
security-scan:
	@echo "Running security scan..."
	@which gosec > /dev/null || (echo "Installing gosec..." && go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest)
	gosec ./...

# Generate documentation
.PHONY: docs
docs:
	@echo "Generating documentation..."
	@which godoc > /dev/null || (echo "Installing godoc..." && go install golang.org/x/tools/cmd/godoc@latest)
	godoc -http=:6060 &
	@echo "Documentation server started at http://localhost:6060"

# Performance test
.PHONY: perf-test
perf-test:
	@echo "Running performance tests..."
	@which hey > /dev/null || (echo "Please install 'hey' for performance testing: go install github.com/rakyll/hey@latest")
	hey -n 1000 -c 10 http://localhost:8080/health

# Load test
.PHONY: load-test
load-test:
	@echo "Running load tests..."
	@which hey > /dev/null || (echo "Please install 'hey' for load testing: go install github.com/rakyll/hey@latest")
	hey -n 10000 -c 100 -t 30 http://localhost:8080/health

# Check dependencies for vulnerabilities
.PHONY: audit
audit:
	@echo "Auditing dependencies..."
	$(GO) list -json -deps ./... | nancy sleuth

# Show build info
.PHONY: info
info:
	@echo "Build Information:"
	@echo "  App Name:    $(APP_NAME)"
	@echo "  Version:     $(VERSION)"
	@echo "  Go Version:  $(GOVERSION)"
	@echo "  OS/Arch:     $(GOOS)/$(GOARCH)"
	@echo "  Build Dir:   $(BUILD_DIR)"
	@echo "  Config File: $(CONFIG_FILE)"

# Quick development setup
.PHONY: setup
setup: deps build config-validate
	@echo "Development setup complete!"
	@echo "Run 'make run' to start the gateway"
	@echo "Run 'make help' to see all available commands"
