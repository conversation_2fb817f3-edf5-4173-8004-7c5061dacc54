package admin

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
)

// ConfigAPI provides HTTP endpoints for dynamic configuration management
type ConfigAPI struct {
	configManager *config.DynamicConfigManager
	logger        *telemetry.Logger
	metrics       *telemetry.Metrics
}

// NewConfigAPI creates a new configuration API handler
func NewConfigAPI(configManager *config.DynamicConfigManager, logger *telemetry.Logger, metrics *telemetry.Metrics) *ConfigAPI {
	return &ConfigAPI{
		configManager: configManager,
		logger:        logger,
		metrics:       metrics,
	}
}

// RegisterRoutes registers configuration API routes
func (api *ConfigAPI) RegisterRoutes(router *gin.RouterGroup) {
	configGroup := router.Group("/config")
	{
		// Configuration CRUD operations
		configGroup.GET("", api.getConfig)
		configGroup.PUT("", api.updateConfig)
		configGroup.POST("/reload", api.reloadConfig)
		
		// Route management
		routeGroup := configGroup.Group("/routes")
		{
			routeGroup.GET("", api.getRoutes)
			routeGroup.POST("", api.addRoute)
			routeGroup.PUT("/:name", api.updateRoute)
			routeGroup.DELETE("/:name", api.deleteRoute)
		}
		
		// Security configuration
		securityGroup := configGroup.Group("/security")
		{
			securityGroup.GET("", api.getSecurityConfig)
			securityGroup.PUT("/rate-limit", api.updateRateLimit)
			securityGroup.PUT("/cors", api.updateCORS)
			securityGroup.PUT("/waf", api.updateWAF)
			securityGroup.PUT("/ip-filter", api.updateIPFilter)
		}
		
		// Plugin management
		pluginGroup := configGroup.Group("/plugins")
		{
			pluginGroup.GET("", api.getPlugins)
			pluginGroup.PUT("/:name", api.updatePlugin)
			pluginGroup.DELETE("/:name", api.deletePlugin)
		}
		
		// Authentication configuration
		authGroup := configGroup.Group("/auth")
		{
			authGroup.GET("", api.getAuthConfig)
			authGroup.PUT("/jwt", api.updateJWTConfig)
			authGroup.PUT("/api-key", api.updateAPIKeyConfig)
			authGroup.PUT("/oidc", api.updateOIDCConfig)
		}
		
		// Version management
		versionGroup := configGroup.Group("/versions")
		{
			versionGroup.GET("", api.getVersionHistory)
			versionGroup.POST("/rollback/:version", api.rollbackToVersion)
		}
	}
}

// getConfig returns the current configuration
func (api *ConfigAPI) getConfig(c *gin.Context) {
	config := api.configManager.GetConfig()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// updateConfig updates the entire configuration
func (api *ConfigAPI) updateConfig(c *gin.Context) {
	var newConfig config.Config
	if err := c.ShouldBindJSON(&newConfig); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid configuration format",
			"details": err.Error(),
		})
		return
	}
	
	// Create update request
	update := config.ConfigUpdate{
		Type:      config.ConfigUpdateTypeUpdate,
		Section:   "all",
		Key:       "config",
		Value:     &newConfig,
		Version:   "",
		Timestamp: time.Now(),
		Source:    "admin_api",
	}
	
	if err := api.configManager.UpdateConfig(update); err != nil {
		api.logger.Error("Failed to update configuration", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to update configuration",
			"details": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Configuration updated successfully",
	})
}

// reloadConfig reloads configuration from storage
func (api *ConfigAPI) reloadConfig(c *gin.Context) {
	update := config.ConfigUpdate{
		Type:      config.ConfigUpdateTypeReload,
		Section:   "all",
		Key:       "config",
		Timestamp: time.Now(),
		Source:    "admin_api",
	}
	
	if err := api.configManager.UpdateConfig(update); err != nil {
		api.logger.Error("Failed to reload configuration", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to reload configuration",
			"details": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Configuration reloaded successfully",
	})
}

// getRoutes returns all routes
func (api *ConfigAPI) getRoutes(c *gin.Context) {
	config := api.configManager.GetConfig()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config.Routes,
	})
}

// addRoute adds a new route
func (api *ConfigAPI) addRoute(c *gin.Context) {
	var route config.RouteConfig
	if err := c.ShouldBindJSON(&route); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid route configuration",
			"details": err.Error(),
		})
		return
	}
	
	update := config.ConfigUpdate{
		Type:      config.ConfigUpdateTypeAdd,
		Section:   "routes",
		Key:       route.Name,
		Value:     route,
		Timestamp: time.Now(),
		Source:    "admin_api",
	}
	
	if err := api.configManager.UpdateConfig(update); err != nil {
		api.logger.Error("Failed to add route", "error", err, "route", route.Name)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to add route",
			"details": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": fmt.Sprintf("Route '%s' added successfully", route.Name),
	})
}

// updateRoute updates an existing route
func (api *ConfigAPI) updateRoute(c *gin.Context) {
	routeName := c.Param("name")
	
	var route config.RouteConfig
	if err := c.ShouldBindJSON(&route); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid route configuration",
			"details": err.Error(),
		})
		return
	}
	
	// Ensure the route name matches the URL parameter
	route.Name = routeName
	
	update := config.ConfigUpdate{
		Type:      config.ConfigUpdateTypeUpdate,
		Section:   "routes",
		Key:       routeName,
		Value:     route,
		Timestamp: time.Now(),
		Source:    "admin_api",
	}
	
	if err := api.configManager.UpdateConfig(update); err != nil {
		api.logger.Error("Failed to update route", "error", err, "route", routeName)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to update route",
			"details": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Route '%s' updated successfully", routeName),
	})
}

// deleteRoute deletes a route
func (api *ConfigAPI) deleteRoute(c *gin.Context) {
	routeName := c.Param("name")
	
	update := config.ConfigUpdate{
		Type:      config.ConfigUpdateTypeDelete,
		Section:   "routes",
		Key:       routeName,
		Timestamp: time.Now(),
		Source:    "admin_api",
	}
	
	if err := api.configManager.UpdateConfig(update); err != nil {
		api.logger.Error("Failed to delete route", "error", err, "route", routeName)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to delete route",
			"details": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Route '%s' deleted successfully", routeName),
	})
}

// getSecurityConfig returns security configuration
func (api *ConfigAPI) getSecurityConfig(c *gin.Context) {
	config := api.configManager.GetConfig()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config.Security,
	})
}

// updateRateLimit updates rate limiting configuration
func (api *ConfigAPI) updateRateLimit(c *gin.Context) {
	var rateLimitConfig config.RateLimitConfig
	if err := c.ShouldBindJSON(&rateLimitConfig); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid rate limit configuration",
			"details": err.Error(),
		})
		return
	}
	
	update := config.ConfigUpdate{
		Type:      config.ConfigUpdateTypeUpdate,
		Section:   "security",
		Key:       "rate_limit",
		Value:     rateLimitConfig,
		Timestamp: time.Now(),
		Source:    "admin_api",
	}
	
	if err := api.configManager.UpdateConfig(update); err != nil {
		api.logger.Error("Failed to update rate limit configuration", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to update rate limit configuration",
			"details": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Rate limit configuration updated successfully",
	})
}

// updateCORS updates CORS configuration
func (api *ConfigAPI) updateCORS(c *gin.Context) {
	var corsConfig config.CORSConfig
	if err := c.ShouldBindJSON(&corsConfig); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid CORS configuration",
			"details": err.Error(),
		})
		return
	}
	
	update := config.ConfigUpdate{
		Type:      config.ConfigUpdateTypeUpdate,
		Section:   "security",
		Key:       "cors",
		Value:     corsConfig,
		Timestamp: time.Now(),
		Source:    "admin_api",
	}
	
	if err := api.configManager.UpdateConfig(update); err != nil {
		api.logger.Error("Failed to update CORS configuration", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to update CORS configuration",
			"details": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "CORS configuration updated successfully",
	})
}

// updateWAF updates WAF configuration
func (api *ConfigAPI) updateWAF(c *gin.Context) {
	var wafConfig config.WAFConfig
	if err := c.ShouldBindJSON(&wafConfig); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid WAF configuration",
			"details": err.Error(),
		})
		return
	}
	
	update := config.ConfigUpdate{
		Type:      config.ConfigUpdateTypeUpdate,
		Section:   "security",
		Key:       "waf",
		Value:     wafConfig,
		Timestamp: time.Now(),
		Source:    "admin_api",
	}
	
	if err := api.configManager.UpdateConfig(update); err != nil {
		api.logger.Error("Failed to update WAF configuration", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to update WAF configuration",
			"details": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "WAF configuration updated successfully",
	})
}

// updateIPFilter updates IP filter configuration
func (api *ConfigAPI) updateIPFilter(c *gin.Context) {
	var ipFilterConfig config.IPFilterConfig
	if err := c.ShouldBindJSON(&ipFilterConfig); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid IP filter configuration",
			"details": err.Error(),
		})
		return
	}

	update := config.ConfigUpdate{
		Type:      config.ConfigUpdateTypeUpdate,
		Section:   "security",
		Key:       "ip_filter",
		Value:     ipFilterConfig,
		Timestamp: time.Now(),
		Source:    "admin_api",
	}

	if err := api.configManager.UpdateConfig(update); err != nil {
		api.logger.Error("Failed to update IP filter configuration", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to update IP filter configuration",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "IP filter configuration updated successfully",
	})
}

// getPlugins returns plugin configuration
func (api *ConfigAPI) getPlugins(c *gin.Context) {
	config := api.configManager.GetConfig()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config.Plugins,
	})
}

// updatePlugin updates plugin configuration
func (api *ConfigAPI) updatePlugin(c *gin.Context) {
	pluginName := c.Param("name")

	var pluginConfig map[string]interface{}
	if err := c.ShouldBindJSON(&pluginConfig); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid plugin configuration",
			"details": err.Error(),
		})
		return
	}

	update := config.ConfigUpdate{
		Type:      config.ConfigUpdateTypeUpdate,
		Section:   "plugins",
		Key:       pluginName,
		Value:     pluginConfig,
		Timestamp: time.Now(),
		Source:    "admin_api",
	}

	if err := api.configManager.UpdateConfig(update); err != nil {
		api.logger.Error("Failed to update plugin configuration", "error", err, "plugin", pluginName)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to update plugin configuration",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Plugin '%s' configuration updated successfully", pluginName),
	})
}

// deletePlugin deletes plugin configuration
func (api *ConfigAPI) deletePlugin(c *gin.Context) {
	pluginName := c.Param("name")

	update := config.ConfigUpdate{
		Type:      config.ConfigUpdateTypeDelete,
		Section:   "plugins",
		Key:       pluginName,
		Timestamp: time.Now(),
		Source:    "admin_api",
	}

	if err := api.configManager.UpdateConfig(update); err != nil {
		api.logger.Error("Failed to delete plugin configuration", "error", err, "plugin", pluginName)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to delete plugin configuration",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Plugin '%s' configuration deleted successfully", pluginName),
	})
}

// getAuthConfig returns authentication configuration
func (api *ConfigAPI) getAuthConfig(c *gin.Context) {
	config := api.configManager.GetConfig()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config.Auth,
	})
}

// updateJWTConfig updates JWT configuration
func (api *ConfigAPI) updateJWTConfig(c *gin.Context) {
	var jwtConfig config.JWTConfig
	if err := c.ShouldBindJSON(&jwtConfig); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid JWT configuration",
			"details": err.Error(),
		})
		return
	}

	update := config.ConfigUpdate{
		Type:      config.ConfigUpdateTypeUpdate,
		Section:   "auth",
		Key:       "jwt",
		Value:     jwtConfig,
		Timestamp: time.Now(),
		Source:    "admin_api",
	}

	if err := api.configManager.UpdateConfig(update); err != nil {
		api.logger.Error("Failed to update JWT configuration", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to update JWT configuration",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "JWT configuration updated successfully",
	})
}

// updateAPIKeyConfig updates API key configuration
func (api *ConfigAPI) updateAPIKeyConfig(c *gin.Context) {
	var apiKeyConfig config.APIKeyConfig
	if err := c.ShouldBindJSON(&apiKeyConfig); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid API key configuration",
			"details": err.Error(),
		})
		return
	}

	update := config.ConfigUpdate{
		Type:      config.ConfigUpdateTypeUpdate,
		Section:   "auth",
		Key:       "api_key",
		Value:     apiKeyConfig,
		Timestamp: time.Now(),
		Source:    "admin_api",
	}

	if err := api.configManager.UpdateConfig(update); err != nil {
		api.logger.Error("Failed to update API key configuration", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to update API key configuration",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "API key configuration updated successfully",
	})
}

// updateOIDCConfig updates OIDC configuration
func (api *ConfigAPI) updateOIDCConfig(c *gin.Context) {
	var oidcConfig config.OIDCConfig
	if err := c.ShouldBindJSON(&oidcConfig); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid OIDC configuration",
			"details": err.Error(),
		})
		return
	}

	update := config.ConfigUpdate{
		Type:      config.ConfigUpdateTypeUpdate,
		Section:   "auth",
		Key:       "oidc",
		Value:     oidcConfig,
		Timestamp: time.Now(),
		Source:    "admin_api",
	}

	if err := api.configManager.UpdateConfig(update); err != nil {
		api.logger.Error("Failed to update OIDC configuration", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to update OIDC configuration",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "OIDC configuration updated successfully",
	})
}

// getVersionHistory returns configuration version history
func (api *ConfigAPI) getVersionHistory(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}

	history := api.configManager.GetVersionHistory()

	// Limit the results
	if len(history) > limit {
		history = history[len(history)-limit:]
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"versions": history,
			"total":    len(api.configManager.GetVersionHistory()),
		},
	})
}

// rollbackToVersion rolls back to a specific configuration version
func (api *ConfigAPI) rollbackToVersion(c *gin.Context) {
	version := c.Param("version")

	if err := api.configManager.RollbackToVersion(version); err != nil {
		api.logger.Error("Failed to rollback configuration", "error", err, "version", version)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to rollback configuration",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Configuration rolled back to version %s successfully", version),
	})
}
