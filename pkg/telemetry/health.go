package telemetry

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// HealthStatus represents the health status of a component
type HealthStatus string

const (
	HealthStatusHealthy   HealthStatus = "healthy"
	HealthStatusUnhealthy HealthStatus = "unhealthy"
	HealthStatusDegraded  HealthStatus = "degraded"
	HealthStatusUnknown   HealthStatus = "unknown"
)

// HealthCheck represents a health check function
type HealthCheck func(ctx context.Context) HealthCheckResult

// HealthCheckResult represents the result of a health check
type HealthCheckResult struct {
	Status    HealthStatus           `json:"status"`
	Message   string                 `json:"message,omitempty"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Duration  time.Duration          `json:"duration"`
	Error     error                  `json:"error,omitempty"`
}

// HealthChecker manages health checks for various components
type HealthChecker struct {
	checks map[string]HealthCheck
	cache  map[string]HealthCheckResult
	mu     sync.RWMutex
	logger *Logger
	
	// Configuration
	timeout       time.Duration
	cacheTimeout  time.Duration
	checkInterval time.Duration
	
	// Background checking
	stopCh chan struct{}
	wg     sync.WaitGroup
}

// NewHealthChecker creates a new health checker
func NewHealthChecker(logger *Logger) *HealthChecker {
	return &HealthChecker{
		checks:        make(map[string]HealthCheck),
		cache:         make(map[string]HealthCheckResult),
		logger:        logger,
		timeout:       10 * time.Second,
		cacheTimeout:  30 * time.Second,
		checkInterval: 30 * time.Second,
		stopCh:        make(chan struct{}),
	}
}

// RegisterCheck registers a health check for a component
func (hc *HealthChecker) RegisterCheck(name string, check HealthCheck) {
	hc.mu.Lock()
	defer hc.mu.Unlock()
	
	hc.checks[name] = check
	hc.logger.Info("Registered health check", "component", name)
}

// UnregisterCheck unregisters a health check
func (hc *HealthChecker) UnregisterCheck(name string) {
	hc.mu.Lock()
	defer hc.mu.Unlock()
	
	delete(hc.checks, name)
	delete(hc.cache, name)
	hc.logger.Info("Unregistered health check", "component", name)
}

// CheckHealth performs a health check for a specific component
func (hc *HealthChecker) CheckHealth(ctx context.Context, name string) HealthCheckResult {
	hc.mu.RLock()
	check, exists := hc.checks[name]
	hc.mu.RUnlock()
	
	if !exists {
		return HealthCheckResult{
			Status:    HealthStatusUnknown,
			Message:   "Health check not found",
			Timestamp: time.Now(),
		}
	}
	
	// Check cache first
	if cached := hc.getCachedResult(name); cached != nil {
		return *cached
	}
	
	// Perform health check with timeout
	ctx, cancel := context.WithTimeout(ctx, hc.timeout)
	defer cancel()
	
	start := time.Now()
	result := check(ctx)
	result.Duration = time.Since(start)
	result.Timestamp = time.Now()
	
	// Cache the result
	hc.setCachedResult(name, result)
	
	return result
}

// CheckAllHealth performs health checks for all registered components
func (hc *HealthChecker) CheckAllHealth(ctx context.Context) map[string]HealthCheckResult {
	hc.mu.RLock()
	checks := make(map[string]HealthCheck)
	for name, check := range hc.checks {
		checks[name] = check
	}
	hc.mu.RUnlock()
	
	results := make(map[string]HealthCheckResult)
	var wg sync.WaitGroup
	var mu sync.Mutex
	
	for name, check := range checks {
		wg.Add(1)
		go func(name string, check HealthCheck) {
			defer wg.Done()
			
			result := hc.CheckHealth(ctx, name)
			
			mu.Lock()
			results[name] = result
			mu.Unlock()
		}(name, check)
	}
	
	wg.Wait()
	return results
}

// GetOverallHealth returns the overall health status
func (hc *HealthChecker) GetOverallHealth(ctx context.Context) HealthCheckResult {
	results := hc.CheckAllHealth(ctx)
	
	overall := HealthCheckResult{
		Status:    HealthStatusHealthy,
		Timestamp: time.Now(),
		Details:   make(map[string]interface{}),
	}
	
	healthyCount := 0
	unhealthyCount := 0
	degradedCount := 0
	
	for name, result := range results {
		overall.Details[name] = map[string]interface{}{
			"status":   result.Status,
			"message":  result.Message,
			"duration": result.Duration,
		}
		
		switch result.Status {
		case HealthStatusHealthy:
			healthyCount++
		case HealthStatusUnhealthy:
			unhealthyCount++
		case HealthStatusDegraded:
			degradedCount++
		}
	}
	
	// Determine overall status
	if unhealthyCount > 0 {
		overall.Status = HealthStatusUnhealthy
		overall.Message = fmt.Sprintf("%d unhealthy components", unhealthyCount)
	} else if degradedCount > 0 {
		overall.Status = HealthStatusDegraded
		overall.Message = fmt.Sprintf("%d degraded components", degradedCount)
	} else {
		overall.Message = fmt.Sprintf("All %d components healthy", healthyCount)
	}
	
	return overall
}

// StartBackgroundChecking starts background health checking
func (hc *HealthChecker) StartBackgroundChecking() {
	hc.wg.Add(1)
	go func() {
		defer hc.wg.Done()
		
		ticker := time.NewTicker(hc.checkInterval)
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				ctx, cancel := context.WithTimeout(context.Background(), hc.timeout)
				results := hc.CheckAllHealth(ctx)
				cancel()
				
				// Log unhealthy components
				for name, result := range results {
					if result.Status != HealthStatusHealthy {
						hc.logger.Warn("Component unhealthy",
							"component", name,
							"status", result.Status,
							"message", result.Message)
					}
				}
				
			case <-hc.stopCh:
				return
			}
		}
	}()
}

// StopBackgroundChecking stops background health checking
func (hc *HealthChecker) StopBackgroundChecking() {
	close(hc.stopCh)
	hc.wg.Wait()
}

// getCachedResult gets a cached health check result
func (hc *HealthChecker) getCachedResult(name string) *HealthCheckResult {
	hc.mu.RLock()
	defer hc.mu.RUnlock()
	
	if result, exists := hc.cache[name]; exists {
		if time.Since(result.Timestamp) < hc.cacheTimeout {
			return &result
		}
	}
	
	return nil
}

// setCachedResult sets a cached health check result
func (hc *HealthChecker) setCachedResult(name string, result HealthCheckResult) {
	hc.mu.Lock()
	defer hc.mu.Unlock()
	
	hc.cache[name] = result
}

// Common health check functions

// DatabaseHealthCheck creates a health check for database connectivity
func DatabaseHealthCheck(pingFunc func(ctx context.Context) error) HealthCheck {
	return func(ctx context.Context) HealthCheckResult {
		err := pingFunc(ctx)
		if err != nil {
			return HealthCheckResult{
				Status:  HealthStatusUnhealthy,
				Message: "Database connection failed",
				Error:   err,
			}
		}
		
		return HealthCheckResult{
			Status:  HealthStatusHealthy,
			Message: "Database connection successful",
		}
	}
}

// HTTPHealthCheck creates a health check for HTTP endpoints
func HTTPHealthCheck(url string, client interface{}) HealthCheck {
	return func(ctx context.Context) HealthCheckResult {
		// This is a placeholder - implement actual HTTP health check
		return HealthCheckResult{
			Status:  HealthStatusHealthy,
			Message: fmt.Sprintf("HTTP endpoint %s is healthy", url),
		}
	}
}

// MemoryHealthCheck creates a health check for memory usage
func MemoryHealthCheck(maxMemoryMB int64) HealthCheck {
	return func(ctx context.Context) HealthCheckResult {
		// This is a placeholder - implement actual memory check
		return HealthCheckResult{
			Status:  HealthStatusHealthy,
			Message: "Memory usage within limits",
			Details: map[string]interface{}{
				"max_memory_mb": maxMemoryMB,
			},
		}
	}
}

// DiskSpaceHealthCheck creates a health check for disk space
func DiskSpaceHealthCheck(path string, minFreeSpaceGB int64) HealthCheck {
	return func(ctx context.Context) HealthCheckResult {
		// This is a placeholder - implement actual disk space check
		return HealthCheckResult{
			Status:  HealthStatusHealthy,
			Message: "Disk space sufficient",
			Details: map[string]interface{}{
				"path":                path,
				"min_free_space_gb":   minFreeSpaceGB,
			},
		}
	}
}

// ServiceHealthCheck creates a health check for external services
func ServiceHealthCheck(serviceName string, checkFunc func(ctx context.Context) error) HealthCheck {
	return func(ctx context.Context) HealthCheckResult {
		err := checkFunc(ctx)
		if err != nil {
			return HealthCheckResult{
				Status:  HealthStatusUnhealthy,
				Message: fmt.Sprintf("Service %s is unhealthy", serviceName),
				Error:   err,
			}
		}
		
		return HealthCheckResult{
			Status:  HealthStatusHealthy,
			Message: fmt.Sprintf("Service %s is healthy", serviceName),
		}
	}
}
