package telemetry

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"api-gateway/pkg/config"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// Metrics contains all Prometheus metrics
type Metrics struct {
	// HTTP metrics
	RequestsTotal     *prometheus.CounterVec
	RequestDuration   *prometheus.HistogramVec
	RequestSize       *prometheus.HistogramVec
	ResponseSize      *prometheus.HistogramVec
	
	// Gateway metrics
	ActiveConnections prometheus.Gauge
	UpstreamLatency   *prometheus.HistogramVec
	UpstreamErrors    *prometheus.CounterVec
	
	// Security metrics
	AuthFailures      *prometheus.CounterVec
	RateLimitHits     *prometheus.CounterVec
	WAFBlocks         *prometheus.CounterVec
	
	// Circuit breaker metrics
	CircuitBreakerState *prometheus.GaugeVec
	CircuitBreakerTrips *prometheus.CounterVec
	
	// Plugin metrics
	PluginDuration    *prometheus.HistogramVec
	PluginErrors      *prometheus.CounterVec
	
	registry *prometheus.Registry
}

// NewMetrics creates a new metrics instance
func NewMetrics(cfg config.MetricsConfig) (*Metrics, error) {
	if !cfg.Enabled {
		return &Metrics{}, nil
	}

	registry := prometheus.NewRegistry()
	
	m := &Metrics{
		RequestsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gateway_requests_total",
				Help: "Total number of HTTP requests",
			},
			[]string{"method", "path", "status_code", "upstream"},
		),
		
		RequestDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "gateway_request_duration_seconds",
				Help:    "HTTP request duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"method", "path", "status_code", "upstream"},
		),
		
		RequestSize: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "gateway_request_size_bytes",
				Help:    "HTTP request size in bytes",
				Buckets: prometheus.ExponentialBuckets(100, 10, 8),
			},
			[]string{"method", "path"},
		),
		
		ResponseSize: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "gateway_response_size_bytes",
				Help:    "HTTP response size in bytes",
				Buckets: prometheus.ExponentialBuckets(100, 10, 8),
			},
			[]string{"method", "path", "status_code"},
		),
		
		ActiveConnections: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "gateway_active_connections",
				Help: "Number of active connections",
			},
		),
		
		UpstreamLatency: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "gateway_upstream_latency_seconds",
				Help:    "Upstream service latency in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"upstream", "status_code"},
		),
		
		UpstreamErrors: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gateway_upstream_errors_total",
				Help: "Total number of upstream errors",
			},
			[]string{"upstream", "error_type"},
		),
		
		AuthFailures: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gateway_auth_failures_total",
				Help: "Total number of authentication failures",
			},
			[]string{"auth_type", "reason"},
		),
		
		RateLimitHits: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gateway_rate_limit_hits_total",
				Help: "Total number of rate limit hits",
			},
			[]string{"path", "key_type"},
		),
		
		WAFBlocks: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gateway_waf_blocks_total",
				Help: "Total number of WAF blocks",
			},
			[]string{"rule_name", "action"},
		),
		
		CircuitBreakerState: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "gateway_circuit_breaker_state",
				Help: "Circuit breaker state (0=closed, 1=open, 2=half-open)",
			},
			[]string{"upstream"},
		),
		
		CircuitBreakerTrips: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gateway_circuit_breaker_trips_total",
				Help: "Total number of circuit breaker trips",
			},
			[]string{"upstream"},
		),
		
		PluginDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "gateway_plugin_duration_seconds",
				Help:    "Plugin execution duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"plugin_name", "phase"},
		),
		
		PluginErrors: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "gateway_plugin_errors_total",
				Help: "Total number of plugin errors",
			},
			[]string{"plugin_name", "error_type"},
		),
		
		registry: registry,
	}

	// Register all metrics
	registry.MustRegister(
		m.RequestsTotal,
		m.RequestDuration,
		m.RequestSize,
		m.ResponseSize,
		m.ActiveConnections,
		m.UpstreamLatency,
		m.UpstreamErrors,
		m.AuthFailures,
		m.RateLimitHits,
		m.WAFBlocks,
		m.CircuitBreakerState,
		m.CircuitBreakerTrips,
		m.PluginDuration,
		m.PluginErrors,
	)

	return m, nil
}

// RecordRequest records HTTP request metrics
func (m *Metrics) RecordRequest(method, path, statusCode, upstream string, duration time.Duration, requestSize, responseSize int64) {
	if m.RequestsTotal == nil {
		return
	}
	
	m.RequestsTotal.WithLabelValues(method, path, statusCode, upstream).Inc()
	m.RequestDuration.WithLabelValues(method, path, statusCode, upstream).Observe(duration.Seconds())
	m.RequestSize.WithLabelValues(method, path).Observe(float64(requestSize))
	m.ResponseSize.WithLabelValues(method, path, statusCode).Observe(float64(responseSize))
}

// RecordUpstreamLatency records upstream service latency
func (m *Metrics) RecordUpstreamLatency(upstream, statusCode string, duration time.Duration) {
	if m.UpstreamLatency == nil {
		return
	}
	m.UpstreamLatency.WithLabelValues(upstream, statusCode).Observe(duration.Seconds())
}

// RecordUpstreamError records upstream service errors
func (m *Metrics) RecordUpstreamError(upstream, errorType string) {
	if m.UpstreamErrors == nil {
		return
	}
	m.UpstreamErrors.WithLabelValues(upstream, errorType).Inc()
}

// RecordAuthFailure records authentication failures
func (m *Metrics) RecordAuthFailure(authType, reason string) {
	if m.AuthFailures == nil {
		return
	}
	m.AuthFailures.WithLabelValues(authType, reason).Inc()
}

// RecordRateLimitHit records rate limit hits
func (m *Metrics) RecordRateLimitHit(path, keyType string) {
	if m.RateLimitHits == nil {
		return
	}
	m.RateLimitHits.WithLabelValues(path, keyType).Inc()
}

// RecordWAFBlock records WAF blocks
func (m *Metrics) RecordWAFBlock(ruleName, action string) {
	if m.WAFBlocks == nil {
		return
	}
	m.WAFBlocks.WithLabelValues(ruleName, action).Inc()
}

// SetCircuitBreakerState sets circuit breaker state
func (m *Metrics) SetCircuitBreakerState(upstream string, state int) {
	if m.CircuitBreakerState == nil {
		return
	}
	m.CircuitBreakerState.WithLabelValues(upstream).Set(float64(state))
}

// RecordCircuitBreakerTrip records circuit breaker trips
func (m *Metrics) RecordCircuitBreakerTrip(upstream string) {
	if m.CircuitBreakerTrips == nil {
		return
	}
	m.CircuitBreakerTrips.WithLabelValues(upstream).Inc()
}

// RecordPluginDuration records plugin execution duration
func (m *Metrics) RecordPluginDuration(pluginName, phase string, duration time.Duration) {
	if m.PluginDuration == nil {
		return
	}
	m.PluginDuration.WithLabelValues(pluginName, phase).Observe(duration.Seconds())
}

// RecordPluginError records plugin errors
func (m *Metrics) RecordPluginError(pluginName, errorType string) {
	if m.PluginErrors == nil {
		return
	}
	m.PluginErrors.WithLabelValues(pluginName, errorType).Inc()
}

// IncActiveConnections increments active connections counter
func (m *Metrics) IncActiveConnections() {
	if m.ActiveConnections == nil {
		return
	}
	m.ActiveConnections.Inc()
}

// DecActiveConnections decrements active connections counter
func (m *Metrics) DecActiveConnections() {
	if m.ActiveConnections == nil {
		return
	}
	m.ActiveConnections.Dec()
}

// Handler returns the Prometheus metrics HTTP handler
func (m *Metrics) Handler() http.Handler {
	if m.registry == nil {
		return http.NotFoundHandler()
	}
	return promhttp.HandlerFor(m.registry, promhttp.HandlerOpts{})
}

// StartMetricsServer starts the metrics HTTP server
func (m *Metrics) StartMetricsServer(cfg config.MetricsConfig) error {
	if !cfg.Enabled {
		return nil
	}

	mux := http.NewServeMux()
	mux.Handle(cfg.Path, m.Handler())
	
	server := &http.Server{
		Addr:    ":" + strconv.Itoa(cfg.Port),
		Handler: mux,
	}

	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("Metrics server error: %v\n", err)
		}
	}()

	return nil
}
