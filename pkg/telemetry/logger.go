package telemetry

import (
	"fmt"
	"os"

	"api-gateway/pkg/config"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Logger wraps zap.Logger with additional functionality
type Logger struct {
	*zap.Logger
}

// NewLogger creates a new logger instance based on configuration
func NewLogger(cfg config.LoggingConfig) (*Logger, error) {
	// Configure log level
	level, err := zapcore.ParseLevel(cfg.Level)
	if err != nil {
		return nil, fmt.Errorf("invalid log level: %w", err)
	}

	// Configure encoder
	var encoder zapcore.Encoder
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "timestamp"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	if cfg.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// Configure output
	var writeSyncer zapcore.WriteSyncer
	if cfg.Output == "file" {
		// Use lumberjack for log rotation
		lumberJackLogger := &lumberjack.Logger{
			Filename:   cfg.File,
			MaxSize:    cfg.MaxSize,
			MaxBackups: cfg.MaxBackups,
			MaxAge:     cfg.MaxAge,
			Compress:   true,
		}
		writeSyncer = zapcore.AddSync(lumberJackLogger)
	} else {
		writeSyncer = zapcore.AddSync(os.Stdout)
	}

	// Create core
	core := zapcore.NewCore(encoder, writeSyncer, level)

	// Create logger with caller information
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	return &Logger{Logger: logger}, nil
}

// Info logs an info message with structured fields
func (l *Logger) Info(msg string, fields ...interface{}) {
	l.Logger.Info(msg, l.convertFields(fields...)...)
}

// Error logs an error message with structured fields
func (l *Logger) Error(msg string, fields ...interface{}) {
	l.Logger.Error(msg, l.convertFields(fields...)...)
}

// Warn logs a warning message with structured fields
func (l *Logger) Warn(msg string, fields ...interface{}) {
	l.Logger.Warn(msg, l.convertFields(fields...)...)
}

// Debug logs a debug message with structured fields
func (l *Logger) Debug(msg string, fields ...interface{}) {
	l.Logger.Debug(msg, l.convertFields(fields...)...)
}

// Fatal logs a fatal message with structured fields and exits
func (l *Logger) Fatal(msg string, fields ...interface{}) {
	l.Logger.Fatal(msg, l.convertFields(fields...)...)
}

// With creates a child logger with additional fields
func (l *Logger) With(fields ...interface{}) *Logger {
	return &Logger{Logger: l.Logger.With(l.convertFields(fields...)...)}
}

// convertFields converts key-value pairs to zap fields
func (l *Logger) convertFields(fields ...interface{}) []zap.Field {
	if len(fields)%2 != 0 {
		// If odd number of fields, add the last one as a string
		fields = append(fields, "")
	}

	zapFields := make([]zap.Field, 0, len(fields)/2)
	for i := 0; i < len(fields); i += 2 {
		key, ok := fields[i].(string)
		if !ok {
			key = fmt.Sprintf("field_%d", i/2)
		}
		value := fields[i+1]
		zapFields = append(zapFields, zap.Any(key, value))
	}

	return zapFields
}

// RequestLogger creates a logger for HTTP request logging
func (l *Logger) RequestLogger() *Logger {
	return &Logger{Logger: l.Logger.Named("request")}
}

// SecurityLogger creates a logger for security events
func (l *Logger) SecurityLogger() *Logger {
	return &Logger{Logger: l.Logger.Named("security")}
}

// AuditLogger creates a logger for audit events
func (l *Logger) AuditLogger() *Logger {
	return &Logger{Logger: l.Logger.Named("audit")}
}
