# Gateway Core Package

This package contains the core functionality of the API Gateway, including routing, request processing, and response handling.

## Architecture

The gateway core follows a modular architecture with the following components:

1. **Router**: Advanced routing with pattern matching and parameter extraction
2. **Request Processor**: Request validation, transformation, and enrichment
3. **Response Handler**: Response formatting, transformation, and caching
4. **Context Manager**: Request context management and propagation
5. **Pipeline**: Request/response processing pipeline

## Structure

```
pkg/gateway/
├── README.md           # This file
├── router.go           # Advanced routing functionality
├── processor.go        # Request/response processing
├── context.go          # Request context management
├── pipeline.go         # Processing pipeline
├── matcher.go          # Route matching logic
├── transformer.go      # Request/response transformation
└── examples/           # Example implementations
    ├── simple.go       # Simple gateway example
    ├── advanced.go     # Advanced features example
    └── custom.go       # Custom processing example
```

## Features

- **Advanced Routing**: Pattern matching, parameter extraction, method filtering
- **Request Processing**: Validation, transformation, enrichment
- **Response Handling**: Formatting, caching, compression
- **Context Management**: Request tracing, user context, metadata
- **Pipeline Processing**: Configurable processing stages
- **Error Handling**: Comprehensive error handling and recovery
- **Performance**: Optimized for high throughput and low latency

## Usage

### Basic Gateway Setup

```go
// Create gateway instance
gw := gateway.New(config, logger)

// Add routes
gw.AddRoute("/api/v1/users/*", "user-service", gateway.RouteOptions{
    Methods: []string{"GET", "POST", "PUT", "DELETE"},
    Auth:    true,
    RateLimit: &gateway.RateLimit{
        Rate:  100,
        Burst: 200,
    },
})

// Start processing
gw.Start()
```

### Custom Processing Pipeline

```go
// Create custom pipeline
pipeline := gateway.NewPipeline()

// Add processing stages
pipeline.AddStage("validation", &ValidationStage{})
pipeline.AddStage("auth", &AuthStage{})
pipeline.AddStage("transform", &TransformStage{})
pipeline.AddStage("proxy", &ProxyStage{})

// Use pipeline in gateway
gw.SetPipeline(pipeline)
```

### Request Context

```go
// Create request context
ctx := gateway.NewRequestContext(request)

// Add metadata
ctx.SetMetadata("user_id", "12345")
ctx.SetMetadata("client_ip", "***********")

// Process request
response, err := gw.ProcessRequest(ctx)
```

## Components

### Router
- Pattern-based routing with wildcards and parameters
- Method-based filtering
- Priority-based route matching
- Dynamic route registration

### Processor
- Request validation and sanitization
- Content transformation
- Header manipulation
- Body processing

### Context
- Request lifecycle management
- Metadata propagation
- Tracing integration
- Error context

### Pipeline
- Configurable processing stages
- Stage ordering and dependencies
- Error handling and recovery
- Performance monitoring

## Best Practices

1. **Route Organization**: Organize routes logically with clear patterns
2. **Context Usage**: Use context for request-scoped data
3. **Error Handling**: Implement comprehensive error handling
4. **Performance**: Monitor and optimize processing stages
5. **Security**: Validate and sanitize all inputs
6. **Logging**: Log important operations and errors
7. **Testing**: Write comprehensive tests for all components
