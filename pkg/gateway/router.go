package gateway

import (
	"fmt"
	"net/http"
	"regexp"
	"sort"
	"strings"
	"sync"

	"api-gateway/pkg/telemetry"
)

// Router handles advanced routing functionality
type Router struct {
	routes  []*Route
	mu      sync.RWMutex
	logger  *telemetry.Logger
	metrics *telemetry.Metrics
}

// Route represents a gateway route
type Route struct {
	ID          string            `json:"id"`
	Pattern     string            `json:"pattern"`
	Methods     []string          `json:"methods"`
	Upstream    string            `json:"upstream"`
	Priority    int               `json:"priority"`
	Middleware  []string          `json:"middleware"`
	Options     *RouteOptions     `json:"options"`
	Metadata    map[string]string `json:"metadata"`
	
	// Compiled pattern for matching
	regex       *regexp.Regexp
	paramNames  []string
}

// RouteOptions contains route-specific options
type RouteOptions struct {
	Auth        bool              `json:"auth"`
	RateLimit   *RateLimit        `json:"rate_limit,omitempty"`
	Timeout     int               `json:"timeout"` // seconds
	Retries     int               `json:"retries"`
	Cache       *CacheOptions     `json:"cache,omitempty"`
	Transform   *TransformOptions `json:"transform,omitempty"`
	Headers     map[string]string `json:"headers,omitempty"`
	CORS        *CORSOptions      `json:"cors,omitempty"`
}

// RateLimit defines rate limiting options
type RateLimit struct {
	Rate  int `json:"rate"`  // requests per second
	Burst int `json:"burst"` // burst capacity
}

// CacheOptions defines caching options
type CacheOptions struct {
	Enabled bool   `json:"enabled"`
	TTL     int    `json:"ttl"` // seconds
	Key     string `json:"key"` // cache key pattern
}

// TransformOptions defines transformation options
type TransformOptions struct {
	Request  *TransformConfig `json:"request,omitempty"`
	Response *TransformConfig `json:"response,omitempty"`
}

// TransformConfig defines transformation configuration
type TransformConfig struct {
	Headers map[string]string `json:"headers,omitempty"`
	Body    *BodyTransform    `json:"body,omitempty"`
}

// BodyTransform defines body transformation
type BodyTransform struct {
	Template string            `json:"template,omitempty"`
	Fields   map[string]string `json:"fields,omitempty"`
}

// CORSOptions defines CORS options
type CORSOptions struct {
	AllowedOrigins   []string `json:"allowed_origins"`
	AllowedMethods   []string `json:"allowed_methods"`
	AllowedHeaders   []string `json:"allowed_headers"`
	AllowCredentials bool     `json:"allow_credentials"`
	MaxAge           int      `json:"max_age"`
}

// RouteMatch represents a matched route with extracted parameters
type RouteMatch struct {
	Route  *Route            `json:"route"`
	Params map[string]string `json:"params"`
	Path   string            `json:"path"`
}

// NewRouter creates a new router instance
func NewRouter(logger *telemetry.Logger, metrics *telemetry.Metrics) *Router {
	return &Router{
		routes:  make([]*Route, 0),
		logger:  logger,
		metrics: metrics,
	}
}

// AddRoute adds a new route to the router
func (r *Router) AddRoute(route *Route) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	// Compile the pattern
	if err := r.compileRoute(route); err != nil {
		return fmt.Errorf("failed to compile route pattern: %w", err)
	}
	
	// Check for duplicate routes
	for _, existing := range r.routes {
		if existing.ID == route.ID {
			return fmt.Errorf("route with ID %s already exists", route.ID)
		}
	}
	
	r.routes = append(r.routes, route)
	
	// Sort routes by priority (higher priority first)
	sort.Slice(r.routes, func(i, j int) bool {
		return r.routes[i].Priority > r.routes[j].Priority
	})
	
	r.logger.Info("Route added", 
		"id", route.ID,
		"pattern", route.Pattern,
		"methods", route.Methods,
		"upstream", route.Upstream)
	
	return nil
}

// RemoveRoute removes a route by ID
func (r *Router) RemoveRoute(routeID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	for i, route := range r.routes {
		if route.ID == routeID {
			r.routes = append(r.routes[:i], r.routes[i+1:]...)
			r.logger.Info("Route removed", "id", routeID)
			return nil
		}
	}
	
	return fmt.Errorf("route with ID %s not found", routeID)
}

// UpdateRoute updates an existing route
func (r *Router) UpdateRoute(route *Route) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	// Compile the pattern
	if err := r.compileRoute(route); err != nil {
		return fmt.Errorf("failed to compile route pattern: %w", err)
	}
	
	for i, existing := range r.routes {
		if existing.ID == route.ID {
			r.routes[i] = route
			
			// Re-sort routes by priority
			sort.Slice(r.routes, func(i, j int) bool {
				return r.routes[i].Priority > r.routes[j].Priority
			})
			
			r.logger.Info("Route updated", "id", route.ID)
			return nil
		}
	}
	
	return fmt.Errorf("route with ID %s not found", route.ID)
}

// Match finds the best matching route for a request
func (r *Router) Match(method, path string) (*RouteMatch, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	for _, route := range r.routes {
		// Check method
		if !r.methodMatches(method, route.Methods) {
			continue
		}
		
		// Check path pattern
		if matches := route.regex.FindStringSubmatch(path); matches != nil {
			params := make(map[string]string)
			
			// Extract parameters
			for i, name := range route.paramNames {
				if i+1 < len(matches) {
					params[name] = matches[i+1]
				}
			}
			
			r.logger.Debug("Route matched",
				"route_id", route.ID,
				"method", method,
				"path", path,
				"params", params)
			
			return &RouteMatch{
				Route:  route,
				Params: params,
				Path:   path,
			}, nil
		}
	}
	
	return nil, fmt.Errorf("no matching route found for %s %s", method, path)
}

// GetRoutes returns all registered routes
func (r *Router) GetRoutes() []*Route {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	routes := make([]*Route, len(r.routes))
	copy(routes, r.routes)
	return routes
}

// GetRoute returns a route by ID
func (r *Router) GetRoute(routeID string) (*Route, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	for _, route := range r.routes {
		if route.ID == routeID {
			return route, nil
		}
	}
	
	return nil, fmt.Errorf("route with ID %s not found", routeID)
}

// compileRoute compiles a route pattern into a regular expression
func (r *Router) compileRoute(route *Route) error {
	pattern := route.Pattern
	paramNames := make([]string, 0)
	
	// Convert gateway pattern to regex
	// Support for:
	// - /api/v1/users/{id} -> named parameters
	// - /api/v1/users/* -> wildcard
	// - /api/v1/users/** -> recursive wildcard
	
	// Escape special regex characters except our patterns
	pattern = regexp.MustCompile(`[.+?^${}()|[\]\\]`).ReplaceAllStringFunc(pattern, func(s string) string {
		if s == "{" || s == "}" || s == "*" {
			return s
		}
		return "\\" + s
	})
	
	// Replace named parameters {name} with regex groups
	paramRegex := regexp.MustCompile(`\{([^}]+)\}`)
	pattern = paramRegex.ReplaceAllStringFunc(pattern, func(match string) string {
		paramName := match[1 : len(match)-1] // Remove { and }
		paramNames = append(paramNames, paramName)
		return `([^/]+)` // Match any character except /
	})
	
	// Replace wildcards
	pattern = strings.ReplaceAll(pattern, "/**", `(/.*)?`) // Recursive wildcard (optional)
	pattern = strings.ReplaceAll(pattern, "/*", `/[^/]*`)  // Single level wildcard
	pattern = strings.ReplaceAll(pattern, "*", `[^/]*`)    // Wildcard within segment
	
	// Anchor the pattern
	pattern = "^" + pattern + "$"
	
	// Compile regex
	regex, err := regexp.Compile(pattern)
	if err != nil {
		return fmt.Errorf("invalid route pattern %s: %w", route.Pattern, err)
	}
	
	route.regex = regex
	route.paramNames = paramNames
	
	return nil
}

// methodMatches checks if the request method matches the route methods
func (r *Router) methodMatches(method string, routeMethods []string) bool {
	if len(routeMethods) == 0 {
		return true // No method restriction
	}
	
	for _, routeMethod := range routeMethods {
		if routeMethod == "*" || strings.EqualFold(routeMethod, method) {
			return true
		}
	}
	
	return false
}

// ValidateRoute validates a route configuration
func (r *Router) ValidateRoute(route *Route) error {
	if route.ID == "" {
		return fmt.Errorf("route ID is required")
	}
	
	if route.Pattern == "" {
		return fmt.Errorf("route pattern is required")
	}
	
	if route.Upstream == "" {
		return fmt.Errorf("route upstream is required")
	}
	
	// Validate methods
	validMethods := map[string]bool{
		"GET": true, "POST": true, "PUT": true, "DELETE": true,
		"PATCH": true, "HEAD": true, "OPTIONS": true, "*": true,
	}
	
	for _, method := range route.Methods {
		if !validMethods[strings.ToUpper(method)] {
			return fmt.Errorf("invalid HTTP method: %s", method)
		}
	}
	
	// Try to compile the pattern
	testRoute := &Route{
		Pattern: route.Pattern,
	}
	
	if err := r.compileRoute(testRoute); err != nil {
		return fmt.Errorf("invalid route pattern: %w", err)
	}
	
	return nil
}

// Stats returns router statistics
func (r *Router) Stats() map[string]interface{} {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	stats := map[string]interface{}{
		"total_routes": len(r.routes),
		"routes_by_method": make(map[string]int),
	}
	
	methodCounts := make(map[string]int)
	for _, route := range r.routes {
		for _, method := range route.Methods {
			methodCounts[method]++
		}
	}
	
	stats["routes_by_method"] = methodCounts
	return stats
}
