package security

import (
	"fmt"
	"net"
	"strings"
	"sync"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// IPFilter interface defines IP filtering methods
type IPFilter interface {
	IsAllowed(clientIP string) (bool, error)
	GetStats() map[string]interface{}
}

// BasicIPFilter implements basic IP filtering
type BasicIPFilter struct {
	config config.IPFilterConfig
	logger *telemetry.Logger
	
	// Compiled IP ranges
	whitelist []*net.IPNet
	blacklist []*net.IPNet
	mu        sync.RWMutex
	
	// Statistics
	stats struct {
		totalRequests   int64
		allowedRequests int64
		blockedRequests int64
		mu              sync.RWMutex
	}
}

// NewIPFilter creates a new IP filter
func NewIPFilter(cfg config.IPFilterConfig, logger *telemetry.Logger) (IPFilter, error) {
	filter := &BasicIPFilter{
		config: cfg,
		logger: logger,
	}

	// Parse IP ranges
	if err := filter.parseIPRanges(); err != nil {
		return nil, fmt.Errorf("failed to parse IP ranges: %w", err)
	}

	return filter, nil
}

// parseIPRanges parses whitelist and blacklist IP ranges
func (f *BasicIPFilter) parseIPRanges() error {
	f.mu.Lock()
	defer f.mu.Unlock()

	// Parse whitelist
	f.whitelist = make([]*net.IPNet, 0, len(f.config.Whitelist))
	for _, ipRange := range f.config.Whitelist {
		ipNet, err := f.parseIPRange(ipRange)
		if err != nil {
			return fmt.Errorf("failed to parse whitelist IP range %s: %w", ipRange, err)
		}
		f.whitelist = append(f.whitelist, ipNet)
	}

	// Parse blacklist
	f.blacklist = make([]*net.IPNet, 0, len(f.config.Blacklist))
	for _, ipRange := range f.config.Blacklist {
		ipNet, err := f.parseIPRange(ipRange)
		if err != nil {
			return fmt.Errorf("failed to parse blacklist IP range %s: %w", ipRange, err)
		}
		f.blacklist = append(f.blacklist, ipNet)
	}

	f.logger.Info("Parsed IP ranges",
		"whitelist_count", len(f.whitelist),
		"blacklist_count", len(f.blacklist))

	return nil
}

// parseIPRange parses a single IP range (IP address or CIDR)
func (f *BasicIPFilter) parseIPRange(ipRange string) (*net.IPNet, error) {
	// Handle single IP address
	if !strings.Contains(ipRange, "/") {
		ip := net.ParseIP(ipRange)
		if ip == nil {
			return nil, fmt.Errorf("invalid IP address: %s", ipRange)
		}

		// Convert to CIDR
		if ip.To4() != nil {
			ipRange += "/32" // IPv4
		} else {
			ipRange += "/128" // IPv6
		}
	}

	// Parse CIDR
	_, ipNet, err := net.ParseCIDR(ipRange)
	if err != nil {
		return nil, fmt.Errorf("invalid CIDR: %s", ipRange)
	}

	return ipNet, nil
}

// IsAllowed checks if an IP address is allowed
func (f *BasicIPFilter) IsAllowed(clientIP string) (bool, error) {
	// Update statistics
	f.stats.mu.Lock()
	f.stats.totalRequests++
	f.stats.mu.Unlock()

	// Parse client IP
	ip := net.ParseIP(clientIP)
	if ip == nil {
		f.logger.Warn("Invalid client IP address", "ip", clientIP)
		f.stats.mu.Lock()
		f.stats.blockedRequests++
		f.stats.mu.Unlock()
		return false, fmt.Errorf("invalid IP address: %s", clientIP)
	}

	f.mu.RLock()
	defer f.mu.RUnlock()

	// Check blacklist first
	for _, ipNet := range f.blacklist {
		if ipNet.Contains(ip) {
			f.logger.Warn("IP blocked by blacklist", "ip", clientIP, "range", ipNet.String())
			f.stats.mu.Lock()
			f.stats.blockedRequests++
			f.stats.mu.Unlock()
			return false, nil
		}
	}

	// If whitelist is empty, allow all (except blacklisted)
	if len(f.whitelist) == 0 {
		f.stats.mu.Lock()
		f.stats.allowedRequests++
		f.stats.mu.Unlock()
		return true, nil
	}

	// Check whitelist
	for _, ipNet := range f.whitelist {
		if ipNet.Contains(ip) {
			f.logger.Debug("IP allowed by whitelist", "ip", clientIP, "range", ipNet.String())
			f.stats.mu.Lock()
			f.stats.allowedRequests++
			f.stats.mu.Unlock()
			return true, nil
		}
	}

	// Not in whitelist, block
	f.logger.Warn("IP not in whitelist", "ip", clientIP)
	f.stats.mu.Lock()
	f.stats.blockedRequests++
	f.stats.mu.Unlock()
	return false, nil
}

// AddToWhitelist adds an IP range to the whitelist
func (f *BasicIPFilter) AddToWhitelist(ipRange string) error {
	ipNet, err := f.parseIPRange(ipRange)
	if err != nil {
		return fmt.Errorf("failed to parse IP range: %w", err)
	}

	f.mu.Lock()
	defer f.mu.Unlock()

	f.whitelist = append(f.whitelist, ipNet)
	f.logger.Info("Added IP range to whitelist", "range", ipRange)

	return nil
}

// AddToBlacklist adds an IP range to the blacklist
func (f *BasicIPFilter) AddToBlacklist(ipRange string) error {
	ipNet, err := f.parseIPRange(ipRange)
	if err != nil {
		return fmt.Errorf("failed to parse IP range: %w", err)
	}

	f.mu.Lock()
	defer f.mu.Unlock()

	f.blacklist = append(f.blacklist, ipNet)
	f.logger.Info("Added IP range to blacklist", "range", ipRange)

	return nil
}

// RemoveFromWhitelist removes an IP range from the whitelist
func (f *BasicIPFilter) RemoveFromWhitelist(ipRange string) error {
	ipNet, err := f.parseIPRange(ipRange)
	if err != nil {
		return fmt.Errorf("failed to parse IP range: %w", err)
	}

	f.mu.Lock()
	defer f.mu.Unlock()

	for i, existing := range f.whitelist {
		if existing.String() == ipNet.String() {
			f.whitelist = append(f.whitelist[:i], f.whitelist[i+1:]...)
			f.logger.Info("Removed IP range from whitelist", "range", ipRange)
			return nil
		}
	}

	return fmt.Errorf("IP range not found in whitelist: %s", ipRange)
}

// RemoveFromBlacklist removes an IP range from the blacklist
func (f *BasicIPFilter) RemoveFromBlacklist(ipRange string) error {
	ipNet, err := f.parseIPRange(ipRange)
	if err != nil {
		return fmt.Errorf("failed to parse IP range: %w", err)
	}

	f.mu.Lock()
	defer f.mu.Unlock()

	for i, existing := range f.blacklist {
		if existing.String() == ipNet.String() {
			f.blacklist = append(f.blacklist[:i], f.blacklist[i+1:]...)
			f.logger.Info("Removed IP range from blacklist", "range", ipRange)
			return nil
		}
	}

	return fmt.Errorf("IP range not found in blacklist: %s", ipRange)
}

// GetWhitelist returns the current whitelist
func (f *BasicIPFilter) GetWhitelist() []string {
	f.mu.RLock()
	defer f.mu.RUnlock()

	whitelist := make([]string, len(f.whitelist))
	for i, ipNet := range f.whitelist {
		whitelist[i] = ipNet.String()
	}

	return whitelist
}

// GetBlacklist returns the current blacklist
func (f *BasicIPFilter) GetBlacklist() []string {
	f.mu.RLock()
	defer f.mu.RUnlock()

	blacklist := make([]string, len(f.blacklist))
	for i, ipNet := range f.blacklist {
		blacklist[i] = ipNet.String()
	}

	return blacklist
}

// ClearWhitelist clears the whitelist
func (f *BasicIPFilter) ClearWhitelist() {
	f.mu.Lock()
	defer f.mu.Unlock()

	f.whitelist = make([]*net.IPNet, 0)
	f.logger.Info("Cleared whitelist")
}

// ClearBlacklist clears the blacklist
func (f *BasicIPFilter) ClearBlacklist() {
	f.mu.Lock()
	defer f.mu.Unlock()

	f.blacklist = make([]*net.IPNet, 0)
	f.logger.Info("Cleared blacklist")
}

// GetStats returns IP filter statistics
func (f *BasicIPFilter) GetStats() map[string]interface{} {
	f.stats.mu.RLock()
	defer f.stats.mu.RUnlock()

	f.mu.RLock()
	whitelistCount := len(f.whitelist)
	blacklistCount := len(f.blacklist)
	f.mu.RUnlock()

	return map[string]interface{}{
		"total_requests":   f.stats.totalRequests,
		"allowed_requests": f.stats.allowedRequests,
		"blocked_requests": f.stats.blockedRequests,
		"whitelist_count":  whitelistCount,
		"blacklist_count":  blacklistCount,
	}
}

// IsPrivateIP checks if an IP address is in a private range
func IsPrivateIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// Private IPv4 ranges
	privateRanges := []string{
		"10.0.0.0/8",
		"**********/12",
		"***********/16",
		"*********/8", // Loopback
	}

	for _, cidr := range privateRanges {
		_, ipNet, err := net.ParseCIDR(cidr)
		if err != nil {
			continue
		}
		if ipNet.Contains(parsedIP) {
			return true
		}
	}

	return false
}

// IsLoopbackIP checks if an IP address is a loopback address
func IsLoopbackIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	return parsedIP.IsLoopback()
}
