package security

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"

	"golang.org/x/time/rate"
)

// RateLimiter interface defines rate limiting methods
type RateLimiter interface {
	IsAllowed(clientIP, path, method, keyType string) (bool, error)
	GetStats() map[string]interface{}
}

// TokenBucketLimiter implements token bucket rate limiting
type TokenBucketLimiter struct {
	config  config.RateLimitConfig
	logger  *telemetry.Logger
	metrics *telemetry.Metrics
	
	// Rate limiters for different keys
	limiters map[string]*rate.Limiter
	mu       sync.RWMutex
	
	// Statistics
	stats struct {
		totalRequests   int64
		allowedRequests int64
		blockedRequests int64
		mu              sync.RWMutex
	}
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(cfg config.RateLimitConfig, logger *telemetry.Logger, metrics *telemetry.Metrics) (RateLimiter, error) {
	switch cfg.Algorithm {
	case "token_bucket":
		return NewTokenBucketLimiter(cfg, logger, metrics)
	case "leaky_bucket":
		return NewLeakyBucketLimiter(cfg, logger, metrics)
	default:
		return nil, fmt.Errorf("unsupported rate limiting algorithm: %s", cfg.Algorithm)
	}
}

// NewTokenBucketLimiter creates a new token bucket rate limiter
func NewTokenBucketLimiter(cfg config.RateLimitConfig, logger *telemetry.Logger, metrics *telemetry.Metrics) (*TokenBucketLimiter, error) {
	limiter := &TokenBucketLimiter{
		config:   cfg,
		logger:   logger,
		metrics:  metrics,
		limiters: make(map[string]*rate.Limiter),
	}

	// Start cleanup goroutine
	go limiter.cleanup()

	return limiter, nil
}

// IsAllowed checks if a request is allowed by rate limiting
func (t *TokenBucketLimiter) IsAllowed(clientIP, path, method, keyType string) (bool, error) {
	// Update statistics
	t.stats.mu.Lock()
	t.stats.totalRequests++
	t.stats.mu.Unlock()

	// Find matching rule
	rule := t.findMatchingRule(path, method)
	if rule == nil {
		// No rule found, allow request
		t.stats.mu.Lock()
		t.stats.allowedRequests++
		t.stats.mu.Unlock()
		return true, nil
	}

	// Generate key based on rule configuration
	key := t.generateKey(rule, clientIP, keyType)

	// Get or create limiter for this key
	limiter := t.getLimiter(key, rule)

	// Check if request is allowed
	allowed := limiter.Allow()

	// Update statistics and metrics
	if allowed {
		t.stats.mu.Lock()
		t.stats.allowedRequests++
		t.stats.mu.Unlock()
	} else {
		t.stats.mu.Lock()
		t.stats.blockedRequests++
		t.stats.mu.Unlock()

		if t.metrics != nil {
			t.metrics.RecordRateLimitHit(path, rule.KeyBy)
		}

		t.logger.Warn("Rate limit exceeded",
			"key", key,
			"path", path,
			"method", method,
			"client_ip", clientIP,
			"rate", rule.Rate,
			"burst", rule.Burst)
	}

	return allowed, nil
}

// findMatchingRule finds the first matching rate limit rule
func (t *TokenBucketLimiter) findMatchingRule(path, method string) *config.RateLimitRule {
	for _, rule := range t.config.Rules {
		if t.matchesRule(rule, path, method) {
			return &rule
		}
	}
	return nil
}

// matchesRule checks if a request matches a rate limit rule
func (t *TokenBucketLimiter) matchesRule(rule config.RateLimitRule, path, method string) bool {
	// Match path
	if !t.matchesPath(rule.Path, path) {
		return false
	}

	// Match method
	if !t.matchesMethod(rule.Method, method) {
		return false
	}

	return true
}

// matchesPath checks if path matches the rule pattern
func (t *TokenBucketLimiter) matchesPath(rulePath, requestPath string) bool {
	// Exact match
	if rulePath == requestPath {
		return true
	}

	// Wildcard match
	if strings.HasSuffix(rulePath, "*") {
		prefix := strings.TrimSuffix(rulePath, "*")
		return strings.HasPrefix(requestPath, prefix)
	}

	return false
}

// matchesMethod checks if method matches the rule
func (t *TokenBucketLimiter) matchesMethod(ruleMethod, requestMethod string) bool {
	// Wildcard match
	if ruleMethod == "*" {
		return true
	}

	// Exact match (case insensitive)
	return strings.EqualFold(ruleMethod, requestMethod)
}

// generateKey generates a rate limiting key based on the rule
func (t *TokenBucketLimiter) generateKey(rule *config.RateLimitRule, clientIP, keyType string) string {
	switch rule.KeyBy {
	case "ip":
		return fmt.Sprintf("ip:%s:%s:%s", clientIP, rule.Path, rule.Method)
	case "user":
		return fmt.Sprintf("user:%s:%s:%s", keyType, rule.Path, rule.Method)
	case "api_key":
		return fmt.Sprintf("apikey:%s:%s:%s", keyType, rule.Path, rule.Method)
	default:
		return fmt.Sprintf("global:%s:%s", rule.Path, rule.Method)
	}
}

// getLimiter gets or creates a rate limiter for a key
func (t *TokenBucketLimiter) getLimiter(key string, rule *config.RateLimitRule) *rate.Limiter {
	t.mu.RLock()
	limiter, exists := t.limiters[key]
	t.mu.RUnlock()

	if exists {
		return limiter
	}

	// Create new limiter
	t.mu.Lock()
	defer t.mu.Unlock()

	// Double-check after acquiring write lock
	if limiter, exists := t.limiters[key]; exists {
		return limiter
	}

	// Calculate rate limit
	rateLimit := rate.Limit(rule.Rate)
	if rule.Window > 0 {
		rateLimit = rate.Limit(float64(rule.Rate) / rule.Window.Seconds())
	}

	// Create limiter with burst capacity
	limiter = rate.NewLimiter(rateLimit, rule.Burst)
	t.limiters[key] = limiter

	t.logger.Debug("Created new rate limiter",
		"key", key,
		"rate", rateLimit,
		"burst", rule.Burst)

	return limiter
}

// cleanup removes old rate limiters periodically
func (t *TokenBucketLimiter) cleanup() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		t.mu.Lock()
		
		// Remove limiters that haven't been used recently
		// This is a simple implementation - in production, you might want more sophisticated cleanup
		for key, limiter := range t.limiters {
			// If limiter has full tokens, it hasn't been used recently
			if limiter.Tokens() == float64(limiter.Burst()) {
				delete(t.limiters, key)
			}
		}
		
		t.mu.Unlock()
	}
}

// GetStats returns rate limiting statistics
func (t *TokenBucketLimiter) GetStats() map[string]interface{} {
	t.stats.mu.RLock()
	defer t.stats.mu.RUnlock()

	t.mu.RLock()
	activeLimiters := len(t.limiters)
	t.mu.RUnlock()

	return map[string]interface{}{
		"algorithm":        "token_bucket",
		"total_requests":   t.stats.totalRequests,
		"allowed_requests": t.stats.allowedRequests,
		"blocked_requests": t.stats.blockedRequests,
		"active_limiters":  activeLimiters,
	}
}

// LeakyBucketLimiter implements leaky bucket rate limiting
type LeakyBucketLimiter struct {
	config  config.RateLimitConfig
	logger  *telemetry.Logger
	metrics *telemetry.Metrics
	
	// Bucket states
	buckets map[string]*LeakyBucket
	mu      sync.RWMutex
	
	// Statistics
	stats struct {
		totalRequests   int64
		allowedRequests int64
		blockedRequests int64
		mu              sync.RWMutex
	}
}

// LeakyBucket represents a leaky bucket
type LeakyBucket struct {
	capacity   int
	tokens     int
	leakRate   time.Duration
	lastLeak   time.Time
	mu         sync.Mutex
}

// NewLeakyBucketLimiter creates a new leaky bucket rate limiter
func NewLeakyBucketLimiter(cfg config.RateLimitConfig, logger *telemetry.Logger, metrics *telemetry.Metrics) (*LeakyBucketLimiter, error) {
	limiter := &LeakyBucketLimiter{
		config:  cfg,
		logger:  logger,
		metrics: metrics,
		buckets: make(map[string]*LeakyBucket),
	}

	// Start cleanup goroutine
	go limiter.cleanup()

	return limiter, nil
}

// IsAllowed checks if a request is allowed by the leaky bucket
func (l *LeakyBucketLimiter) IsAllowed(clientIP, path, method, keyType string) (bool, error) {
	// Update statistics
	l.stats.mu.Lock()
	l.stats.totalRequests++
	l.stats.mu.Unlock()

	// Find matching rule
	rule := l.findMatchingRule(path, method)
	if rule == nil {
		// No rule found, allow request
		l.stats.mu.Lock()
		l.stats.allowedRequests++
		l.stats.mu.Unlock()
		return true, nil
	}

	// Generate key
	key := l.generateKey(rule, clientIP, keyType)

	// Get or create bucket
	bucket := l.getBucket(key, rule)

	// Check if request is allowed
	allowed := bucket.Allow()

	// Update statistics and metrics
	if allowed {
		l.stats.mu.Lock()
		l.stats.allowedRequests++
		l.stats.mu.Unlock()
	} else {
		l.stats.mu.Lock()
		l.stats.blockedRequests++
		l.stats.mu.Unlock()

		if l.metrics != nil {
			l.metrics.RecordRateLimitHit(path, rule.KeyBy)
		}

		l.logger.Warn("Rate limit exceeded (leaky bucket)",
			"key", key,
			"path", path,
			"method", method,
			"client_ip", clientIP)
	}

	return allowed, nil
}

// Allow checks if the bucket allows a request
func (b *LeakyBucket) Allow() bool {
	b.mu.Lock()
	defer b.mu.Unlock()

	now := time.Now()

	// Leak tokens based on time elapsed
	if !b.lastLeak.IsZero() {
		elapsed := now.Sub(b.lastLeak)
		leaks := int(elapsed / b.leakRate)
		if leaks > 0 {
			b.tokens -= leaks
			if b.tokens < 0 {
				b.tokens = 0
			}
			b.lastLeak = now
		}
	} else {
		b.lastLeak = now
	}

	// Check if bucket has capacity
	if b.tokens < b.capacity {
		b.tokens++
		return true
	}

	return false
}

// Helper methods for LeakyBucketLimiter (similar to TokenBucketLimiter)
func (l *LeakyBucketLimiter) findMatchingRule(path, method string) *config.RateLimitRule {
	for _, rule := range l.config.Rules {
		if l.matchesRule(rule, path, method) {
			return &rule
		}
	}
	return nil
}

func (l *LeakyBucketLimiter) matchesRule(rule config.RateLimitRule, path, method string) bool {
	return l.matchesPath(rule.Path, path) && l.matchesMethod(rule.Method, method)
}

func (l *LeakyBucketLimiter) matchesPath(rulePath, requestPath string) bool {
	if rulePath == requestPath {
		return true
	}
	if strings.HasSuffix(rulePath, "*") {
		prefix := strings.TrimSuffix(rulePath, "*")
		return strings.HasPrefix(requestPath, prefix)
	}
	return false
}

func (l *LeakyBucketLimiter) matchesMethod(ruleMethod, requestMethod string) bool {
	return ruleMethod == "*" || strings.EqualFold(ruleMethod, requestMethod)
}

func (l *LeakyBucketLimiter) generateKey(rule *config.RateLimitRule, clientIP, keyType string) string {
	switch rule.KeyBy {
	case "ip":
		return fmt.Sprintf("ip:%s:%s:%s", clientIP, rule.Path, rule.Method)
	case "user":
		return fmt.Sprintf("user:%s:%s:%s", keyType, rule.Path, rule.Method)
	case "api_key":
		return fmt.Sprintf("apikey:%s:%s:%s", keyType, rule.Path, rule.Method)
	default:
		return fmt.Sprintf("global:%s:%s", rule.Path, rule.Method)
	}
}

func (l *LeakyBucketLimiter) getBucket(key string, rule *config.RateLimitRule) *LeakyBucket {
	l.mu.RLock()
	bucket, exists := l.buckets[key]
	l.mu.RUnlock()

	if exists {
		return bucket
	}

	l.mu.Lock()
	defer l.mu.Unlock()

	if bucket, exists := l.buckets[key]; exists {
		return bucket
	}

	// Calculate leak rate
	leakRate := time.Second
	if rule.Window > 0 {
		leakRate = rule.Window / time.Duration(rule.Rate)
	}

	bucket = &LeakyBucket{
		capacity: rule.Burst,
		tokens:   0,
		leakRate: leakRate,
		lastLeak: time.Now(),
	}

	l.buckets[key] = bucket
	return bucket
}

func (l *LeakyBucketLimiter) cleanup() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		l.mu.Lock()
		
		now := time.Now()
		for key, bucket := range l.buckets {
			bucket.mu.Lock()
			// Remove buckets that haven't been used for 10 minutes
			if now.Sub(bucket.lastLeak) > 10*time.Minute {
				delete(l.buckets, key)
			}
			bucket.mu.Unlock()
		}
		
		l.mu.Unlock()
	}
}

func (l *LeakyBucketLimiter) GetStats() map[string]interface{} {
	l.stats.mu.RLock()
	defer l.stats.mu.RUnlock()

	l.mu.RLock()
	activeBuckets := len(l.buckets)
	l.mu.RUnlock()

	return map[string]interface{}{
		"algorithm":        "leaky_bucket",
		"total_requests":   l.stats.totalRequests,
		"allowed_requests": l.stats.allowedRequests,
		"blocked_requests": l.stats.blockedRequests,
		"active_buckets":   activeBuckets,
	}
}
