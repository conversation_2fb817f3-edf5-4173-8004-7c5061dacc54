package security

import (
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// WAFRequest represents a request to be evaluated by WAF
type WAFRequest struct {
	Method      string            `json:"method"`
	Path        string            `json:"path"`
	Query       string            `json:"query"`
	Headers     map[string]string `json:"headers"`
	Body        string            `json:"body"`
	ClientIP    string            `json:"client_ip"`
	UserAgent   string            `json:"user_agent"`
	ContentType string            `json:"content_type"`
}

// WAFResult represents the result of WAF evaluation
type WAFResult struct {
	Allowed     bool                   `json:"allowed"`
	Action      string                 `json:"action"`
	RuleName    string                 `json:"rule_name"`
	Reason      string                 `json:"reason"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Severity    string                 `json:"severity,omitempty"`
}

// WAFEngine interface defines WAF evaluation methods
type WAFEngine interface {
	Evaluate(request *WAFRequest) (*WAFResult, error)
	GetStats() map[string]interface{}
}

// BasicWAFEngine implements a basic WAF engine
type BasicWAFEngine struct {
	config  config.WAFConfig
	logger  *telemetry.Logger
	metrics *telemetry.Metrics
	
	// Compiled regex patterns
	rules map[string]*WAFRule
	mu    sync.RWMutex
	
	// Statistics
	stats struct {
		totalRequests   int64
		allowedRequests int64
		blockedRequests int64
		loggedRequests  int64
		mu              sync.RWMutex
	}
}

// WAFRule represents a compiled WAF rule
type WAFRule struct {
	Name        string
	Pattern     *regexp.Regexp
	Action      string
	Description string
	Severity    string
}

// NewWAFEngine creates a new WAF engine
func NewWAFEngine(cfg config.WAFConfig, logger *telemetry.Logger, metrics *telemetry.Metrics) (WAFEngine, error) {
	engine := &BasicWAFEngine{
		config:  cfg,
		logger:  logger,
		metrics: metrics,
		rules:   make(map[string]*WAFRule),
	}

	// Compile rules
	if err := engine.compileRules(); err != nil {
		return nil, fmt.Errorf("failed to compile WAF rules: %w", err)
	}

	return engine, nil
}

// compileRules compiles all WAF rules
func (w *BasicWAFEngine) compileRules() error {
	w.mu.Lock()
	defer w.mu.Unlock()

	for _, rule := range w.config.Rules {
		compiledPattern, err := regexp.Compile(rule.Pattern)
		if err != nil {
			return fmt.Errorf("failed to compile pattern for rule %s: %w", rule.Name, err)
		}

		wafRule := &WAFRule{
			Name:        rule.Name,
			Pattern:     compiledPattern,
			Action:      rule.Action,
			Description: rule.Description,
			Severity:    "medium", // Default severity
		}

		w.rules[rule.Name] = wafRule
		w.logger.Debug("Compiled WAF rule", "name", rule.Name, "pattern", rule.Pattern)
	}

	w.logger.Info("Compiled WAF rules", "count", len(w.rules))
	return nil
}

// Evaluate evaluates a request against WAF rules
func (w *BasicWAFEngine) Evaluate(request *WAFRequest) (*WAFResult, error) {
	// Update statistics
	w.stats.mu.Lock()
	w.stats.totalRequests++
	w.stats.mu.Unlock()

	// Default result - allow
	result := &WAFResult{
		Allowed: true,
		Action:  "allow",
	}

	w.mu.RLock()
	defer w.mu.RUnlock()

	// Evaluate each rule
	for _, rule := range w.rules {
		if w.evaluateRule(rule, request) {
			// Rule matched
			result.Allowed = rule.Action != "block"
			result.Action = rule.Action
			result.RuleName = rule.Name
			result.Reason = rule.Description
			result.Severity = rule.Severity

			// Log the match
			w.logger.Warn("WAF rule matched",
				"rule", rule.Name,
				"action", rule.Action,
				"client_ip", request.ClientIP,
				"path", request.Path,
				"user_agent", request.UserAgent)

			// Update statistics and metrics
			switch rule.Action {
			case "block":
				w.stats.mu.Lock()
				w.stats.blockedRequests++
				w.stats.mu.Unlock()

				if w.metrics != nil {
					w.metrics.RecordWAFBlock(rule.Name, rule.Action)
				}

			case "log":
				w.stats.mu.Lock()
				w.stats.loggedRequests++
				w.stats.mu.Unlock()

				if w.metrics != nil {
					w.metrics.RecordWAFBlock(rule.Name, rule.Action)
				}

			case "allow":
				w.stats.mu.Lock()
				w.stats.allowedRequests++
				w.stats.mu.Unlock()
			}

			// Return on first match (you might want to evaluate all rules)
			return result, nil
		}
	}

	// No rules matched, allow request
	w.stats.mu.Lock()
	w.stats.allowedRequests++
	w.stats.mu.Unlock()

	return result, nil
}

// evaluateRule evaluates a single rule against a request
func (w *BasicWAFEngine) evaluateRule(rule *WAFRule, request *WAFRequest) bool {
	// Check different parts of the request
	targets := []string{
		request.Path,
		request.Query,
		request.Body,
		request.UserAgent,
	}

	// Add header values to targets
	for _, value := range request.Headers {
		targets = append(targets, value)
	}

	// Check each target against the rule pattern
	for _, target := range targets {
		if rule.Pattern.MatchString(target) {
			return true
		}
	}

	return false
}

// GetStats returns WAF statistics
func (w *BasicWAFEngine) GetStats() map[string]interface{} {
	w.stats.mu.RLock()
	defer w.stats.mu.RUnlock()

	w.mu.RLock()
	ruleCount := len(w.rules)
	w.mu.RUnlock()

	return map[string]interface{}{
		"total_requests":   w.stats.totalRequests,
		"allowed_requests": w.stats.allowedRequests,
		"blocked_requests": w.stats.blockedRequests,
		"logged_requests":  w.stats.loggedRequests,
		"active_rules":     ruleCount,
	}
}

// AddRule adds a new WAF rule
func (w *BasicWAFEngine) AddRule(rule config.WAFRule) error {
	w.mu.Lock()
	defer w.mu.Unlock()

	compiledPattern, err := regexp.Compile(rule.Pattern)
	if err != nil {
		return fmt.Errorf("failed to compile pattern for rule %s: %w", rule.Name, err)
	}

	wafRule := &WAFRule{
		Name:        rule.Name,
		Pattern:     compiledPattern,
		Action:      rule.Action,
		Description: rule.Description,
		Severity:    "medium",
	}

	w.rules[rule.Name] = wafRule
	w.logger.Info("Added WAF rule", "name", rule.Name)

	return nil
}

// RemoveRule removes a WAF rule
func (w *BasicWAFEngine) RemoveRule(ruleName string) error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if _, exists := w.rules[ruleName]; !exists {
		return fmt.Errorf("rule not found: %s", ruleName)
	}

	delete(w.rules, ruleName)
	w.logger.Info("Removed WAF rule", "name", ruleName)

	return nil
}

// UpdateRule updates an existing WAF rule
func (w *BasicWAFEngine) UpdateRule(rule config.WAFRule) error {
	w.mu.Lock()
	defer w.mu.Unlock()

	compiledPattern, err := regexp.Compile(rule.Pattern)
	if err != nil {
		return fmt.Errorf("failed to compile pattern for rule %s: %w", rule.Name, err)
	}

	wafRule := &WAFRule{
		Name:        rule.Name,
		Pattern:     compiledPattern,
		Action:      rule.Action,
		Description: rule.Description,
		Severity:    "medium",
	}

	w.rules[rule.Name] = wafRule
	w.logger.Info("Updated WAF rule", "name", rule.Name)

	return nil
}

// GetRules returns all WAF rules
func (w *BasicWAFEngine) GetRules() []config.WAFRule {
	w.mu.RLock()
	defer w.mu.RUnlock()

	rules := make([]config.WAFRule, 0, len(w.rules))
	for _, rule := range w.rules {
		rules = append(rules, config.WAFRule{
			Name:        rule.Name,
			Pattern:     rule.Pattern.String(),
			Action:      rule.Action,
			Description: rule.Description,
		})
	}

	return rules
}

// CreateWAFRequestFromHTTP creates a WAFRequest from HTTP request data
func CreateWAFRequestFromHTTP(method, path, query, body, clientIP, userAgent, contentType string, headers map[string]string) *WAFRequest {
	return &WAFRequest{
		Method:      method,
		Path:        path,
		Query:       query,
		Headers:     headers,
		Body:        body,
		ClientIP:    clientIP,
		UserAgent:   userAgent,
		ContentType: contentType,
	}
}

// GetDefaultWAFRules returns a set of default WAF rules
func GetDefaultWAFRules() []config.WAFRule {
	return []config.WAFRule{
		{
			Name:        "sql_injection",
			Pattern:     `(?i)(union|select|insert|delete|update|drop|create|alter|exec|script|declare|cast|convert)`,
			Action:      "block",
			Description: "Block SQL injection attempts",
		},
		{
			Name:        "xss_attack",
			Pattern:     `(?i)(<script|javascript:|on\w+\s*=|<iframe|<object|<embed)`,
			Action:      "block",
			Description: "Block XSS attacks",
		},
		{
			Name:        "path_traversal",
			Pattern:     `(\.\./|\.\.\\|%2e%2e%2f|%2e%2e%5c)`,
			Action:      "block",
			Description: "Block path traversal attempts",
		},
		{
			Name:        "command_injection",
			Pattern:     `(?i)(;|\||&|`+"`"+`|\$\(|<\(|>\(|\${)`,
			Action:      "block",
			Description: "Block command injection attempts",
		},
		{
			Name:        "suspicious_user_agent",
			Pattern:     `(?i)(sqlmap|nmap|nikto|burp|owasp|zap|w3af|acunetix|nessus)`,
			Action:      "log",
			Description: "Log suspicious user agents",
		},
		{
			Name:        "large_request_body",
			Pattern:     `.{10000,}`, // More than 10KB
			Action:      "log",
			Description: "Log large request bodies",
		},
	}
}
