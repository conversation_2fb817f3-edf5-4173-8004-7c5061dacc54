package security

import (
	"fmt"
	"net"
	"sync"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// Manager manages security operations
type Manager struct {
	config      *config.SecurityConfig
	logger      *telemetry.Logger
	metrics     *telemetry.Metrics
	
	// Security components
	rateLimiter   RateLimiter
	wafEngine     WAFEngine
	ipFilter      IPFilter
	corsHandler   CORSHandler
	
	mu sync.RWMutex
}

// NewManager creates a new security manager
func NewManager(cfg config.SecurityConfig, logger *telemetry.Logger, metrics *telemetry.Metrics) (*Manager, error) {
	manager := &Manager{
		config:  &cfg,
		logger:  logger,
		metrics: metrics,
	}

	// Initialize security components
	if err := manager.initializeComponents(); err != nil {
		return nil, fmt.Errorf("failed to initialize security components: %w", err)
	}

	return manager, nil
}

// initializeComponents initializes all security components
func (m *Manager) initializeComponents() error {
	var err error

	// Initialize rate limiter
	if m.config.RateLimit.Enabled {
		m.rateLimiter, err = NewRateLimiter(m.config.RateLimit, m.logger.With("component", "ratelimit"), m.metrics)
		if err != nil {
			return fmt.Errorf("failed to initialize rate limiter: %w", err)
		}
	}

	// Initialize WAF engine
	if m.config.WAF.Enabled {
		m.wafEngine, err = NewWAFEngine(m.config.WAF, m.logger.With("component", "waf"), m.metrics)
		if err != nil {
			return fmt.Errorf("failed to initialize WAF engine: %w", err)
		}
	}

	// Initialize IP filter
	if m.config.IPFilter.Enabled {
		m.ipFilter, err = NewIPFilter(m.config.IPFilter, m.logger.With("component", "ipfilter"))
		if err != nil {
			return fmt.Errorf("failed to initialize IP filter: %w", err)
		}
	}

	// Initialize CORS handler
	if m.config.CORS.Enabled {
		m.corsHandler, err = NewCORSHandler(m.config.CORS, m.logger.With("component", "cors"))
		if err != nil {
			return fmt.Errorf("failed to initialize CORS handler: %w", err)
		}
	}

	return nil
}

// CheckRateLimit checks if a request should be rate limited
func (m *Manager) CheckRateLimit(clientIP, path, method, keyType string) (bool, error) {
	if m.rateLimiter == nil {
		return false, nil // Rate limiting disabled
	}

	return m.rateLimiter.IsAllowed(clientIP, path, method, keyType)
}

// CheckWAF checks if a request should be blocked by WAF
func (m *Manager) CheckWAF(request *WAFRequest) (*WAFResult, error) {
	if m.wafEngine == nil {
		return &WAFResult{Allowed: true}, nil // WAF disabled
	}

	return m.wafEngine.Evaluate(request)
}

// CheckIPFilter checks if an IP should be allowed or blocked
func (m *Manager) CheckIPFilter(clientIP string) (bool, error) {
	if m.ipFilter == nil {
		return true, nil // IP filtering disabled
	}

	return m.ipFilter.IsAllowed(clientIP)
}

// HandleCORS handles CORS preflight and actual requests
func (m *Manager) HandleCORS(origin, method string, headers []string) (*CORSResult, error) {
	if m.corsHandler == nil {
		return &CORSResult{Allowed: true}, nil // CORS disabled
	}

	return m.corsHandler.Handle(origin, method, headers)
}

// GetRateLimiter returns the rate limiter instance
func (m *Manager) GetRateLimiter() RateLimiter {
	return m.rateLimiter
}

// GetWAFEngine returns the WAF engine instance
func (m *Manager) GetWAFEngine() WAFEngine {
	return m.wafEngine
}

// GetIPFilter returns the IP filter instance
func (m *Manager) GetIPFilter() IPFilter {
	return m.ipFilter
}

// GetCORSHandler returns the CORS handler instance
func (m *Manager) GetCORSHandler() CORSHandler {
	return m.corsHandler
}

// UpdateConfig updates the security configuration
func (m *Manager) UpdateConfig(cfg config.SecurityConfig) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.config = &cfg

	// Reinitialize components with new configuration
	return m.initializeComponents()
}

// GetStats returns security statistics
func (m *Manager) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})

	if m.rateLimiter != nil {
		stats["rate_limit"] = m.rateLimiter.GetStats()
	}

	if m.wafEngine != nil {
		stats["waf"] = m.wafEngine.GetStats()
	}

	if m.ipFilter != nil {
		stats["ip_filter"] = m.ipFilter.GetStats()
	}

	if m.corsHandler != nil {
		stats["cors"] = m.corsHandler.GetStats()
	}

	return stats
}

// Close closes the security manager and all its components
func (m *Manager) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Close rate limiter
	if m.rateLimiter != nil {
		if closer, ok := m.rateLimiter.(interface{ Close() error }); ok {
			if err := closer.Close(); err != nil {
				m.logger.Error("Failed to close rate limiter", "error", err)
			}
		}
	}

	// Close WAF engine
	if m.wafEngine != nil {
		if closer, ok := m.wafEngine.(interface{ Close() error }); ok {
			if err := closer.Close(); err != nil {
				m.logger.Error("Failed to close WAF engine", "error", err)
			}
		}
	}

	// Close IP filter
	if m.ipFilter != nil {
		if closer, ok := m.ipFilter.(interface{ Close() error }); ok {
			if err := closer.Close(); err != nil {
				m.logger.Error("Failed to close IP filter", "error", err)
			}
		}
	}

	// Close CORS handler
	if m.corsHandler != nil {
		if closer, ok := m.corsHandler.(interface{ Close() error }); ok {
			if err := closer.Close(); err != nil {
				m.logger.Error("Failed to close CORS handler", "error", err)
			}
		}
	}

	return nil
}

// IsValidIP checks if a string is a valid IP address
func IsValidIP(ip string) bool {
	return net.ParseIP(ip) != nil
}

// IsInCIDR checks if an IP is in a CIDR range
func IsInCIDR(ip, cidr string) bool {
	_, network, err := net.ParseCIDR(cidr)
	if err != nil {
		return false
	}

	ipAddr := net.ParseIP(ip)
	if ipAddr == nil {
		return false
	}

	return network.Contains(ipAddr)
}
