package config

import (
	"context"
	"testing"
	"time"

	"api-gateway/pkg/telemetry"
)

func TestDynamicConfigManager(t *testing.T) {
	// Create test logger
	logger, err := telemetry.NewLogger(LoggingConfig{
		Level:  "debug",
		Format: "text",
		Output: "stdout",
	})
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Create test configuration
	testConfig := &Config{
		Server: ServerConfig{
			Address:      ":8080",
			ReadTimeout:  30,
			WriteTimeout: 30,
			IdleTimeout:  120,
		},
		Routes: []RouteConfig{
			{
				Name:   "test-route",
				Path:   "/api/v1/test/*",
				Method: "GET",
				Upstream: UpstreamConfig{
					Type:         "static",
					LoadBalancer: "round_robin",
					Servers: []ServerTarget{
						{
							Host:   "localhost",
							Port:   8081,
							Weight: 1,
						},
					},
				},
				Timeout: 30 * time.Second,
				Retries: 3,
			},
		},
		Security: SecurityConfig{
			RateLimit: RateLimitConfig{
				Enabled:   true,
				Algorithm: "token_bucket",
				Rules: []RateLimitRule{
					{
						Path:   "/api/*",
						Method: "*",
						Rate:   100,
						Burst:  200,
						Window: "1m",
						KeyBy:  "ip",
					},
				},
			},
		},
	}

	// Create storage
	storage := NewMemoryConfigStorage(logger)

	// Create dynamic config manager
	manager := NewDynamicConfigManager(testConfig, storage, logger)

	// Start the manager
	ctx := context.Background()
	if err := manager.Start(ctx); err != nil {
		t.Fatalf("Failed to start config manager: %v", err)
	}
	defer manager.Stop()

	// Test getting configuration
	config := manager.GetConfig()
	if config == nil {
		t.Fatal("Config should not be nil")
	}

	if len(config.Routes) != 1 {
		t.Fatalf("Expected 1 route, got %d", len(config.Routes))
	}

	// Test adding a new route
	newRoute := RouteConfig{
		Name:   "new-route",
		Path:   "/api/v1/new/*",
		Method: "POST",
		Upstream: UpstreamConfig{
			Type:         "static",
			LoadBalancer: "round_robin",
			Servers: []ServerTarget{
				{
					Host:   "localhost",
					Port:   8082,
					Weight: 1,
				},
			},
		},
		Timeout: 30 * time.Second,
		Retries: 3,
	}

	update := ConfigUpdate{
		Type:      ConfigUpdateTypeAdd,
		Section:   "routes",
		Key:       newRoute.Name,
		Value:     newRoute,
		Timestamp: time.Now(),
		Source:    "test",
	}

	if err := manager.UpdateConfig(update); err != nil {
		t.Fatalf("Failed to add new route: %v", err)
	}

	// Wait for update to be processed
	time.Sleep(100 * time.Millisecond)

	// Verify the route was added
	updatedConfig := manager.GetConfig()
	if len(updatedConfig.Routes) != 2 {
		t.Fatalf("Expected 2 routes after adding, got %d", len(updatedConfig.Routes))
	}

	// Test updating an existing route
	updatedRoute := newRoute
	updatedRoute.Method = "*"
	updatedRoute.Retries = 5

	updateExisting := ConfigUpdate{
		Type:      ConfigUpdateTypeUpdate,
		Section:   "routes",
		Key:       newRoute.Name,
		Value:     updatedRoute,
		Timestamp: time.Now(),
		Source:    "test",
	}

	if err := manager.UpdateConfig(updateExisting); err != nil {
		t.Fatalf("Failed to update route: %v", err)
	}

	// Wait for update to be processed
	time.Sleep(100 * time.Millisecond)

	// Verify the route was updated
	finalConfig := manager.GetConfig()
	var foundRoute *RouteConfig
	for _, route := range finalConfig.Routes {
		if route.Name == newRoute.Name {
			foundRoute = &route
			break
		}
	}

	if foundRoute == nil {
		t.Fatal("Updated route not found")
	}

	if foundRoute.Method != "*" {
		t.Fatalf("Expected method '*', got '%s'", foundRoute.Method)
	}

	if foundRoute.Retries != 5 {
		t.Fatalf("Expected retries 5, got %d", foundRoute.Retries)
	}

	// Test deleting a route
	deleteUpdate := ConfigUpdate{
		Type:      ConfigUpdateTypeDelete,
		Section:   "routes",
		Key:       newRoute.Name,
		Timestamp: time.Now(),
		Source:    "test",
	}

	if err := manager.UpdateConfig(deleteUpdate); err != nil {
		t.Fatalf("Failed to delete route: %v", err)
	}

	// Wait for update to be processed
	time.Sleep(100 * time.Millisecond)

	// Verify the route was deleted
	finalConfig2 := manager.GetConfig()
	if len(finalConfig2.Routes) != 1 {
		t.Fatalf("Expected 1 route after deletion, got %d", len(finalConfig2.Routes))
	}
}

func TestConfigValidator(t *testing.T) {
	validator := NewDefaultConfigValidator()

	// Test valid route configuration
	validRoute := RouteConfig{
		Name:   "valid-route",
		Path:   "/api/v1/valid/*",
		Method: "GET",
		Upstream: UpstreamConfig{
			Type:         "static",
			LoadBalancer: "round_robin",
			Servers: []ServerTarget{
				{
					Host:   "localhost",
					Port:   8081,
					Weight: 1,
				},
			},
		},
		Timeout: 30 * time.Second,
		Retries: 3,
	}

	if err := validator.ValidateRouteConfig(validRoute); err != nil {
		t.Fatalf("Valid route should pass validation: %v", err)
	}

	// Test invalid route configuration - missing name
	invalidRoute := validRoute
	invalidRoute.Name = ""

	if err := validator.ValidateRouteConfig(invalidRoute); err == nil {
		t.Fatal("Route without name should fail validation")
	}

	// Test invalid route configuration - invalid method
	invalidRoute2 := validRoute
	invalidRoute2.Method = "INVALID"

	if err := validator.ValidateRouteConfig(invalidRoute2); err == nil {
		t.Fatal("Route with invalid method should fail validation")
	}

	// Test invalid route configuration - invalid path
	invalidRoute3 := validRoute
	invalidRoute3.Path = "invalid-path"

	if err := validator.ValidateRouteConfig(invalidRoute3); err == nil {
		t.Fatal("Route with invalid path should fail validation")
	}
}

func TestConfigStorage(t *testing.T) {
	logger, err := telemetry.NewLogger(LoggingConfig{
		Level:  "debug",
		Format: "text",
		Output: "stdout",
	})
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	storage := NewMemoryConfigStorage(logger)
	defer storage.Close()

	// Test Set and Get
	key := "test-key"
	value := []byte("test-value")

	if err := storage.Set(key, value); err != nil {
		t.Fatalf("Failed to set value: %v", err)
	}

	retrievedValue, err := storage.Get(key)
	if err != nil {
		t.Fatalf("Failed to get value: %v", err)
	}

	if string(retrievedValue) != string(value) {
		t.Fatalf("Expected '%s', got '%s'", string(value), string(retrievedValue))
	}

	// Test List
	key2 := "test-key-2"
	value2 := []byte("test-value-2")

	if err := storage.Set(key2, value2); err != nil {
		t.Fatalf("Failed to set second value: %v", err)
	}

	allValues, err := storage.List("")
	if err != nil {
		t.Fatalf("Failed to list values: %v", err)
	}

	if len(allValues) != 2 {
		t.Fatalf("Expected 2 values, got %d", len(allValues))
	}

	// Test List with prefix
	prefixValues, err := storage.List("test-key")
	if err != nil {
		t.Fatalf("Failed to list values with prefix: %v", err)
	}

	if len(prefixValues) != 2 {
		t.Fatalf("Expected 2 values with prefix, got %d", len(prefixValues))
	}

	// Test Delete
	if err := storage.Delete(key); err != nil {
		t.Fatalf("Failed to delete value: %v", err)
	}

	_, err = storage.Get(key)
	if err == nil {
		t.Fatal("Expected error when getting deleted key")
	}

	// Test Watch
	watchKey := "watch-key"
	watcher, err := storage.Watch(watchKey)
	if err != nil {
		t.Fatalf("Failed to create watcher: %v", err)
	}

	// Set a value and check if watcher receives event
	go func() {
		time.Sleep(50 * time.Millisecond)
		storage.Set(watchKey, []byte("watch-value"))
	}()

	select {
	case event := <-watcher:
		if event.Type != "update" {
			t.Fatalf("Expected update event, got %s", event.Type)
		}
		if event.Key != watchKey {
			t.Fatalf("Expected key %s, got %s", watchKey, event.Key)
		}
	case <-time.After(1 * time.Second):
		t.Fatal("Timeout waiting for watch event")
	}
}

func TestConfigListeners(t *testing.T) {
	logger, err := telemetry.NewLogger(LoggingConfig{
		Level:  "debug",
		Format: "text",
		Output: "stdout",
	})
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Test route config listener
	called := false
	callback := func(ctx context.Context, change ConfigChange) error {
		called = true
		return nil
	}

	listener := NewRouteConfigListener("test-listener", logger, callback)

	change := ConfigChange{
		Type:      ConfigUpdateTypeAdd,
		Section:   "routes",
		Key:       "test-route",
		NewValue:  "test-value",
		Version:   "v1",
		Timestamp: time.Now(),
	}

	if err := listener.OnConfigChange(context.Background(), change); err != nil {
		t.Fatalf("Listener failed: %v", err)
	}

	if !called {
		t.Fatal("Callback should have been called")
	}

	if listener.GetName() != "test-listener" {
		t.Fatalf("Expected name 'test-listener', got '%s'", listener.GetName())
	}
}
