package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v3"
)

// LoadConfig loads configuration from a file
func LoadConfig(configFile string) (*Config, error) {
	// Check if file exists
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		return nil, fmt.Errorf("configuration file not found: %s", configFile)
	}

	// Read file
	data, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read configuration file: %w", err)
	}

	// Parse YAML
	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, fmt.Errorf("failed to parse configuration file: %w", err)
	}

	// Apply environment variable overrides
	if err := applyEnvironmentOverrides(&cfg); err != nil {
		return nil, fmt.Errorf("failed to apply environment overrides: %w", err)
	}

	// Validate configuration
	if err := validateConfig(&cfg); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	// Set defaults
	setDefaults(&cfg)

	return &cfg, nil
}

// applyEnvironmentOverrides applies environment variable overrides
func applyEnvironmentOverrides(cfg *Config) error {
	// Server configuration
	if port := os.Getenv("GATEWAY_SERVER_PORT"); port != "" {
		cfg.Server.Address = ":" + port
	}

	if addr := os.Getenv("GATEWAY_SERVER_ADDRESS"); addr != "" {
		cfg.Server.Address = addr
	}

	// Logging configuration
	if level := os.Getenv("GATEWAY_LOG_LEVEL"); level != "" {
		cfg.Logging.Level = level
	}

	if format := os.Getenv("GATEWAY_LOG_FORMAT"); format != "" {
		cfg.Logging.Format = format
	}

	// Database configuration (if applicable)
	if dbURL := os.Getenv("DATABASE_URL"); dbURL != "" {
		// Parse database URL and set configuration
		// This is a placeholder for database configuration
	}

	// Redis configuration (if applicable)
	if redisURL := os.Getenv("REDIS_URL"); redisURL != "" {
		// Parse Redis URL and set configuration
		// This is a placeholder for Redis configuration
	}

	// JWT configuration
	if jwtSecret := os.Getenv("JWT_SECRET"); jwtSecret != "" {
		cfg.Auth.JWT.Secret = jwtSecret
	}

	// Service discovery configuration
	if consulAddr := os.Getenv("CONSUL_ADDRESS"); consulAddr != "" {
		cfg.Discovery.Consul.Address = consulAddr
	}

	if consulToken := os.Getenv("CONSUL_TOKEN"); consulToken != "" {
		cfg.Discovery.Consul.Token = consulToken
	}

	return nil
}

// validateConfig validates the configuration
func validateConfig(cfg *Config) error {
	// Validate server configuration
	if cfg.Server.Address == "" {
		return fmt.Errorf("server address is required")
	}

	if cfg.Server.ReadTimeout < 0 {
		return fmt.Errorf("server read timeout must be non-negative")
	}

	if cfg.Server.WriteTimeout < 0 {
		return fmt.Errorf("server write timeout must be non-negative")
	}

	// Validate TLS configuration
	if cfg.Server.TLS.Enabled {
		if cfg.Server.TLS.CertFile == "" {
			return fmt.Errorf("TLS certificate file is required when TLS is enabled")
		}
		if cfg.Server.TLS.KeyFile == "" {
			return fmt.Errorf("TLS key file is required when TLS is enabled")
		}
	}

	// Validate logging configuration
	validLogLevels := []string{"debug", "info", "warn", "error"}
	if !contains(validLogLevels, cfg.Logging.Level) {
		return fmt.Errorf("invalid log level: %s (valid: %s)", cfg.Logging.Level, strings.Join(validLogLevels, ", "))
	}

	validLogFormats := []string{"json", "text"}
	if !contains(validLogFormats, cfg.Logging.Format) {
		return fmt.Errorf("invalid log format: %s (valid: %s)", cfg.Logging.Format, strings.Join(validLogFormats, ", "))
	}

	// Validate authentication configuration
	if cfg.Auth.JWT.Enabled && cfg.Auth.JWT.Secret == "" {
		return fmt.Errorf("JWT secret is required when JWT authentication is enabled")
	}

	// Validate rate limiting configuration
	for i, rule := range cfg.Security.RateLimit.Rules {
		if rule.Rate <= 0 {
			return fmt.Errorf("rate limit rule %d: rate must be positive", i)
		}
		if rule.Burst <= 0 {
			return fmt.Errorf("rate limit rule %d: burst must be positive", i)
		}
	}

	// Validate routes
	for i, route := range cfg.Routes {
		if route.Name == "" {
			return fmt.Errorf("route %d: name is required", i)
		}
		if route.Path == "" {
			return fmt.Errorf("route %d: path is required", i)
		}
		if route.Method == "" {
			return fmt.Errorf("route %d: method is required", i)
		}
	}

	return nil
}

// setDefaults sets default values for configuration
func setDefaults(cfg *Config) {
	// Server defaults
	if cfg.Server.Address == "" {
		cfg.Server.Address = ":8080"
	}
	if cfg.Server.ReadTimeout == 0 {
		cfg.Server.ReadTimeout = 30
	}
	if cfg.Server.WriteTimeout == 0 {
		cfg.Server.WriteTimeout = 30
	}
	if cfg.Server.IdleTimeout == 0 {
		cfg.Server.IdleTimeout = 120
	}

	// Logging defaults
	if cfg.Logging.Level == "" {
		cfg.Logging.Level = "info"
	}
	if cfg.Logging.Format == "" {
		cfg.Logging.Format = "json"
	}
	if cfg.Logging.Output == "" {
		cfg.Logging.Output = "stdout"
	}

	// Metrics defaults
	if cfg.Metrics.Port == 0 {
		cfg.Metrics.Port = 9090
	}
	if cfg.Metrics.Path == "" {
		cfg.Metrics.Path = "/metrics"
	}

	// Tracing defaults
	if cfg.Tracing.ServiceName == "" {
		cfg.Tracing.ServiceName = "api-gateway"
	}
	if cfg.Tracing.SampleRate == 0 {
		cfg.Tracing.SampleRate = 0.1
	}

	// Auth defaults
	if cfg.Auth.JWT.Algorithm == "" {
		cfg.Auth.JWT.Algorithm = "HS256"
	}
	if cfg.Auth.JWT.Expiration == 0 {
		cfg.Auth.JWT.Expiration = 3600
	}

	// Security defaults
	if cfg.Security.RateLimit.Algorithm == "" {
		cfg.Security.RateLimit.Algorithm = "token_bucket"
	}

	// Plugin defaults
	if cfg.Plugins.Directory == "" {
		cfg.Plugins.Directory = "plugins"
	}
}

// LoadConfigFromMultipleSources loads configuration from multiple sources
func LoadConfigFromMultipleSources(configFiles []string) (*Config, error) {
	var mergedConfig Config

	for _, configFile := range configFiles {
		cfg, err := LoadConfig(configFile)
		if err != nil {
			return nil, fmt.Errorf("failed to load config from %s: %w", configFile, err)
		}

		// Merge configurations (later files override earlier ones)
		if err := mergeConfigs(&mergedConfig, cfg); err != nil {
			return nil, fmt.Errorf("failed to merge config from %s: %w", configFile, err)
		}
	}

	return &mergedConfig, nil
}

// mergeConfigs merges two configurations
func mergeConfigs(base, override *Config) error {
	// This is a simplified merge - in production, you might want more sophisticated merging
	if override.Server.Address != "" {
		base.Server.Address = override.Server.Address
	}
	if override.Server.ReadTimeout != 0 {
		base.Server.ReadTimeout = override.Server.ReadTimeout
	}
	if override.Server.WriteTimeout != 0 {
		base.Server.WriteTimeout = override.Server.WriteTimeout
	}

	// Merge routes (append)
	base.Routes = append(base.Routes, override.Routes...)

	return nil
}

// SaveConfig saves configuration to a file
func SaveConfig(cfg *Config, configFile string) error {
	// Create directory if it doesn't exist
	dir := filepath.Dir(configFile)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	// Marshal to YAML
	data, err := yaml.Marshal(cfg)
	if err != nil {
		return fmt.Errorf("failed to marshal configuration: %w", err)
	}

	// Write to file
	if err := os.WriteFile(configFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write configuration file: %w", err)
	}

	return nil
}

// GetConfigTemplate returns a template configuration
func GetConfigTemplate() *Config {
	return &Config{
		Server: ServerConfig{
			Address:      ":8080",
			ReadTimeout:  30,
			WriteTimeout: 30,
			IdleTimeout:  120,
			TLS: TLSConfig{
				Enabled: false,
			},
		},
		Logging: LoggingConfig{
			Level:      "info",
			Format:     "json",
			Output:     "stdout",
			MaxSize:    100,
			MaxBackups: 3,
			MaxAge:     28,
		},
		Metrics: MetricsConfig{
			Enabled: true,
			Path:    "/metrics",
			Port:    9090,
		},
		Tracing: TracingConfig{
			Enabled:     false,
			ServiceName: "api-gateway",
			SampleRate:  0.1,
		},
		Auth: AuthConfig{
			JWT: JWTConfig{
				Enabled:    true,
				Algorithm:  "HS256",
				Expiration: 3600,
			},
		},
		Security: SecurityConfig{
			RateLimit: RateLimitConfig{
				Enabled:   true,
				Algorithm: "token_bucket",
			},
			CORS: CORSConfig{
				Enabled:          true,
				AllowedOrigins:   []string{"*"},
				AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
				AllowedHeaders:   []string{"Content-Type", "Authorization"},
				AllowCredentials: false,
				MaxAge:           86400,
			},
		},
		Discovery: DiscoveryConfig{
			Type: "consul",
		},
		Plugins: PluginConfig{
			Directory: "plugins",
		},
	}
}

// contains checks if a slice contains a string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
