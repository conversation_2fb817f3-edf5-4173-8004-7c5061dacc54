package discovery

import (
	"fmt"
	"strconv"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"

	"github.com/hashicorp/consul/api"
)

// ConsulClient implements DiscoveryClient for Consul
type ConsulClient struct {
	client *api.Client
	logger *telemetry.Logger
	config config.ConsulConfig
}

// NewConsulClient creates a new Consul discovery client
func NewConsulClient(cfg config.ConsulConfig, logger *telemetry.Logger) (*ConsulClient, error) {
	// Create Consul client configuration
	consulConfig := api.DefaultConfig()
	consulConfig.Address = cfg.Address
	consulConfig.Datacenter = cfg.Datacenter
	consulConfig.Token = cfg.Token

	// Create Consul client
	client, err := api.NewClient(consulConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create consul client: %w", err)
	}

	return &ConsulClient{
		client: client,
		logger: logger,
		config: cfg,
	}, nil
}

// GetServiceInstances returns service instances from Consul
func (c *ConsulClient) GetServiceInstances(serviceName string) ([]*ServiceInstance, error) {
	// Query healthy services
	services, _, err := c.client.Health().Service(serviceName, "", true, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to query consul for service %s: %w", serviceName, err)
	}

	instances := make([]*ServiceInstance, 0, len(services))
	for _, service := range services {
		instance := &ServiceInstance{
			ID:       service.Service.ID,
			Name:     service.Service.Service,
			Host:     service.Service.Address,
			Port:     service.Service.Port,
			Scheme:   "http", // Default to HTTP
			Healthy:  true,   // We only query healthy services
			Metadata: service.Service.Meta,
			Tags:     service.Service.Tags,
		}

		// Use node address if service address is empty
		if instance.Host == "" {
			instance.Host = service.Node.Address
		}

		// Check for HTTPS tag
		for _, tag := range service.Service.Tags {
			if tag == "https" || tag == "ssl" || tag == "tls" {
				instance.Scheme = "https"
				break
			}
		}

		instances = append(instances, instance)
	}

	c.logger.Debug("Retrieved service instances from Consul",
		"service", serviceName,
		"count", len(instances))

	return instances, nil
}

// RegisterService registers a service instance in Consul
func (c *ConsulClient) RegisterService(instance *ServiceInstance) error {
	// Create service registration
	registration := &api.AgentServiceRegistration{
		ID:      instance.ID,
		Name:    instance.Name,
		Address: instance.Host,
		Port:    instance.Port,
		Tags:    instance.Tags,
		Meta:    instance.Metadata,
	}

	// Add scheme as tag if HTTPS
	if instance.Scheme == "https" {
		registration.Tags = append(registration.Tags, "https")
	}

	// Add health check
	registration.Check = &api.AgentServiceCheck{
		HTTP:                           fmt.Sprintf("%s://%s:%d/health", instance.Scheme, instance.Host, instance.Port),
		Interval:                       "30s",
		Timeout:                        "5s",
		DeregisterCriticalServiceAfter: "90s",
	}

	// Register service
	err := c.client.Agent().ServiceRegister(registration)
	if err != nil {
		return fmt.Errorf("failed to register service in consul: %w", err)
	}

	c.logger.Info("Registered service in Consul",
		"service_id", instance.ID,
		"service_name", instance.Name,
		"address", fmt.Sprintf("%s:%d", instance.Host, instance.Port))

	return nil
}

// DeregisterService deregisters a service instance from Consul
func (c *ConsulClient) DeregisterService(serviceID string) error {
	err := c.client.Agent().ServiceDeregister(serviceID)
	if err != nil {
		return fmt.Errorf("failed to deregister service from consul: %w", err)
	}

	c.logger.Info("Deregistered service from Consul", "service_id", serviceID)
	return nil
}

// HealthCheck checks the health of the Consul connection
func (c *ConsulClient) HealthCheck() error {
	// Try to get leader status
	_, err := c.client.Status().Leader()
	if err != nil {
		return fmt.Errorf("consul health check failed: %w", err)
	}

	return nil
}

// Close closes the Consul client
func (c *ConsulClient) Close() error {
	// Consul client doesn't need explicit closing
	return nil
}

// GetServiceHealth returns the health status of a specific service instance
func (c *ConsulClient) GetServiceHealth(serviceID string) (bool, error) {
	checks, _, err := c.client.Agent().Checks()
	if err != nil {
		return false, fmt.Errorf("failed to get service checks: %w", err)
	}

	for _, check := range checks {
		if check.ServiceID == serviceID {
			return check.Status == api.HealthPassing, nil
		}
	}

	return false, fmt.Errorf("service check not found for service ID: %s", serviceID)
}

// WatchService watches for changes in a service
func (c *ConsulClient) WatchService(serviceName string, callback func([]*ServiceInstance)) error {
	// This is a simplified implementation
	// In production, you might want to use Consul's blocking queries or watches
	go func() {
		for {
			instances, err := c.GetServiceInstances(serviceName)
			if err != nil {
				c.logger.Error("Failed to watch service", "service", serviceName, "error", err)
				continue
			}
			callback(instances)
		}
	}()

	return nil
}

// GetAllServices returns all services registered in Consul
func (c *ConsulClient) GetAllServices() (map[string][]string, error) {
	services, _, err := c.client.Catalog().Services(nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get all services from consul: %w", err)
	}

	return services, nil
}

// GetServicesByTag returns services that have a specific tag
func (c *ConsulClient) GetServicesByTag(tag string) ([]*ServiceInstance, error) {
	services, _, err := c.client.Catalog().Services(nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get services from consul: %w", err)
	}

	var instances []*ServiceInstance
	for serviceName, tags := range services {
		// Check if service has the required tag
		hasTag := false
		for _, serviceTag := range tags {
			if serviceTag == tag {
				hasTag = true
				break
			}
		}

		if hasTag {
			serviceInstances, err := c.GetServiceInstances(serviceName)
			if err != nil {
				c.logger.Error("Failed to get instances for tagged service",
					"service", serviceName, "tag", tag, "error", err)
				continue
			}
			instances = append(instances, serviceInstances...)
		}
	}

	return instances, nil
}

// UpdateServiceTags updates the tags of a service
func (c *ConsulClient) UpdateServiceTags(serviceID string, tags []string) error {
	// Get current service registration
	services, err := c.client.Agent().Services()
	if err != nil {
		return fmt.Errorf("failed to get current services: %w", err)
	}

	service, exists := services[serviceID]
	if !exists {
		return fmt.Errorf("service not found: %s", serviceID)
	}

	// Update registration with new tags
	registration := &api.AgentServiceRegistration{
		ID:      service.ID,
		Name:    service.Service,
		Address: service.Address,
		Port:    service.Port,
		Tags:    tags,
		Meta:    service.Meta,
	}

	// Re-register service with updated tags
	err = c.client.Agent().ServiceRegister(registration)
	if err != nil {
		return fmt.Errorf("failed to update service tags: %w", err)
	}

	c.logger.Info("Updated service tags in Consul",
		"service_id", serviceID,
		"tags", tags)

	return nil
}

// GetDatacenters returns all available datacenters
func (c *ConsulClient) GetDatacenters() ([]string, error) {
	datacenters, err := c.client.Catalog().Datacenters()
	if err != nil {
		return nil, fmt.Errorf("failed to get datacenters: %w", err)
	}

	return datacenters, nil
}

// GetServiceEndpoints returns all endpoints for a service across datacenters
func (c *ConsulClient) GetServiceEndpoints(serviceName string, datacenter string) ([]*ServiceInstance, error) {
	queryOptions := &api.QueryOptions{}
	if datacenter != "" {
		queryOptions.Datacenter = datacenter
	}

	services, _, err := c.client.Health().Service(serviceName, "", true, queryOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to query consul for service %s in datacenter %s: %w", 
			serviceName, datacenter, err)
	}

	instances := make([]*ServiceInstance, 0, len(services))
	for _, service := range services {
		instance := &ServiceInstance{
			ID:       service.Service.ID,
			Name:     service.Service.Service,
			Host:     service.Service.Address,
			Port:     service.Service.Port,
			Scheme:   "http",
			Healthy:  true,
			Metadata: service.Service.Meta,
			Tags:     service.Service.Tags,
		}

		if instance.Host == "" {
			instance.Host = service.Node.Address
		}

		// Add datacenter to metadata
		if instance.Metadata == nil {
			instance.Metadata = make(map[string]string)
		}
		instance.Metadata["datacenter"] = service.Node.Datacenter

		// Check for HTTPS
		for _, tag := range service.Service.Tags {
			if tag == "https" || tag == "ssl" || tag == "tls" {
				instance.Scheme = "https"
				break
			}
		}

		instances = append(instances, instance)
	}

	return instances, nil
}
