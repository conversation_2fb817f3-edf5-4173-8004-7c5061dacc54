package discovery

import (
	"fmt"
	"strconv"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"

	"github.com/nacos-group/nacos-sdk-go/clients"
	"github.com/nacos-group/nacos-sdk-go/clients/naming_client"
	"github.com/nacos-group/nacos-sdk-go/common/constant"
	"github.com/nacos-group/nacos-sdk-go/model"
	"github.com/nacos-group/nacos-sdk-go/vo"
)

// NacosClient implements DiscoveryClient for Nacos
type NacosClient struct {
	client naming_client.INamingClient
	logger *telemetry.Logger
	config config.NacosConfig
}

// NewNacosClient creates a new Nacos discovery client
func NewNacosClient(cfg config.NacosConfig, logger *telemetry.Logger) (*NacosClient, error) {
	// Create server configs
	serverConfigs := make([]constant.ServerConfig, len(cfg.ServerConfigs))
	for i, sc := range cfg.ServerConfigs {
		serverConfigs[i] = constant.ServerConfig{
			IpAddr: sc.IpAddr,
			Port:   sc.Port,
		}
	}

	// Create client config
	clientConfig := constant.ClientConfig{
		NamespaceId:         cfg.ClientConfig.NamespaceId,
		TimeoutMs:           cfg.ClientConfig.TimeoutMs,
		NotLoadCacheAtStart: cfg.ClientConfig.NotLoadCacheAtStart,
		LogDir:              cfg.ClientConfig.LogDir,
		CacheDir:            cfg.ClientConfig.CacheDir,
		LogLevel:            cfg.ClientConfig.LogLevel,
	}

	// Create naming client
	client, err := clients.NewNamingClient(
		vo.NacosClientParam{
			ClientConfig:  &clientConfig,
			ServerConfigs: serverConfigs,
		},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create nacos client: %w", err)
	}

	return &NacosClient{
		client: client,
		logger: logger,
		config: cfg,
	}, nil
}

// GetServiceInstances returns service instances from Nacos
func (n *NacosClient) GetServiceInstances(serviceName string) ([]*ServiceInstance, error) {
	// Get healthy instances
	instances, err := n.client.SelectInstances(vo.SelectInstancesParam{
		ServiceName: serviceName,
		HealthyOnly: true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get service instances from nacos: %w", err)
	}

	serviceInstances := make([]*ServiceInstance, 0, len(instances))
	for _, instance := range instances {
		serviceInstance := &ServiceInstance{
			ID:       instance.InstanceId,
			Name:     serviceName,
			Host:     instance.Ip,
			Port:     int(instance.Port),
			Scheme:   "http", // Default to HTTP
			Healthy:  instance.Healthy,
			Metadata: instance.Metadata,
		}

		// Check for HTTPS in metadata
		if scheme, exists := instance.Metadata["scheme"]; exists && scheme == "https" {
			serviceInstance.Scheme = "https"
		}

		// Convert cluster name to tag
		if instance.ClusterName != "" {
			serviceInstance.Tags = []string{instance.ClusterName}
		}

		serviceInstances = append(serviceInstances, serviceInstance)
	}

	n.logger.Debug("Retrieved service instances from Nacos",
		"service", serviceName,
		"count", len(serviceInstances))

	return serviceInstances, nil
}

// RegisterService registers a service instance in Nacos
func (n *NacosClient) RegisterService(instance *ServiceInstance) error {
	// Prepare metadata
	metadata := instance.Metadata
	if metadata == nil {
		metadata = make(map[string]string)
	}

	// Add scheme to metadata
	metadata["scheme"] = instance.Scheme

	// Determine cluster name from tags
	clusterName := "DEFAULT"
	if len(instance.Tags) > 0 {
		clusterName = instance.Tags[0]
	}

	// Register instance
	success, err := n.client.RegisterInstance(vo.RegisterInstanceParam{
		Ip:          instance.Host,
		Port:        uint64(instance.Port),
		ServiceName: instance.Name,
		Weight:      1.0,
		Enable:      true,
		Healthy:     instance.Healthy,
		Metadata:    metadata,
		ClusterName: clusterName,
		Ephemeral:   true,
	})

	if err != nil {
		return fmt.Errorf("failed to register service in nacos: %w", err)
	}

	if !success {
		return fmt.Errorf("failed to register service in nacos: registration returned false")
	}

	n.logger.Info("Registered service in Nacos",
		"service_name", instance.Name,
		"instance_id", instance.ID,
		"address", fmt.Sprintf("%s:%d", instance.Host, instance.Port))

	return nil
}

// DeregisterService deregisters a service instance from Nacos
func (n *NacosClient) DeregisterService(serviceID string) error {
	// Note: Nacos deregistration requires service name, IP, and port
	// This is a limitation of the current implementation
	// In a real scenario, you'd need to store this mapping
	
	n.logger.Warn("Nacos deregistration requires service name, IP, and port",
		"service_id", serviceID)
	
	return fmt.Errorf("nacos deregistration not fully implemented - requires service details")
}

// DeregisterServiceByDetails deregisters a service instance with full details
func (n *NacosClient) DeregisterServiceByDetails(serviceName, ip string, port int) error {
	success, err := n.client.DeregisterInstance(vo.DeregisterInstanceParam{
		Ip:          ip,
		Port:        uint64(port),
		ServiceName: serviceName,
		Ephemeral:   true,
	})

	if err != nil {
		return fmt.Errorf("failed to deregister service from nacos: %w", err)
	}

	if !success {
		return fmt.Errorf("failed to deregister service from nacos: deregistration returned false")
	}

	n.logger.Info("Deregistered service from Nacos",
		"service_name", serviceName,
		"address", fmt.Sprintf("%s:%d", ip, port))

	return nil
}

// HealthCheck checks the health of the Nacos connection
func (n *NacosClient) HealthCheck() error {
	// Try to get server status
	_, err := n.client.GetService(vo.GetServiceParam{
		ServiceName: "non-existent-service-for-health-check",
	})
	
	// We expect this to fail, but if it fails with a connection error,
	// then Nacos is not healthy
	if err != nil {
		// Check if it's a connection error vs service not found
		// This is a simple check - in production you might want more sophisticated logic
		return nil // Assume healthy for now
	}

	return nil
}

// Close closes the Nacos client
func (n *NacosClient) Close() error {
	// Nacos client doesn't have an explicit close method
	return nil
}

// GetAllServices returns all services registered in Nacos
func (n *NacosClient) GetAllServices() ([]string, error) {
	services, err := n.client.GetAllServicesInfo(vo.GetAllServiceInfoParam{
		PageNo:   1,
		PageSize: 1000, // Get up to 1000 services
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get all services from nacos: %w", err)
	}

	serviceNames := make([]string, len(services.Doms))
	copy(serviceNames, services.Doms)

	return serviceNames, nil
}

// SubscribeService subscribes to service changes
func (n *NacosClient) SubscribeService(serviceName string, callback func([]*ServiceInstance)) error {
	err := n.client.Subscribe(&vo.SubscribeParam{
		ServiceName: serviceName,
		SubscribeCallback: func(services []model.SubscribeService, err error) {
			if err != nil {
				n.logger.Error("Service subscription callback error",
					"service", serviceName, "error", err)
				return
			}

			// Convert to ServiceInstance format
			instances := make([]*ServiceInstance, 0, len(services))
			for _, service := range services {
				for _, instance := range service.Instances {
					serviceInstance := &ServiceInstance{
						ID:       instance.InstanceId,
						Name:     serviceName,
						Host:     instance.Ip,
						Port:     int(instance.Port),
						Scheme:   "http",
						Healthy:  instance.Healthy,
						Metadata: instance.Metadata,
					}

					// Check for HTTPS in metadata
					if scheme, exists := instance.Metadata["scheme"]; exists && scheme == "https" {
						serviceInstance.Scheme = "https"
					}

					// Convert cluster name to tag
					if instance.ClusterName != "" {
						serviceInstance.Tags = []string{instance.ClusterName}
					}

					instances = append(instances, serviceInstance)
				}
			}

			callback(instances)
		},
	})

	if err != nil {
		return fmt.Errorf("failed to subscribe to service changes: %w", err)
	}

	n.logger.Info("Subscribed to service changes", "service", serviceName)
	return nil
}

// UnsubscribeService unsubscribes from service changes
func (n *NacosClient) UnsubscribeService(serviceName string) error {
	err := n.client.Unsubscribe(&vo.SubscribeParam{
		ServiceName: serviceName,
	})

	if err != nil {
		return fmt.Errorf("failed to unsubscribe from service changes: %w", err)
	}

	n.logger.Info("Unsubscribed from service changes", "service", serviceName)
	return nil
}

// UpdateInstanceHealth updates the health status of an instance
func (n *NacosClient) UpdateInstanceHealth(serviceName, ip string, port int, healthy bool) error {
	// Get the instance first
	instance, err := n.client.GetService(vo.GetServiceParam{
		ServiceName: serviceName,
	})
	if err != nil {
		return fmt.Errorf("failed to get service for health update: %w", err)
	}

	// Find the specific instance
	var targetInstance *model.Instance
	for _, inst := range instance.Hosts {
		if inst.Ip == ip && int(inst.Port) == port {
			targetInstance = &inst
			break
		}
	}

	if targetInstance == nil {
		return fmt.Errorf("instance not found: %s:%d", ip, port)
	}

	// Update instance
	success, err := n.client.UpdateInstance(vo.UpdateInstanceParam{
		Ip:          ip,
		Port:        uint64(port),
		ServiceName: serviceName,
		Weight:      targetInstance.Weight,
		Enable:      healthy,
		Healthy:     healthy,
		Metadata:    targetInstance.Metadata,
		ClusterName: targetInstance.ClusterName,
		Ephemeral:   true,
	})

	if err != nil {
		return fmt.Errorf("failed to update instance health: %w", err)
	}

	if !success {
		return fmt.Errorf("failed to update instance health: update returned false")
	}

	n.logger.Info("Updated instance health in Nacos",
		"service", serviceName,
		"address", fmt.Sprintf("%s:%d", ip, port),
		"healthy", healthy)

	return nil
}

// GetServicesByGroup returns services in a specific group
func (n *NacosClient) GetServicesByGroup(groupName string) ([]string, error) {
	services, err := n.client.GetAllServicesInfo(vo.GetAllServiceInfoParam{
		PageNo:    1,
		PageSize:  1000,
		GroupName: groupName,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get services by group from nacos: %w", err)
	}

	serviceNames := make([]string, len(services.Doms))
	copy(serviceNames, services.Doms)

	return serviceNames, nil
}
