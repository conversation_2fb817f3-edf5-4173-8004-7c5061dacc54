package discovery

import (
	"fmt"
	"sync"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// ServiceInstance represents a service instance
type ServiceInstance struct {
	ID       string            `json:"id"`
	Name     string            `json:"name"`
	Host     string            `json:"host"`
	Port     int               `json:"port"`
	Scheme   string            `json:"scheme"`
	Healthy  bool              `json:"healthy"`
	Metadata map[string]string `json:"metadata"`
	Tags     []string          `json:"tags"`
}

// DiscoveryClient interface defines service discovery operations
type DiscoveryClient interface {
	GetServiceInstances(serviceName string) ([]*ServiceInstance, error)
	RegisterService(instance *ServiceInstance) error
	DeregisterService(serviceID string) error
	HealthCheck() error
	Close() error
}

// Manager manages service discovery operations
type Manager struct {
	config *config.DiscoveryConfig
	logger *telemetry.Logger
	client DiscoveryClient
	
	// Cache for service instances
	cache map[string][]*ServiceInstance
	mu    sync.RWMutex
	
	// Background refresh
	stopCh chan struct{}
	wg     sync.WaitGroup
}

// NewManager creates a new discovery manager
func NewManager(cfg config.DiscoveryConfig, logger *telemetry.Logger) (*Manager, error) {
	manager := &Manager{
		config: &cfg,
		logger: logger,
		cache:  make(map[string][]*ServiceInstance),
		stopCh: make(chan struct{}),
	}

	// Initialize discovery client based on type
	var err error
	switch cfg.Type {
	case "consul":
		manager.client, err = NewConsulClient(cfg.Consul, logger)
	case "nacos":
		manager.client, err = NewNacosClient(cfg.Nacos, logger)
	case "eureka":
		return nil, fmt.Errorf("eureka discovery not implemented yet")
	case "":
		// No service discovery configured
		return manager, nil
	default:
		return nil, fmt.Errorf("unsupported discovery type: %s", cfg.Type)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create discovery client: %w", err)
	}

	// Start background refresh
	manager.startBackgroundRefresh()

	return manager, nil
}

// GetServiceInstances returns service instances for a given service name
func (m *Manager) GetServiceInstances(serviceName string) ([]*ServiceInstance, error) {
	if m.client == nil {
		return nil, fmt.Errorf("service discovery not configured")
	}

	// Try cache first
	m.mu.RLock()
	if instances, exists := m.cache[serviceName]; exists {
		m.mu.RUnlock()
		return instances, nil
	}
	m.mu.RUnlock()

	// Fetch from discovery service
	instances, err := m.client.GetServiceInstances(serviceName)
	if err != nil {
		return nil, fmt.Errorf("failed to get service instances: %w", err)
	}

	// Update cache
	m.mu.Lock()
	m.cache[serviceName] = instances
	m.mu.Unlock()

	return instances, nil
}

// RegisterService registers a service instance
func (m *Manager) RegisterService(instance *ServiceInstance) error {
	if m.client == nil {
		return fmt.Errorf("service discovery not configured")
	}

	return m.client.RegisterService(instance)
}

// DeregisterService deregisters a service instance
func (m *Manager) DeregisterService(serviceID string) error {
	if m.client == nil {
		return fmt.Errorf("service discovery not configured")
	}

	return m.client.DeregisterService(serviceID)
}

// HealthStatus returns the health status of the discovery service
func (m *Manager) HealthStatus() string {
	if m.client == nil {
		return "disabled"
	}

	if err := m.client.HealthCheck(); err != nil {
		return "unhealthy"
	}

	return "healthy"
}

// RefreshCache manually refreshes the service cache
func (m *Manager) RefreshCache() error {
	if m.client == nil {
		return nil
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	// Clear cache and let it be rebuilt on next request
	m.cache = make(map[string][]*ServiceInstance)

	return nil
}

// startBackgroundRefresh starts background cache refresh
func (m *Manager) startBackgroundRefresh() {
	if m.client == nil {
		return
	}

	m.wg.Add(1)
	go func() {
		defer m.wg.Done()
		
		ticker := time.NewTicker(30 * time.Second) // Refresh every 30 seconds
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				m.refreshCacheBackground()
			case <-m.stopCh:
				return
			}
		}
	}()
}

// refreshCacheBackground refreshes cache in background
func (m *Manager) refreshCacheBackground() {
	m.mu.RLock()
	serviceNames := make([]string, 0, len(m.cache))
	for serviceName := range m.cache {
		serviceNames = append(serviceNames, serviceName)
	}
	m.mu.RUnlock()

	// Refresh each cached service
	for _, serviceName := range serviceNames {
		instances, err := m.client.GetServiceInstances(serviceName)
		if err != nil {
			m.logger.Error("Failed to refresh service instances", 
				"service", serviceName, "error", err)
			continue
		}

		m.mu.Lock()
		m.cache[serviceName] = instances
		m.mu.Unlock()

		m.logger.Debug("Refreshed service instances", 
			"service", serviceName, "count", len(instances))
	}
}

// Close closes the discovery manager
func (m *Manager) Close() error {
	// Stop background refresh
	close(m.stopCh)
	m.wg.Wait()

	// Close discovery client
	if m.client != nil {
		return m.client.Close()
	}

	return nil
}
