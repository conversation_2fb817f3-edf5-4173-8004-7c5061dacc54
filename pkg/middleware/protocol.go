package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// WebSocket upgrader with default settings
var wsUpgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// Allow all origins for now - in production, implement proper origin checking
		return true
	},
}

// ProtocolHandler middleware handles different protocols
func ProtocolHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for WebSocket upgrade request
		if isWebSocketUpgrade(c.Request) {
			handleWebSocketUpgrade(c)
			return
		}

		// Handle HTTP/2 specific features
		if c.Request.ProtoMajor == 2 {
			handleHTTP2Features(c)
		}

		// Set protocol version header
		c.Header("X-Protocol-Version", c.Request.Proto)

		c.Next()
	}
}

// isWebSocketUpgrade checks if the request is a WebSocket upgrade request
func isWebSocketUpgrade(r *http.Request) bool {
	return strings.ToLower(r.Header.Get("Connection")) == "upgrade" &&
		strings.ToLower(r.Header.Get("Upgrade")) == "websocket"
}

// handleWebSocketUpgrade handles WebSocket upgrade requests
func handleWebSocketUpgrade(c *gin.Context) {
	// Upgrade the HTTP connection to WebSocket
	conn, err := wsUpgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "websocket upgrade failed",
			"message": "Failed to upgrade connection to WebSocket",
		})
		return
	}
	defer conn.Close()

	// Store WebSocket connection in context for downstream handlers
	c.Set("websocket_conn", conn)

	// Handle WebSocket communication
	handleWebSocketConnection(conn, c)
}

// handleWebSocketConnection handles WebSocket communication
func handleWebSocketConnection(conn *websocket.Conn, c *gin.Context) {
	// This is a basic WebSocket handler
	// In a real implementation, you would:
	// 1. Route WebSocket messages to appropriate handlers
	// 2. Implement proper message handling
	// 3. Handle connection lifecycle
	// 4. Implement authentication and authorization for WebSocket connections

	for {
		// Read message from client
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				// Log unexpected close error
			}
			break
		}

		// Echo the message back (simple example)
		err = conn.WriteMessage(messageType, message)
		if err != nil {
			break
		}
	}
}

// handleHTTP2Features handles HTTP/2 specific features
func handleHTTP2Features(c *gin.Context) {
	// Enable server push if supported
	if pusher, ok := c.Writer.(http.Pusher); ok {
		// Example: push CSS and JS resources
		pushResources := []string{
			"/static/css/main.css",
			"/static/js/main.js",
		}

		for _, resource := range pushResources {
			if err := pusher.Push(resource, nil); err != nil {
				// Log push error but continue
				break
			}
		}
	}

	// Set HTTP/2 specific headers
	c.Header("X-HTTP2-Enabled", "true")
}

// ContentNegotiation middleware handles content negotiation
func ContentNegotiation() gin.HandlerFunc {
	return func(c *gin.Context) {
		acceptHeader := c.GetHeader("Accept")
		
		// Determine response format based on Accept header
		switch {
		case strings.Contains(acceptHeader, "application/json"):
			c.Set("response_format", "json")
		case strings.Contains(acceptHeader, "application/xml"):
			c.Set("response_format", "xml")
		case strings.Contains(acceptHeader, "text/html"):
			c.Set("response_format", "html")
		case strings.Contains(acceptHeader, "text/plain"):
			c.Set("response_format", "text")
		default:
			c.Set("response_format", "json") // Default to JSON
		}

		c.Next()
	}
}

// CompressionHandler middleware handles response compression
func CompressionHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if client accepts compression
		acceptEncoding := c.GetHeader("Accept-Encoding")
		
		if strings.Contains(acceptEncoding, "gzip") {
			c.Header("Content-Encoding", "gzip")
			c.Set("compression", "gzip")
		} else if strings.Contains(acceptEncoding, "deflate") {
			c.Header("Content-Encoding", "deflate")
			c.Set("compression", "deflate")
		}

		c.Next()
	}
}

// KeepAlive middleware manages connection keep-alive
func KeepAlive() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Set keep-alive headers
		c.Header("Connection", "keep-alive")
		c.Header("Keep-Alive", "timeout=5, max=1000")

		c.Next()
	}
}

// RequestTransform middleware transforms requests
func RequestTransform() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Transform request headers
		transformRequestHeaders(c)
		
		// Transform request body if needed
		transformRequestBody(c)

		c.Next()
	}
}

// ResponseTransform middleware transforms responses
func ResponseTransform() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Process request first
		c.Next()

		// Transform response headers
		transformResponseHeaders(c)
		
		// Transform response body if needed
		transformResponseBody(c)
	}
}

// transformRequestHeaders transforms request headers
func transformRequestHeaders(c *gin.Context) {
	// Add gateway identification
	c.Request.Header.Set("X-Gateway", "api-gateway")
	c.Request.Header.Set("X-Gateway-Version", "1.0.0")
	
	// Add request timestamp
	c.Request.Header.Set("X-Request-Time", "2023-01-01T00:00:00Z") // Use actual timestamp
	
	// Remove sensitive headers before forwarding
	sensitiveHeaders := []string{
		"Authorization", // Will be handled by auth middleware
		"Cookie",        // Handle cookies separately
	}
	
	for _, header := range sensitiveHeaders {
		if c.GetHeader("X-Forward-"+header) != "true" {
			c.Request.Header.Del(header)
		}
	}
}

// transformRequestBody transforms request body
func transformRequestBody(c *gin.Context) {
	// This is where you would implement request body transformation
	// For example:
	// - Convert between different data formats
	// - Add/remove fields
	// - Validate and sanitize input
	
	contentType := c.GetHeader("Content-Type")
	
	switch {
	case strings.Contains(contentType, "application/json"):
		// Handle JSON transformation
		handleJSONTransformation(c)
	case strings.Contains(contentType, "application/xml"):
		// Handle XML transformation
		handleXMLTransformation(c)
	case strings.Contains(contentType, "application/x-www-form-urlencoded"):
		// Handle form data transformation
		handleFormTransformation(c)
	}
}

// transformResponseHeaders transforms response headers
func transformResponseHeaders(c *gin.Context) {
	// Add response headers
	c.Header("X-Gateway-Response", "processed")
	c.Header("X-Response-Time", "2023-01-01T00:00:00Z") // Use actual timestamp
	
	// Remove sensitive response headers
	sensitiveResponseHeaders := []string{
		"Server",
		"X-Powered-By",
	}
	
	for _, header := range sensitiveResponseHeaders {
		c.Header(header, "")
	}
}

// transformResponseBody transforms response body
func transformResponseBody(c *gin.Context) {
	// This is where you would implement response body transformation
	// For example:
	// - Convert between different data formats
	// - Add/remove fields
	// - Filter sensitive data
	
	responseFormat, exists := c.Get("response_format")
	if !exists {
		return
	}
	
	switch responseFormat {
	case "json":
		// Handle JSON response transformation
		handleJSONResponseTransformation(c)
	case "xml":
		// Handle XML response transformation
		handleXMLResponseTransformation(c)
	case "html":
		// Handle HTML response transformation
		handleHTMLResponseTransformation(c)
	}
}

// Placeholder transformation functions
func handleJSONTransformation(c *gin.Context) {
	// Implement JSON request transformation
}

func handleXMLTransformation(c *gin.Context) {
	// Implement XML request transformation
}

func handleFormTransformation(c *gin.Context) {
	// Implement form data transformation
}

func handleJSONResponseTransformation(c *gin.Context) {
	// Implement JSON response transformation
}

func handleXMLResponseTransformation(c *gin.Context) {
	// Implement XML response transformation
}

func handleHTMLResponseTransformation(c *gin.Context) {
	// Implement HTML response transformation
}

// ProtocolUpgrade middleware handles protocol upgrades
func ProtocolUpgrade() gin.HandlerFunc {
	return func(c *gin.Context) {
		upgrade := c.GetHeader("Upgrade")
		
		switch strings.ToLower(upgrade) {
		case "websocket":
			// WebSocket upgrade is handled by ProtocolHandler
			c.Next()
		case "h2c":
			// HTTP/2 cleartext upgrade
			handleH2CUpgrade(c)
		default:
			c.Next()
		}
	}
}

// handleH2CUpgrade handles HTTP/2 cleartext upgrade
func handleH2CUpgrade(c *gin.Context) {
	// This is a placeholder for HTTP/2 cleartext upgrade
	// In a real implementation, you would:
	// 1. Validate the upgrade request
	// 2. Perform the protocol upgrade
	// 3. Handle HTTP/2 communication
	
	c.Header("Connection", "Upgrade")
	c.Header("Upgrade", "h2c")
	c.Status(http.StatusSwitchingProtocols)
	c.Abort()
}
