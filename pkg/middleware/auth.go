package middleware

import (
	"net/http"
	"strings"

	"api-gateway/pkg/auth"
	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
)

// Authentication middleware performs authentication and authorization
func Authentication(authManager *auth.Manager, logger *telemetry.Logger, metrics *telemetry.Metrics) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip authentication for health check and admin endpoints
		if shouldSkipAuth(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Create authentication context
		authCtx := createAuthContext(c)

		// Perform authentication
		authResult, err := authManager.Authenticate(c.Request.Context(), authCtx)
		if err != nil {
			logger.Error("Authentication error",
				"request_id", authCtx.RequestID,
				"error", err)
			
			if metrics != nil {
				metrics.RecordAuthFailure("unknown", "authentication_error")
			}

			c.<PERSON>(http.StatusInternalServerError, gin.H{
				"error":   "authentication error",
				"message": "An error occurred during authentication",
			})
			c.Abort()
			return
		}

		// Check if authentication was successful
		if !authResult.Authenticated {
			logger.Warn("Authentication failed",
				"request_id", authCtx.RequestID,
				"client_ip", authCtx.ClientIP,
				"path", authCtx.Path,
				"reason", authResult.Error)

			if metrics != nil {
				metrics.RecordAuthFailure(authResult.TokenType, authResult.Error)
			}

			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication failed",
				"message": "Invalid or missing authentication credentials",
			})
			c.Abort()
			return
		}

		// Perform authorization
		authorized, err := authManager.Authorize(c.Request.Context(), authResult, authCtx)
		if err != nil {
			logger.Error("Authorization error",
				"request_id", authCtx.RequestID,
				"user_id", authResult.UserID,
				"error", err)

			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "authorization error",
				"message": "An error occurred during authorization",
			})
			c.Abort()
			return
		}

		if !authorized {
			logger.Warn("Authorization denied",
				"request_id", authCtx.RequestID,
				"user_id", authResult.UserID,
				"path", authCtx.Path,
				"method", authCtx.Method)

			c.JSON(http.StatusForbidden, gin.H{
				"error":   "access denied",
				"message": "Insufficient permissions to access this resource",
			})
			c.Abort()
			return
		}

		// Store authentication result in context for downstream use
		c.Set("auth_result", authResult)
		c.Set("user_id", authResult.UserID)
		c.Set("username", authResult.Username)
		c.Set("user_roles", authResult.Roles)
		c.Set("user_permissions", authResult.Permissions)

		// Add user information to response headers
		c.Header("X-User-ID", authResult.UserID)
		c.Header("X-Username", authResult.Username)

		logger.Debug("Authentication and authorization successful",
			"request_id", authCtx.RequestID,
			"user_id", authResult.UserID,
			"username", authResult.Username,
			"token_type", authResult.TokenType)

		c.Next()
	}
}

// shouldSkipAuth determines if authentication should be skipped for a path
func shouldSkipAuth(path string) bool {
	skipPaths := []string{
		"/health",
		"/metrics",
		"/admin/health",
		"/favicon.ico",
	}

	for _, skipPath := range skipPaths {
		if path == skipPath {
			return true
		}
	}

	// Skip authentication for public paths
	if strings.HasPrefix(path, "/public/") {
		return true
	}

	return false
}

// createAuthContext creates an authentication context from the Gin context
func createAuthContext(c *gin.Context) *auth.AuthContext {
	// Get request ID
	requestID, _ := c.Get("request_id")
	requestIDStr := ""
	if requestID != nil {
		requestIDStr = requestID.(string)
	}

	// Extract headers
	headers := make(map[string]string)
	for key, values := range c.Request.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	// Get real IP
	clientIP := c.ClientIP()
	if realIP, exists := c.Get("real_ip"); exists {
		clientIP = realIP.(string)
	}

	return &auth.AuthContext{
		RequestID: requestIDStr,
		ClientIP:  clientIP,
		UserAgent: c.Request.UserAgent(),
		Path:      c.Request.URL.Path,
		Method:    c.Request.Method,
		Headers:   headers,
	}
}

// RequireRole middleware requires specific roles
func RequireRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		authResult, exists := c.Get("auth_result")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication required",
				"message": "This endpoint requires authentication",
			})
			c.Abort()
			return
		}

		result := authResult.(*auth.AuthResult)
		if !result.Authenticated {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication required",
				"message": "This endpoint requires authentication",
			})
			c.Abort()
			return
		}

		// Check if user has any of the required roles
		hasRole := false
		for _, requiredRole := range roles {
			for _, userRole := range result.Roles {
				if strings.EqualFold(requiredRole, userRole) {
					hasRole = true
					break
				}
			}
			if hasRole {
				break
			}
		}

		if !hasRole {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "insufficient privileges",
				"message": "This endpoint requires specific roles",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequirePermission middleware requires specific permissions
func RequirePermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		authResult, exists := c.Get("auth_result")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication required",
				"message": "This endpoint requires authentication",
			})
			c.Abort()
			return
		}

		result := authResult.(*auth.AuthResult)
		if !result.Authenticated {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication required",
				"message": "This endpoint requires authentication",
			})
			c.Abort()
			return
		}

		// Check if user has any of the required permissions
		hasPermission := false
		for _, requiredPerm := range permissions {
			for _, userPerm := range result.Permissions {
				if strings.EqualFold(requiredPerm, userPerm) || userPerm == "*" {
					hasPermission = true
					break
				}
				// Check for wildcard permissions
				if strings.HasSuffix(userPerm, "*") {
					prefix := strings.TrimSuffix(userPerm, "*")
					if strings.HasPrefix(requiredPerm, prefix) {
						hasPermission = true
						break
					}
				}
			}
			if hasPermission {
				break
			}
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "insufficient permissions",
				"message": "This endpoint requires specific permissions",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// OptionalAuth middleware performs optional authentication
func OptionalAuth(authManager *auth.Manager, logger *telemetry.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create authentication context
		authCtx := createAuthContext(c)

		// Attempt authentication
		authResult, err := authManager.Authenticate(c.Request.Context(), authCtx)
		if err != nil {
			logger.Debug("Optional authentication error",
				"request_id", authCtx.RequestID,
				"error", err)
		}

		// Store result even if authentication failed
		if authResult != nil {
			c.Set("auth_result", authResult)
			if authResult.Authenticated {
				c.Set("user_id", authResult.UserID)
				c.Set("username", authResult.Username)
				c.Set("user_roles", authResult.Roles)
				c.Set("user_permissions", authResult.Permissions)
			}
		}

		c.Next()
	}
}
