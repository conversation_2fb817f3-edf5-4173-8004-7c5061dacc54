package middleware

import (
	"fmt"
	"net/http"
	"sync"
	"time"

	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
)

// CircuitBreakerState represents the state of a circuit breaker
type CircuitBreakerState int

const (
	StateClosed CircuitBreakerState = iota
	StateOpen
	StateHalfOpen
)

// CircuitBreaker represents a circuit breaker instance
type CircuitBreaker struct {
	name             string
	failureThreshold int
	recoveryTimeout  time.Duration
	timeout          time.Duration
	
	// State management
	state        CircuitBreakerState
	failures     int
	lastFailTime time.Time
	mu           sync.RWMutex
	
	// Metrics
	metrics *telemetry.Metrics
	logger  *telemetry.Logger
}

// CircuitBreakerConfig contains circuit breaker configuration
type CircuitBreakerConfig struct {
	Name             string        `yaml:"name"`
	FailureThreshold int           `yaml:"failure_threshold"`
	RecoveryTimeout  time.Duration `yaml:"recovery_timeout"`
	Timeout          time.Duration `yaml:"timeout"`
}

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(config CircuitBreakerConfig, logger *telemetry.Logger, metrics *telemetry.Metrics) *CircuitBreaker {
	cb := &CircuitBreaker{
		name:             config.Name,
		failureThreshold: config.FailureThreshold,
		recoveryTimeout:  config.RecoveryTimeout,
		timeout:          config.Timeout,
		state:            StateClosed,
		failures:         0,
		metrics:          metrics,
		logger:           logger,
	}

	// Set default values if not provided
	if cb.failureThreshold == 0 {
		cb.failureThreshold = 5
	}
	if cb.recoveryTimeout == 0 {
		cb.recoveryTimeout = 30 * time.Second
	}
	if cb.timeout == 0 {
		cb.timeout = 10 * time.Second
	}

	return cb
}

// Execute executes a function with circuit breaker protection
func (cb *CircuitBreaker) Execute(fn func() error) error {
	// Check if circuit breaker allows execution
	if !cb.allowRequest() {
		return fmt.Errorf("circuit breaker is open")
	}

	// Execute with timeout
	done := make(chan error, 1)
	go func() {
		done <- fn()
	}()

	select {
	case err := <-done:
		cb.recordResult(err)
		return err
	case <-time.After(cb.timeout):
		cb.recordResult(fmt.Errorf("timeout"))
		return fmt.Errorf("request timeout")
	}
}

// allowRequest checks if the circuit breaker allows a request
func (cb *CircuitBreaker) allowRequest() bool {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	switch cb.state {
	case StateClosed:
		return true
	case StateOpen:
		// Check if recovery timeout has passed
		if time.Since(cb.lastFailTime) > cb.recoveryTimeout {
			cb.state = StateHalfOpen
			cb.logger.Info("Circuit breaker transitioning to half-open", "name", cb.name)
			if cb.metrics != nil {
				cb.metrics.SetCircuitBreakerState(cb.name, int(StateHalfOpen))
			}
			return true
		}
		return false
	case StateHalfOpen:
		return true
	default:
		return false
	}
}

// recordResult records the result of a request
func (cb *CircuitBreaker) recordResult(err error) {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	if err != nil {
		cb.failures++
		cb.lastFailTime = time.Now()

		// Check if we should open the circuit
		if cb.failures >= cb.failureThreshold {
			cb.state = StateOpen
			cb.logger.Warn("Circuit breaker opened", 
				"name", cb.name, 
				"failures", cb.failures)
			
			if cb.metrics != nil {
				cb.metrics.SetCircuitBreakerState(cb.name, int(StateOpen))
				cb.metrics.RecordCircuitBreakerTrip(cb.name)
			}
		}
	} else {
		// Success - reset failures and close circuit if it was half-open
		if cb.state == StateHalfOpen {
			cb.state = StateClosed
			cb.logger.Info("Circuit breaker closed", "name", cb.name)
			if cb.metrics != nil {
				cb.metrics.SetCircuitBreakerState(cb.name, int(StateClosed))
			}
		}
		cb.failures = 0
	}
}

// GetState returns the current state of the circuit breaker
func (cb *CircuitBreaker) GetState() CircuitBreakerState {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.state
}

// GetFailures returns the current failure count
func (cb *CircuitBreaker) GetFailures() int {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.failures
}

// Reset resets the circuit breaker to closed state
func (cb *CircuitBreaker) Reset() {
	cb.mu.Lock()
	defer cb.mu.Unlock()
	
	cb.state = StateClosed
	cb.failures = 0
	cb.logger.Info("Circuit breaker reset", "name", cb.name)
	
	if cb.metrics != nil {
		cb.metrics.SetCircuitBreakerState(cb.name, int(StateClosed))
	}
}

// CircuitBreakerManager manages multiple circuit breakers
type CircuitBreakerManager struct {
	breakers map[string]*CircuitBreaker
	mu       sync.RWMutex
	logger   *telemetry.Logger
	metrics  *telemetry.Metrics
}

// NewCircuitBreakerManager creates a new circuit breaker manager
func NewCircuitBreakerManager(logger *telemetry.Logger, metrics *telemetry.Metrics) *CircuitBreakerManager {
	return &CircuitBreakerManager{
		breakers: make(map[string]*CircuitBreaker),
		logger:   logger,
		metrics:  metrics,
	}
}

// GetCircuitBreaker gets or creates a circuit breaker for a service
func (cbm *CircuitBreakerManager) GetCircuitBreaker(name string, config CircuitBreakerConfig) *CircuitBreaker {
	cbm.mu.RLock()
	cb, exists := cbm.breakers[name]
	cbm.mu.RUnlock()

	if exists {
		return cb
	}

	cbm.mu.Lock()
	defer cbm.mu.Unlock()

	// Double-check after acquiring write lock
	if cb, exists := cbm.breakers[name]; exists {
		return cb
	}

	// Create new circuit breaker
	config.Name = name
	cb = NewCircuitBreaker(config, cbm.logger, cbm.metrics)
	cbm.breakers[name] = cb

	return cb
}

// CircuitBreakerMiddleware creates a circuit breaker middleware
func CircuitBreakerMiddleware(manager *CircuitBreakerManager, config CircuitBreakerConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get upstream service name
		upstream, exists := c.Get("upstream")
		if !exists {
			// No upstream specified, continue without circuit breaker
			c.Next()
			return
		}

		upstreamName := upstream.(string)
		cb := manager.GetCircuitBreaker(upstreamName, config)

		// Execute request with circuit breaker protection
		err := cb.Execute(func() error {
			c.Next()
			
			// Check if the request was successful
			if c.Writer.Status() >= 500 {
				return fmt.Errorf("upstream error: %d", c.Writer.Status())
			}
			
			return nil
		})

		if err != nil {
			// Circuit breaker prevented execution or request failed
			if err.Error() == "circuit breaker is open" {
				c.JSON(http.StatusServiceUnavailable, gin.H{
					"error":   "service unavailable",
					"message": "Service is temporarily unavailable due to high error rate",
				})
			} else if err.Error() == "request timeout" {
				c.JSON(http.StatusGatewayTimeout, gin.H{
					"error":   "gateway timeout",
					"message": "Request to upstream service timed out",
				})
			} else {
				c.JSON(http.StatusBadGateway, gin.H{
					"error":   "bad gateway",
					"message": "Error communicating with upstream service",
				})
			}
			c.Abort()
		}
	}
}

// BulkheadMiddleware implements bulkhead pattern for resource isolation
func BulkheadMiddleware(maxConcurrent int) gin.HandlerFunc {
	semaphore := make(chan struct{}, maxConcurrent)
	
	return func(c *gin.Context) {
		select {
		case semaphore <- struct{}{}:
			// Acquired semaphore, proceed with request
			defer func() { <-semaphore }()
			c.Next()
		default:
			// Semaphore full, reject request
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error":   "service overloaded",
				"message": "Service is currently overloaded, please try again later",
			})
			c.Abort()
		}
	}
}

// RetryMiddleware implements retry logic with exponential backoff
func RetryMiddleware(maxRetries int, baseDelay time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		var lastErr error
		
		for attempt := 0; attempt <= maxRetries; attempt++ {
			// Create a copy of the response writer to capture the response
			recorder := &responseRecorder{
				ResponseWriter: c.Writer,
				statusCode:     200,
			}
			c.Writer = recorder

			// Execute the request
			c.Next()

			// Check if request was successful
			if recorder.statusCode < 500 {
				// Success or client error, don't retry
				return
			}

			// Server error, prepare for retry
			lastErr = fmt.Errorf("upstream error: %d", recorder.statusCode)

			// Don't retry on the last attempt
			if attempt < maxRetries {
				// Calculate delay with exponential backoff
				delay := baseDelay * time.Duration(1<<uint(attempt))
				time.Sleep(delay)

				// Reset the context for retry
				c.Writer = recorder.ResponseWriter
				c.Abort() // Clear any previous handlers
			}
		}

		// All retries failed
		c.JSON(http.StatusBadGateway, gin.H{
			"error":   "bad gateway",
			"message": fmt.Sprintf("Request failed after %d retries: %v", maxRetries, lastErr),
		})
	}
}

// responseRecorder captures response details for retry logic
type responseRecorder struct {
	gin.ResponseWriter
	statusCode int
}

func (r *responseRecorder) WriteHeader(statusCode int) {
	r.statusCode = statusCode
	r.ResponseWriter.WriteHeader(statusCode)
}

// HealthCheckCircuitBreaker performs health checks on circuit breakers
func HealthCheckCircuitBreaker(manager *CircuitBreakerManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		manager.mu.RLock()
		defer manager.mu.RUnlock()

		status := make(map[string]interface{})
		overallHealthy := true

		for name, cb := range manager.breakers {
			state := cb.GetState()
			failures := cb.GetFailures()
			
			breakerStatus := map[string]interface{}{
				"state":    stateToString(state),
				"failures": failures,
				"healthy":  state == StateClosed,
			}
			
			status[name] = breakerStatus
			
			if state != StateClosed {
				overallHealthy = false
			}
		}

		response := gin.H{
			"circuit_breakers": status,
			"healthy":          overallHealthy,
		}

		if overallHealthy {
			c.JSON(http.StatusOK, response)
		} else {
			c.JSON(http.StatusServiceUnavailable, response)
		}
	}
}

// stateToString converts circuit breaker state to string
func stateToString(state CircuitBreakerState) string {
	switch state {
	case StateClosed:
		return "closed"
	case StateOpen:
		return "open"
	case StateHalfOpen:
		return "half-open"
	default:
		return "unknown"
	}
}
