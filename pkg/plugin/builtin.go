package plugin

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"api-gateway/pkg/telemetry"
	"github.com/gin-gonic/gin"
)

// LoggingPlugin logs request/response information
type LoggingPlugin struct {
	logger *telemetry.Logger
	config map[string]interface{}
}

// NewLoggingPlugin creates a new logging plugin
func NewLoggingPlugin(logger *telemetry.Logger) *LoggingPlugin {
	return &LoggingPlugin{
		logger: logger.With("plugin", "logging"),
		config: make(map[string]interface{}),
	}
}

func (p *LoggingPlugin) Name() string        { return "logging" }
func (p *LoggingPlugin) Version() string     { return "1.0.0" }
func (p *LoggingPlugin) Description() string { return "Logs request and response information" }
func (p *LoggingPlugin) Priority() int       { return 1000 }

func (p *LoggingPlugin) Phases() []PluginPhase {
	return []PluginPhase{PhasePreAuth, PhasePostProxy}
}

func (p *LoggingPlugin) Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
	switch phase {
	case PhasePreAuth:
		p.logger.Info("Request received",
			"request_id", pluginCtx.RequestID,
			"method", pluginCtx.Method,
			"path", pluginCtx.Path,
			"client_ip", pluginCtx.ClientIP)
	case PhasePostProxy:
		p.logger.Info("Request processed",
			"request_id", pluginCtx.RequestID,
			"user_id", pluginCtx.UserID)
	}

	return &PluginResult{Continue: true}, nil
}

func (p *LoggingPlugin) Configure(config map[string]interface{}) error {
	p.config = config
	return nil
}

func (p *LoggingPlugin) Start() error       { return nil }
func (p *LoggingPlugin) Stop() error        { return nil }
func (p *LoggingPlugin) HealthCheck() error { return nil }

// MetricsPlugin records metrics
type MetricsPlugin struct {
	metrics *telemetry.Metrics
	config  map[string]interface{}
}

// NewMetricsPlugin creates a new metrics plugin
func NewMetricsPlugin(metrics *telemetry.Metrics) *MetricsPlugin {
	return &MetricsPlugin{
		metrics: metrics,
		config:  make(map[string]interface{}),
	}
}

func (p *MetricsPlugin) Name() string        { return "metrics" }
func (p *MetricsPlugin) Version() string     { return "1.0.0" }
func (p *MetricsPlugin) Description() string { return "Records request metrics" }
func (p *MetricsPlugin) Priority() int       { return 900 }

func (p *MetricsPlugin) Phases() []PluginPhase {
	return []PluginPhase{PhasePreAuth, PhasePostProxy}
}

func (p *MetricsPlugin) Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
	if p.metrics == nil {
		return &PluginResult{Continue: true}, nil
	}

	switch phase {
	case PhasePreAuth:
		// Record request start
		pluginCtx.Metadata["request_start_time"] = time.Now()
	case PhasePostProxy:
		// Record request completion
		if startTime, ok := pluginCtx.Metadata["request_start_time"].(time.Time); ok {
			duration := time.Since(startTime)
			// Metrics are recorded in the main middleware
			_ = duration
		}
	}

	return &PluginResult{Continue: true}, nil
}

func (p *MetricsPlugin) Configure(config map[string]interface{}) error {
	p.config = config
	return nil
}

func (p *MetricsPlugin) Start() error       { return nil }
func (p *MetricsPlugin) Stop() error        { return nil }
func (p *MetricsPlugin) HealthCheck() error { return nil }

// TransformPlugin transforms request/response data
type TransformPlugin struct {
	config map[string]interface{}
}

// NewTransformPlugin creates a new transform plugin
func NewTransformPlugin() *TransformPlugin {
	return &TransformPlugin{
		config: make(map[string]interface{}),
	}
}

func (p *TransformPlugin) Name() string        { return "transform" }
func (p *TransformPlugin) Version() string     { return "1.0.0" }
func (p *TransformPlugin) Description() string { return "Transforms request and response data" }
func (p *TransformPlugin) Priority() int       { return 500 }

func (p *TransformPlugin) Phases() []PluginPhase {
	return []PluginPhase{PhasePreProxy, PhasePostProxy}
}

func (p *TransformPlugin) Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
	switch phase {
	case PhasePreProxy:
		// Transform request
		if err := p.transformRequest(pluginCtx); err != nil {
			return nil, fmt.Errorf("request transformation failed: %w", err)
		}
	case PhasePostProxy:
		// Transform response
		if err := p.transformResponse(pluginCtx); err != nil {
			return nil, fmt.Errorf("response transformation failed: %w", err)
		}
	}

	return &PluginResult{Continue: true}, nil
}

func (p *TransformPlugin) transformRequest(pluginCtx *PluginContext) error {
	// Add transformation headers
	if pluginCtx.Headers == nil {
		pluginCtx.Headers = make(map[string]string)
	}
	pluginCtx.Headers["X-Transformed-By"] = "api-gateway"
	pluginCtx.Headers["X-Transform-Time"] = time.Now().Format(time.RFC3339)

	return nil
}

func (p *TransformPlugin) transformResponse(pluginCtx *PluginContext) error {
	// Add response transformation metadata
	if pluginCtx.Metadata == nil {
		pluginCtx.Metadata = make(map[string]interface{})
	}
	pluginCtx.Metadata["response_transformed"] = true

	return nil
}

func (p *TransformPlugin) Configure(config map[string]interface{}) error {
	p.config = config
	return nil
}

func (p *TransformPlugin) Start() error       { return nil }
func (p *TransformPlugin) Stop() error        { return nil }
func (p *TransformPlugin) HealthCheck() error { return nil }

// ValidationPlugin validates requests
type ValidationPlugin struct {
	config map[string]interface{}
}

// NewValidationPlugin creates a new validation plugin
func NewValidationPlugin() *ValidationPlugin {
	return &ValidationPlugin{
		config: make(map[string]interface{}),
	}
}

func (p *ValidationPlugin) Name() string        { return "validation" }
func (p *ValidationPlugin) Version() string     { return "1.0.0" }
func (p *ValidationPlugin) Description() string { return "Validates request data" }
func (p *ValidationPlugin) Priority() int       { return 200 }

func (p *ValidationPlugin) Phases() []PluginPhase {
	return []PluginPhase{PhasePreAuth}
}

func (p *ValidationPlugin) Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
	if phase == PhasePreAuth {
		if err := p.validateRequest(pluginCtx); err != nil {
			return &PluginResult{
				Continue:   false,
				StatusCode: 400,
				Body:       []byte(fmt.Sprintf(`{"error": "validation failed", "message": "%s"}`, err.Error())),
				Headers:    map[string]string{"Content-Type": "application/json"},
			}, nil
		}
	}

	return &PluginResult{Continue: true}, nil
}

func (p *ValidationPlugin) validateRequest(pluginCtx *PluginContext) error {
	// Basic validation rules
	if pluginCtx.Method == "" {
		return fmt.Errorf("HTTP method is required")
	}

	if pluginCtx.Path == "" {
		return fmt.Errorf("request path is required")
	}

	// Validate content type for POST/PUT requests
	if pluginCtx.Method == "POST" || pluginCtx.Method == "PUT" {
		contentType := pluginCtx.Headers["Content-Type"]
		if contentType == "" {
			return fmt.Errorf("Content-Type header is required for %s requests", pluginCtx.Method)
		}
	}

	return nil
}

func (p *ValidationPlugin) Configure(config map[string]interface{}) error {
	p.config = config
	return nil
}

func (p *ValidationPlugin) Start() error       { return nil }
func (p *ValidationPlugin) Stop() error        { return nil }
func (p *ValidationPlugin) HealthCheck() error { return nil }

// CachePlugin provides caching functionality
type CachePlugin struct {
	config map[string]interface{}
	cache  map[string]CacheEntry
}

// CacheEntry represents a cached entry
type CacheEntry struct {
	Data      []byte    `json:"data"`
	Headers   map[string]string `json:"headers"`
	ExpiresAt time.Time `json:"expires_at"`
}

// NewCachePlugin creates a new cache plugin
func NewCachePlugin() *CachePlugin {
	return &CachePlugin{
		config: make(map[string]interface{}),
		cache:  make(map[string]CacheEntry),
	}
}

func (p *CachePlugin) Name() string        { return "cache" }
func (p *CachePlugin) Version() string     { return "1.0.0" }
func (p *CachePlugin) Description() string { return "Provides response caching" }
func (p *CachePlugin) Priority() int       { return 300 }

func (p *CachePlugin) Phases() []PluginPhase {
	return []PluginPhase{PhasePreProxy, PhasePostProxy}
}

func (p *CachePlugin) Execute(ctx context.Context, phase PluginPhase, pluginCtx *PluginContext) (*PluginResult, error) {
	switch phase {
	case PhasePreProxy:
		// Check cache for GET requests
		if pluginCtx.Method == "GET" {
			if entry, found := p.getFromCache(pluginCtx.Path); found {
				return &PluginResult{
					Continue:   false,
					StatusCode: 200,
					Body:       entry.Data,
					Headers:    entry.Headers,
				}, nil
			}
		}
	case PhasePostProxy:
		// Cache successful GET responses
		if pluginCtx.Method == "GET" && len(pluginCtx.Body) > 0 {
			p.setCache(pluginCtx.Path, pluginCtx.Body, pluginCtx.Headers)
		}
	}

	return &PluginResult{Continue: true}, nil
}

func (p *CachePlugin) getFromCache(key string) (CacheEntry, bool) {
	entry, exists := p.cache[key]
	if !exists {
		return CacheEntry{}, false
	}

	if time.Now().After(entry.ExpiresAt) {
		delete(p.cache, key)
		return CacheEntry{}, false
	}

	return entry, true
}

func (p *CachePlugin) setCache(key string, data []byte, headers map[string]string) {
	// Default cache duration: 5 minutes
	duration := 5 * time.Minute
	
	if ttl, exists := p.config["ttl"]; exists {
		if ttlDuration, ok := ttl.(time.Duration); ok {
			duration = ttlDuration
		}
	}

	entry := CacheEntry{
		Data:      data,
		Headers:   headers,
		ExpiresAt: time.Now().Add(duration),
	}

	p.cache[key] = entry
}

func (p *CachePlugin) Configure(config map[string]interface{}) error {
	p.config = config
	return nil
}

func (p *CachePlugin) Start() error       { return nil }
func (p *CachePlugin) Stop() error        { return nil }
func (p *CachePlugin) HealthCheck() error { return nil }

// PluginExecutionMiddleware creates middleware for plugin execution
func PluginExecutionMiddleware(manager *Manager, logger *telemetry.Logger, metrics *telemetry.Metrics) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Create plugin context
		pluginCtx := &PluginContext{
			RequestID:   getRequestID(c),
			ClientIP:    c.ClientIP(),
			Path:        c.Request.URL.Path,
			Method:      c.Request.Method,
			Headers:     extractHeaders(c),
			QueryParams: extractQueryParams(c),
			Metadata:    make(map[string]interface{}),
		}

		// Execute pre-auth plugins
		if result, err := manager.ExecutePhase(c.Request.Context(), PhasePreAuth, pluginCtx); err != nil {
			logger.Error("Pre-auth plugin execution failed", "error", err)
		} else if result != nil && !result.Continue {
			handlePluginResult(c, result)
			return
		}

		// Continue with request processing
		c.Next()

		// Execute post-proxy plugins
		if result, err := manager.ExecutePhase(c.Request.Context(), PhasePostProxy, pluginCtx); err != nil {
			logger.Error("Post-proxy plugin execution failed", "error", err)
		} else if result != nil && !result.Continue {
			handlePluginResult(c, result)
			return
		}
	}
}

// Helper functions
func getRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		return requestID.(string)
	}
	return ""
}

func extractHeaders(c *gin.Context) map[string]string {
	headers := make(map[string]string)
	for key, values := range c.Request.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}
	return headers
}

func extractQueryParams(c *gin.Context) map[string]string {
	params := make(map[string]string)
	for key, values := range c.Request.URL.Query() {
		if len(values) > 0 {
			params[key] = values[0]
		}
	}
	return params
}

func handlePluginResult(c *gin.Context, result *PluginResult) {
	// Set headers
	for key, value := range result.Headers {
		c.Header(key, value)
	}

	// Set status code and body
	if result.StatusCode > 0 {
		c.Status(result.StatusCode)
	}

	if len(result.Body) > 0 {
		c.Writer.Write(result.Body)
	}
}
