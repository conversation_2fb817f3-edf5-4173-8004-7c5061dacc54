package auth

import (
	"context"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"strings"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"

	"github.com/golang-jwt/jwt/v5"
)

// JWTAuthenticator implements JWT-based authentication
type JWTAuthenticator struct {
	config     config.JWTConfig
	logger     *telemetry.Logger
	signingKey interface{}
	verifyKey  interface{}
	
	// Token blacklist (in production, use Redis or similar)
	blacklist map[string]time.Time
}

// JWTClaims represents JWT claims
type JWTClaims struct {
	UserID      string                 `json:"user_id"`
	Username    string                 `json:"username"`
	Email       string                 `json:"email,omitempty"`
	Roles       []string               `json:"roles,omitempty"`
	Permissions []string               `json:"permissions,omitempty"`
	Attributes  map[string]interface{} `json:"attributes,omitempty"`
	TokenType   string                 `json:"token_type,omitempty"`
	jwt.RegisteredClaims
}

// NewJWTAuthenticator creates a new JWT authenticator
func NewJWTAuthenticator(cfg config.JWTConfig, logger *telemetry.Logger) (*JWTAuthenticator, error) {
	auth := &JWTAuthenticator{
		config:    cfg,
		logger:    logger,
		blacklist: make(map[string]time.Time),
	}

	// Initialize signing and verification keys
	if err := auth.initializeKeys(); err != nil {
		return nil, fmt.Errorf("failed to initialize JWT keys: %w", err)
	}

	return auth, nil
}

// initializeKeys initializes signing and verification keys
func (j *JWTAuthenticator) initializeKeys() error {
	switch j.config.Algorithm {
	case "HS256", "HS384", "HS512":
		// HMAC algorithms use the same key for signing and verification
		j.signingKey = []byte(j.config.Secret)
		j.verifyKey = []byte(j.config.Secret)
		
	case "RS256", "RS384", "RS512":
		// RSA algorithms use different keys for signing and verification
		if j.config.PublicKey != "" {
			publicKey, err := j.parseRSAPublicKey(j.config.PublicKey)
			if err != nil {
				return fmt.Errorf("failed to parse RSA public key: %w", err)
			}
			j.verifyKey = publicKey
		}
		
		// For signing, we would need a private key (not implemented in this example)
		// j.signingKey = privateKey
		
	default:
		return fmt.Errorf("unsupported JWT algorithm: %s", j.config.Algorithm)
	}

	return nil
}

// parseRSAPublicKey parses RSA public key from PEM format
func (j *JWTAuthenticator) parseRSAPublicKey(keyData string) (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(keyData))
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %w", err)
	}

	rsaPub, ok := pub.(*rsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("not an RSA public key")
	}

	return rsaPub, nil
}

// Authenticate performs JWT authentication
func (j *JWTAuthenticator) Authenticate(ctx context.Context, authCtx *AuthContext) (*AuthResult, error) {
	// Extract token from Authorization header
	token := j.extractToken(authCtx.Headers)
	if token == "" {
		return &AuthResult{
			Authenticated: false,
			Error:         "no JWT token found",
		}, nil
	}

	// Validate token
	return j.ValidateToken(ctx, token)
}

// extractToken extracts JWT token from headers
func (j *JWTAuthenticator) extractToken(headers map[string]string) string {
	// Check Authorization header
	authHeader := headers["Authorization"]
	if authHeader == "" {
		authHeader = headers["authorization"]
	}

	if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
		return strings.TrimPrefix(authHeader, "Bearer ")
	}

	return ""
}

// ValidateToken validates a JWT token
func (j *JWTAuthenticator) ValidateToken(ctx context.Context, tokenString string) (*AuthResult, error) {
	// Check if token is blacklisted
	if j.isBlacklisted(tokenString) {
		return &AuthResult{
			Authenticated: false,
			Error:         "token is revoked",
		}, nil
	}

	// Parse and validate token
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Verify signing method
		if token.Method.Alg() != j.config.Algorithm {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.verifyKey, nil
	})

	if err != nil {
		j.logger.Warn("JWT validation failed", "error", err)
		return &AuthResult{
			Authenticated: false,
			Error:         "invalid token",
		}, nil
	}

	if !token.Valid {
		return &AuthResult{
			Authenticated: false,
			Error:         "token is not valid",
		}, nil
	}

	// Extract claims
	claims, ok := token.Claims.(*JWTClaims)
	if !ok {
		return &AuthResult{
			Authenticated: false,
			Error:         "invalid token claims",
		}, nil
	}

	// Check expiration
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
		return &AuthResult{
			Authenticated: false,
			Error:         "token expired",
		}, nil
	}

	// Build auth result
	result := &AuthResult{
		Authenticated: true,
		UserID:        claims.UserID,
		Username:      claims.Username,
		Roles:         claims.Roles,
		Permissions:   claims.Permissions,
		Attributes:    claims.Attributes,
		TokenType:     "jwt",
	}

	if claims.ExpiresAt != nil {
		result.ExpiresAt = &claims.ExpiresAt.Time
	}

	return result, nil
}

// GenerateToken generates a new JWT token
func (j *JWTAuthenticator) GenerateToken(userID, username string, roles, permissions []string, attributes map[string]interface{}) (string, error) {
	now := time.Now()
	expiresAt := now.Add(time.Duration(j.config.Expiration) * time.Second)

	claims := &JWTClaims{
		UserID:      userID,
		Username:    username,
		Roles:       roles,
		Permissions: permissions,
		Attributes:  attributes,
		TokenType:   "access",
		RegisteredClaims: jwt.RegisteredClaims{
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "api-gateway",
			Subject:   userID,
		},
	}

	// Create token
	token := jwt.NewWithClaims(jwt.GetSigningMethod(j.config.Algorithm), claims)

	// Sign token
	tokenString, err := token.SignedString(j.signingKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

// GenerateRefreshToken generates a refresh token
func (j *JWTAuthenticator) GenerateRefreshToken(userID string) (string, error) {
	now := time.Now()
	expiresAt := now.Add(24 * time.Hour * 7) // 7 days

	claims := &JWTClaims{
		UserID:    userID,
		TokenType: "refresh",
		RegisteredClaims: jwt.RegisteredClaims{
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "api-gateway",
			Subject:   userID,
		},
	}

	token := jwt.NewWithClaims(jwt.GetSigningMethod(j.config.Algorithm), claims)
	tokenString, err := token.SignedString(j.signingKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign refresh token: %w", err)
	}

	return tokenString, nil
}

// RefreshToken refreshes an access token using a refresh token
func (j *JWTAuthenticator) RefreshToken(ctx context.Context, refreshToken string) (*AuthResult, error) {
	// Validate refresh token
	token, err := jwt.ParseWithClaims(refreshToken, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if token.Method.Alg() != j.config.Algorithm {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.verifyKey, nil
	})

	if err != nil {
		return &AuthResult{
			Authenticated: false,
			Error:         "invalid refresh token",
		}, fmt.Errorf("refresh token validation failed: %w", err)
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok || claims.TokenType != "refresh" {
		return &AuthResult{
			Authenticated: false,
			Error:         "invalid refresh token type",
		}, fmt.Errorf("invalid refresh token claims")
	}

	// Check if refresh token is expired
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
		return &AuthResult{
			Authenticated: false,
			Error:         "refresh token expired",
		}, fmt.Errorf("refresh token expired")
	}

	// Generate new access token
	// In a real implementation, you would fetch user details from a user store
	newToken, err := j.GenerateToken(claims.UserID, claims.Username, nil, nil, nil)
	if err != nil {
		return &AuthResult{
			Authenticated: false,
			Error:         "failed to generate new token",
		}, fmt.Errorf("failed to generate new access token: %w", err)
	}

	return &AuthResult{
		Authenticated: true,
		UserID:        claims.UserID,
		Username:      claims.Username,
		TokenType:     "jwt",
		Attributes: map[string]interface{}{
			"access_token": newToken,
		},
	}, nil
}

// RevokeToken revokes a JWT token by adding it to blacklist
func (j *JWTAuthenticator) RevokeToken(ctx context.Context, tokenString string) error {
	// Parse token to get expiration
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		return j.verifyKey, nil
	})

	if err != nil {
		return fmt.Errorf("failed to parse token for revocation: %w", err)
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok {
		return fmt.Errorf("invalid token claims")
	}

	// Add to blacklist with expiration time
	var expiresAt time.Time
	if claims.ExpiresAt != nil {
		expiresAt = claims.ExpiresAt.Time
	} else {
		expiresAt = time.Now().Add(24 * time.Hour) // Default 24 hours
	}

	j.blacklist[tokenString] = expiresAt

	j.logger.Info("Token revoked", "user_id", claims.UserID)
	return nil
}

// isBlacklisted checks if a token is blacklisted
func (j *JWTAuthenticator) isBlacklisted(tokenString string) bool {
	expiresAt, exists := j.blacklist[tokenString]
	if !exists {
		return false
	}

	// Remove expired entries from blacklist
	if time.Now().After(expiresAt) {
		delete(j.blacklist, tokenString)
		return false
	}

	return true
}

// Name returns the authenticator name
func (j *JWTAuthenticator) Name() string {
	return "jwt"
}

// Priority returns the authenticator priority
func (j *JWTAuthenticator) Priority() int {
	return 100
}

// Enabled returns whether the authenticator is enabled
func (j *JWTAuthenticator) Enabled() bool {
	return j.config.Enabled
}

// Close closes the JWT authenticator
func (j *JWTAuthenticator) Close() error {
	// Clear blacklist
	j.blacklist = make(map[string]time.Time)
	return nil
}
