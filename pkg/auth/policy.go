package auth

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// AuthorizationContext contains context for authorization decisions
type AuthorizationContext struct {
	UserID      string                 `json:"user_id"`
	Username    string                 `json:"username"`
	Roles       []string               `json:"roles"`
	Permissions []string               `json:"permissions"`
	Attributes  map[string]interface{} `json:"attributes"`
	Resource    string                 `json:"resource"`
	Action      string                 `json:"action"`
	Context     *AuthContext           `json:"context"`
}

// AuthorizationDecision represents an authorization decision
type AuthorizationDecision struct {
	Allowed   bool                   `json:"allowed"`
	Reason    string                 `json:"reason"`
	Policy    string                 `json:"policy"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	ExpiresAt *time.Time             `json:"expires_at,omitempty"`
}

// PolicyEngine interface defines policy evaluation methods
type PolicyEngine interface {
	Evaluate(ctx context.Context, authzCtx *AuthorizationContext) (*AuthorizationDecision, error)
	LoadPolicies(policies []config.PolicyRule) error
	ReloadPolicies() error
	GetPolicies() []config.PolicyRule
	Close() error
}

// BuiltinPolicyEngine implements a simple built-in policy engine
type BuiltinPolicyEngine struct {
	config   config.PolicyConfig
	logger   *telemetry.Logger
	policies []config.PolicyRule
}

// NewBuiltinPolicyEngine creates a new built-in policy engine
func NewBuiltinPolicyEngine(cfg config.PolicyConfig, logger *telemetry.Logger) (*BuiltinPolicyEngine, error) {
	engine := &BuiltinPolicyEngine{
		config: cfg,
		logger: logger,
	}

	// Load initial policies
	if err := engine.LoadPolicies(cfg.Policies); err != nil {
		return nil, fmt.Errorf("failed to load policies: %w", err)
	}

	return engine, nil
}

// Evaluate evaluates authorization using built-in rules
func (b *BuiltinPolicyEngine) Evaluate(ctx context.Context, authzCtx *AuthorizationContext) (*AuthorizationDecision, error) {
	// Default deny
	decision := &AuthorizationDecision{
		Allowed: false,
		Reason:  "no matching policy found",
	}

	// Evaluate each policy in order
	for _, policy := range b.policies {
		if b.matchesPolicy(policy, authzCtx) {
			// Check role-based access
			if b.hasRequiredRole(policy.Roles, authzCtx.Roles) {
				// Check permission-based access
				if b.hasRequiredPermission(policy.Permissions, authzCtx.Permissions) {
					// Check additional conditions
					if b.evaluateConditions(policy.Conditions, authzCtx) {
						decision.Allowed = true
						decision.Reason = "policy matched and conditions satisfied"
						decision.Policy = policy.Name
						
						b.logger.Debug("Authorization granted",
							"policy", policy.Name,
							"user_id", authzCtx.UserID,
							"resource", authzCtx.Resource,
							"action", authzCtx.Action)
						
						return decision, nil
					} else {
						decision.Reason = "policy matched but conditions not satisfied"
						decision.Policy = policy.Name
					}
				} else {
					decision.Reason = "insufficient permissions"
					decision.Policy = policy.Name
				}
			} else {
				decision.Reason = "insufficient roles"
				decision.Policy = policy.Name
			}
		}
	}

	b.logger.Debug("Authorization denied",
		"user_id", authzCtx.UserID,
		"resource", authzCtx.Resource,
		"action", authzCtx.Action,
		"reason", decision.Reason)

	return decision, nil
}

// matchesPolicy checks if the request matches a policy
func (b *BuiltinPolicyEngine) matchesPolicy(policy config.PolicyRule, authzCtx *AuthorizationContext) bool {
	// Match path
	if !b.matchesPath(policy.Path, authzCtx.Resource) {
		return false
	}

	// Match method
	if !b.matchesMethod(policy.Method, authzCtx.Action) {
		return false
	}

	return true
}

// matchesPath checks if the resource path matches the policy path
func (b *BuiltinPolicyEngine) matchesPath(policyPath, resourcePath string) bool {
	// Exact match
	if policyPath == resourcePath {
		return true
	}

	// Wildcard match
	if strings.HasSuffix(policyPath, "*") {
		prefix := strings.TrimSuffix(policyPath, "*")
		return strings.HasPrefix(resourcePath, prefix)
	}

	// Regex match (if path starts with ~)
	if strings.HasPrefix(policyPath, "~") {
		pattern := strings.TrimPrefix(policyPath, "~")
		matched, err := regexp.MatchString(pattern, resourcePath)
		if err != nil {
			b.logger.Error("Invalid regex pattern in policy", "pattern", pattern, "error", err)
			return false
		}
		return matched
	}

	return false
}

// matchesMethod checks if the action method matches the policy method
func (b *BuiltinPolicyEngine) matchesMethod(policyMethod, actionMethod string) bool {
	// Wildcard match
	if policyMethod == "*" {
		return true
	}

	// Exact match (case insensitive)
	return strings.EqualFold(policyMethod, actionMethod)
}

// hasRequiredRole checks if the user has any of the required roles
func (b *BuiltinPolicyEngine) hasRequiredRole(requiredRoles, userRoles []string) bool {
	// If no roles required, allow
	if len(requiredRoles) == 0 {
		return true
	}

	// Check if user has any of the required roles
	for _, required := range requiredRoles {
		for _, userRole := range userRoles {
			if strings.EqualFold(required, userRole) {
				return true
			}
		}
	}

	return false
}

// hasRequiredPermission checks if the user has any of the required permissions
func (b *BuiltinPolicyEngine) hasRequiredPermission(requiredPermissions, userPermissions []string) bool {
	// If no permissions required, allow
	if len(requiredPermissions) == 0 {
		return true
	}

	// Check if user has any of the required permissions
	for _, required := range requiredPermissions {
		// Wildcard permission
		if required == "*" {
			return true
		}

		for _, userPerm := range userPermissions {
			// Exact match
			if strings.EqualFold(required, userPerm) {
				return true
			}

			// Wildcard user permission
			if userPerm == "*" {
				return true
			}

			// Prefix match for hierarchical permissions
			if strings.HasSuffix(userPerm, "*") {
				prefix := strings.TrimSuffix(userPerm, "*")
				if strings.HasPrefix(required, prefix) {
					return true
				}
			}
		}
	}

	return false
}

// evaluateConditions evaluates additional policy conditions
func (b *BuiltinPolicyEngine) evaluateConditions(conditions map[string]string, authzCtx *AuthorizationContext) bool {
	// If no conditions, allow
	if len(conditions) == 0 {
		return true
	}

	for key, value := range conditions {
		switch key {
		case "time_range":
			if !b.evaluateTimeRange(value) {
				return false
			}
		case "ip_range":
			if !b.evaluateIPRange(value, authzCtx.Context.ClientIP) {
				return false
			}
		case "user_attribute":
			if !b.evaluateUserAttribute(value, authzCtx.Attributes) {
				return false
			}
		default:
			b.logger.Warn("Unknown condition type", "condition", key)
			return false
		}
	}

	return true
}

// evaluateTimeRange evaluates time-based conditions
func (b *BuiltinPolicyEngine) evaluateTimeRange(timeRange string) bool {
	// Simple time range evaluation (e.g., "09:00-17:00")
	// This is a simplified implementation
	now := time.Now()
	currentTime := now.Format("15:04")

	parts := strings.Split(timeRange, "-")
	if len(parts) != 2 {
		return false
	}

	startTime := strings.TrimSpace(parts[0])
	endTime := strings.TrimSpace(parts[1])

	return currentTime >= startTime && currentTime <= endTime
}

// evaluateIPRange evaluates IP-based conditions
func (b *BuiltinPolicyEngine) evaluateIPRange(ipRange, clientIP string) bool {
	// Simple IP range evaluation
	// This is a simplified implementation - in production, use proper CIDR matching
	if ipRange == "*" {
		return true
	}

	// Exact IP match
	if ipRange == clientIP {
		return true
	}

	// Subnet match (simplified)
	if strings.HasSuffix(ipRange, "*") {
		prefix := strings.TrimSuffix(ipRange, "*")
		return strings.HasPrefix(clientIP, prefix)
	}

	return false
}

// evaluateUserAttribute evaluates user attribute conditions
func (b *BuiltinPolicyEngine) evaluateUserAttribute(condition string, attributes map[string]interface{}) bool {
	// Parse condition (e.g., "department=engineering")
	parts := strings.SplitN(condition, "=", 2)
	if len(parts) != 2 {
		return false
	}

	attrName := strings.TrimSpace(parts[0])
	expectedValue := strings.TrimSpace(parts[1])

	if attributes == nil {
		return false
	}

	actualValue, exists := attributes[attrName]
	if !exists {
		return false
	}

	// Convert to string for comparison
	actualStr := fmt.Sprintf("%v", actualValue)
	return strings.EqualFold(actualStr, expectedValue)
}

// LoadPolicies loads policies into the engine
func (b *BuiltinPolicyEngine) LoadPolicies(policies []config.PolicyRule) error {
	b.policies = make([]config.PolicyRule, len(policies))
	copy(b.policies, policies)

	b.logger.Info("Loaded policies", "count", len(policies))
	return nil
}

// ReloadPolicies reloads policies from configuration
func (b *BuiltinPolicyEngine) ReloadPolicies() error {
	// In a real implementation, this would reload from the configuration source
	b.logger.Info("Policy reload requested")
	return nil
}

// GetPolicies returns current policies
func (b *BuiltinPolicyEngine) GetPolicies() []config.PolicyRule {
	return b.policies
}

// Close closes the policy engine
func (b *BuiltinPolicyEngine) Close() error {
	b.policies = nil
	return nil
}
