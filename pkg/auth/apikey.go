package auth

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"
	"sync"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// APIKeyInfo represents API key information
type APIKeyInfo struct {
	ID          string                 `json:"id"`
	Key         string                 `json:"key"`
	HashedKey   string                 `json:"hashed_key"`
	Name        string                 `json:"name"`
	UserID      string                 `json:"user_id"`
	Roles       []string               `json:"roles"`
	Permissions []string               `json:"permissions"`
	Attributes  map[string]interface{} `json:"attributes"`
	Active      bool                   `json:"active"`
	CreatedAt   time.Time              `json:"created_at"`
	ExpiresAt   *time.Time             `json:"expires_at,omitempty"`
	LastUsedAt  *time.Time             `json:"last_used_at,omitempty"`
	RateLimit   *RateLimitInfo         `json:"rate_limit,omitempty"`
}

// RateLimitInfo represents rate limiting information for API keys
type RateLimitInfo struct {
	RequestsPerSecond int `json:"requests_per_second"`
	RequestsPerMinute int `json:"requests_per_minute"`
	RequestsPerHour   int `json:"requests_per_hour"`
	RequestsPerDay    int `json:"requests_per_day"`
}

// APIKeyAuthenticator implements API key-based authentication
type APIKeyAuthenticator struct {
	config config.APIKeyConfig
	logger *telemetry.Logger
	
	// In-memory store for API keys (in production, use database)
	apiKeys map[string]*APIKeyInfo
	mu      sync.RWMutex
}

// NewAPIKeyAuthenticator creates a new API key authenticator
func NewAPIKeyAuthenticator(cfg config.APIKeyConfig, logger *telemetry.Logger) (*APIKeyAuthenticator, error) {
	auth := &APIKeyAuthenticator{
		config:  cfg,
		logger:  logger,
		apiKeys: make(map[string]*APIKeyInfo),
	}

	// Initialize with some default API keys for testing
	if err := auth.initializeDefaultKeys(); err != nil {
		return nil, fmt.Errorf("failed to initialize default API keys: %w", err)
	}

	return auth, nil
}

// initializeDefaultKeys initializes some default API keys for testing
func (a *APIKeyAuthenticator) initializeDefaultKeys() error {
	// Create a default admin API key
	adminKey, err := a.CreateAPIKey("admin-key", "admin", []string{"admin"}, []string{"*"}, nil, nil)
	if err != nil {
		return fmt.Errorf("failed to create admin API key: %w", err)
	}

	a.logger.Info("Created default admin API key", "key_id", adminKey.ID)

	// Create a default user API key
	userKey, err := a.CreateAPIKey("user-key", "user", []string{"user"}, []string{"read"}, nil, nil)
	if err != nil {
		return fmt.Errorf("failed to create user API key: %w", err)
	}

	a.logger.Info("Created default user API key", "key_id", userKey.ID)

	return nil
}

// Authenticate performs API key authentication
func (a *APIKeyAuthenticator) Authenticate(ctx context.Context, authCtx *AuthContext) (*AuthResult, error) {
	// Extract API key from headers or query parameters
	apiKey := a.extractAPIKey(authCtx.Headers, authCtx.Path)
	if apiKey == "" {
		return &AuthResult{
			Authenticated: false,
			Error:         "no API key found",
		}, nil
	}

	// Validate API key
	return a.ValidateAPIKey(ctx, apiKey)
}

// extractAPIKey extracts API key from headers or query parameters
func (a *APIKeyAuthenticator) extractAPIKey(headers map[string]string, path string) string {
	// Check header
	if a.config.HeaderName != "" {
		if key := headers[a.config.HeaderName]; key != "" {
			return key
		}
		// Try lowercase
		if key := headers[strings.ToLower(a.config.HeaderName)]; key != "" {
			return key
		}
	}

	// Check query parameter (simplified extraction)
	if a.config.QueryParam != "" && strings.Contains(path, a.config.QueryParam+"=") {
		// This is a simplified implementation
		// In production, you would properly parse the query string
		parts := strings.Split(path, a.config.QueryParam+"=")
		if len(parts) > 1 {
			keyPart := strings.Split(parts[1], "&")[0]
			return keyPart
		}
	}

	return ""
}

// ValidateAPIKey validates an API key
func (a *APIKeyAuthenticator) ValidateAPIKey(ctx context.Context, apiKey string) (*AuthResult, error) {
	// Hash the provided key
	hashedKey := a.hashAPIKey(apiKey)

	a.mu.RLock()
	defer a.mu.RUnlock()

	// Find API key by hashed value
	var keyInfo *APIKeyInfo
	for _, info := range a.apiKeys {
		if info.HashedKey == hashedKey {
			keyInfo = info
			break
		}
	}

	if keyInfo == nil {
		return &AuthResult{
			Authenticated: false,
			Error:         "invalid API key",
		}, nil
	}

	// Check if key is active
	if !keyInfo.Active {
		return &AuthResult{
			Authenticated: false,
			Error:         "API key is disabled",
		}, nil
	}

	// Check expiration
	if keyInfo.ExpiresAt != nil && keyInfo.ExpiresAt.Before(time.Now()) {
		return &AuthResult{
			Authenticated: false,
			Error:         "API key expired",
		}, nil
	}

	// Update last used time
	now := time.Now()
	keyInfo.LastUsedAt = &now

	// Build auth result
	result := &AuthResult{
		Authenticated: true,
		UserID:        keyInfo.UserID,
		Username:      keyInfo.Name,
		Roles:         keyInfo.Roles,
		Permissions:   keyInfo.Permissions,
		Attributes:    keyInfo.Attributes,
		TokenType:     "apikey",
	}

	if keyInfo.ExpiresAt != nil {
		result.ExpiresAt = keyInfo.ExpiresAt
	}

	return result, nil
}

// CreateAPIKey creates a new API key
func (a *APIKeyAuthenticator) CreateAPIKey(name, userID string, roles, permissions []string, attributes map[string]interface{}, expiresAt *time.Time) (*APIKeyInfo, error) {
	// Generate random API key
	key, err := a.generateAPIKey()
	if err != nil {
		return nil, fmt.Errorf("failed to generate API key: %w", err)
	}

	// Hash the key
	hashedKey := a.hashAPIKey(key)

	// Create API key info
	keyInfo := &APIKeyInfo{
		ID:          a.generateKeyID(),
		Key:         key,
		HashedKey:   hashedKey,
		Name:        name,
		UserID:      userID,
		Roles:       roles,
		Permissions: permissions,
		Attributes:  attributes,
		Active:      true,
		CreatedAt:   time.Now(),
		ExpiresAt:   expiresAt,
	}

	a.mu.Lock()
	a.apiKeys[keyInfo.ID] = keyInfo
	a.mu.Unlock()

	a.logger.Info("Created API key",
		"key_id", keyInfo.ID,
		"name", name,
		"user_id", userID)

	// Return copy without the actual key for security
	result := *keyInfo
	result.Key = "" // Don't return the actual key
	return &result, nil
}

// RevokeAPIKey revokes an API key
func (a *APIKeyAuthenticator) RevokeAPIKey(ctx context.Context, keyID string) error {
	a.mu.Lock()
	defer a.mu.Unlock()

	keyInfo, exists := a.apiKeys[keyID]
	if !exists {
		return fmt.Errorf("API key not found: %s", keyID)
	}

	keyInfo.Active = false

	a.logger.Info("Revoked API key",
		"key_id", keyID,
		"name", keyInfo.Name,
		"user_id", keyInfo.UserID)

	return nil
}

// DeleteAPIKey deletes an API key
func (a *APIKeyAuthenticator) DeleteAPIKey(keyID string) error {
	a.mu.Lock()
	defer a.mu.Unlock()

	keyInfo, exists := a.apiKeys[keyID]
	if !exists {
		return fmt.Errorf("API key not found: %s", keyID)
	}

	delete(a.apiKeys, keyID)

	a.logger.Info("Deleted API key",
		"key_id", keyID,
		"name", keyInfo.Name,
		"user_id", keyInfo.UserID)

	return nil
}

// ListAPIKeys lists all API keys for a user
func (a *APIKeyAuthenticator) ListAPIKeys(userID string) ([]*APIKeyInfo, error) {
	a.mu.RLock()
	defer a.mu.RUnlock()

	var keys []*APIKeyInfo
	for _, keyInfo := range a.apiKeys {
		if keyInfo.UserID == userID {
			// Return copy without the actual key
			result := *keyInfo
			result.Key = ""
			keys = append(keys, &result)
		}
	}

	return keys, nil
}

// GetAPIKey gets API key information by ID
func (a *APIKeyAuthenticator) GetAPIKey(keyID string) (*APIKeyInfo, error) {
	a.mu.RLock()
	defer a.mu.RUnlock()

	keyInfo, exists := a.apiKeys[keyID]
	if !exists {
		return nil, fmt.Errorf("API key not found: %s", keyID)
	}

	// Return copy without the actual key
	result := *keyInfo
	result.Key = ""
	return &result, nil
}

// UpdateAPIKey updates API key information
func (a *APIKeyAuthenticator) UpdateAPIKey(keyID string, name string, roles, permissions []string, attributes map[string]interface{}) error {
	a.mu.Lock()
	defer a.mu.Unlock()

	keyInfo, exists := a.apiKeys[keyID]
	if !exists {
		return fmt.Errorf("API key not found: %s", keyID)
	}

	if name != "" {
		keyInfo.Name = name
	}
	if roles != nil {
		keyInfo.Roles = roles
	}
	if permissions != nil {
		keyInfo.Permissions = permissions
	}
	if attributes != nil {
		keyInfo.Attributes = attributes
	}

	a.logger.Info("Updated API key",
		"key_id", keyID,
		"name", keyInfo.Name,
		"user_id", keyInfo.UserID)

	return nil
}

// generateAPIKey generates a random API key
func (a *APIKeyAuthenticator) generateAPIKey() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// generateKeyID generates a unique key ID
func (a *APIKeyAuthenticator) generateKeyID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// hashAPIKey hashes an API key for secure storage
func (a *APIKeyAuthenticator) hashAPIKey(key string) string {
	hash := sha256.Sum256([]byte(key))
	return hex.EncodeToString(hash[:])
}

// Name returns the authenticator name
func (a *APIKeyAuthenticator) Name() string {
	return "apikey"
}

// Priority returns the authenticator priority
func (a *APIKeyAuthenticator) Priority() int {
	return 200
}

// Enabled returns whether the authenticator is enabled
func (a *APIKeyAuthenticator) Enabled() bool {
	return a.config.Enabled
}

// Close closes the API key authenticator
func (a *APIKeyAuthenticator) Close() error {
	a.mu.Lock()
	defer a.mu.Unlock()
	
	// Clear API keys
	a.apiKeys = make(map[string]*APIKeyInfo)
	return nil
}
