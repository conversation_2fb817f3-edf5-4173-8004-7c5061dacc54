package proxy

import (
	"fmt"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
	"sync"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/discovery"
	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
)

// Manager manages proxy operations and load balancing
type Manager struct {
	config           *config.Config
	logger           *telemetry.Logger
	metrics          *telemetry.Metrics
	discoveryManager *discovery.Manager
	
	// Route handlers cache
	routeHandlers map[string]gin.HandlerFunc
	mu            sync.RWMutex
	
	// Load balancers for each upstream
	loadBalancers map[string]LoadBalancer
	lbMu          sync.RWMutex
}

// NewManager creates a new proxy manager
func NewManager(cfg *config.Config, logger *telemetry.Logger, metrics *telemetry.Metrics, discoveryManager *discovery.Manager) (*Manager, error) {
	manager := &Manager{
		config:           cfg,
		logger:           logger,
		metrics:          metrics,
		discoveryManager: discoveryManager,
		routeHandlers:    make(map[string]gin.HandlerFunc),
		loadBalancers:    make(map[string]LoadBalancer),
	}

	// Initialize load balancers for static routes
	for _, route := range cfg.Routes {
		if route.Upstream.Type == "static" {
			lb, err := NewLoadBalancer(route.Upstream.LoadBalancer, route.Upstream.Servers)
			if err != nil {
				return nil, fmt.Errorf("failed to create load balancer for route %s: %w", route.Name, err)
			}
			manager.loadBalancers[route.Name] = lb
		}
	}

	return manager, nil
}

// CreateHandler creates a proxy handler for a specific route
func (m *Manager) CreateHandler(route config.RouteConfig) gin.HandlerFunc {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Check if handler already exists
	if handler, exists := m.routeHandlers[route.Name]; exists {
		return handler
	}

	// Create new handler
	handler := m.createProxyHandler(route)
	m.routeHandlers[route.Name] = handler

	return handler
}

// CreateDynamicHandler creates a dynamic proxy handler for unmatched routes
func (m *Manager) CreateDynamicHandler(path, method string) gin.HandlerFunc {
	// Try to find service through discovery
	if m.discoveryManager != nil {
		serviceName := m.extractServiceName(path)
		if serviceName != "" {
			instances, err := m.discoveryManager.GetServiceInstances(serviceName)
			if err == nil && len(instances) > 0 {
				// Create temporary route config
				route := config.RouteConfig{
					Name:   "dynamic-" + serviceName,
					Path:   path,
					Method: method,
					Upstream: config.UpstreamConfig{
						Type:         "discovery",
						LoadBalancer: "round_robin",
						ServiceName:  serviceName,
					},
					Timeout: 30 * time.Second,
					Retries: 3,
				}
				return m.createProxyHandler(route)
			}
		}
	}

	return nil
}

// createProxyHandler creates the actual proxy handler
func (m *Manager) createProxyHandler(route config.RouteConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// Get target server
		target, err := m.getTarget(route)
		if err != nil {
			m.logger.Error("Failed to get target server", "route", route.Name, "error", err)
			m.metrics.RecordUpstreamError(route.Name, "target_selection")
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error":   "service unavailable",
				"message": "Unable to find healthy upstream server",
			})
			return
		}

		// Apply path rewrite if configured
		originalPath := c.Request.URL.Path
		if route.Rewrite.Enabled {
			newPath := m.applyPathRewrite(originalPath, route.Rewrite)
			c.Request.URL.Path = newPath
		}

		// Create reverse proxy
		proxy := m.createReverseProxy(target, route)

		// Set additional headers
		for key, value := range route.Headers {
			c.Header(key, value)
		}

		// Execute proxy request
		proxy.ServeHTTP(c.Writer, c.Request)

		// Restore original path
		c.Request.URL.Path = originalPath

		// Record metrics
		duration := time.Since(startTime)
		statusCode := fmt.Sprintf("%d", c.Writer.Status())
		m.metrics.RecordRequest(
			c.Request.Method,
			route.Path,
			statusCode,
			route.Name,
			duration,
			c.Request.ContentLength,
			int64(c.Writer.Size()),
		)
	}
}

// getTarget selects a target server for the request
func (m *Manager) getTarget(route config.RouteConfig) (*url.URL, error) {
	switch route.Upstream.Type {
	case "static":
		return m.getStaticTarget(route)
	case "discovery":
		return m.getDiscoveryTarget(route)
	default:
		return nil, fmt.Errorf("unsupported upstream type: %s", route.Upstream.Type)
	}
}

// getStaticTarget gets target from static configuration
func (m *Manager) getStaticTarget(route config.RouteConfig) (*url.URL, error) {
	m.lbMu.RLock()
	lb, exists := m.loadBalancers[route.Name]
	m.lbMu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("load balancer not found for route: %s", route.Name)
	}

	server := lb.NextServer()
	if server == nil {
		return nil, fmt.Errorf("no healthy servers available for route: %s", route.Name)
	}

	target := &url.URL{
		Scheme: "http", // TODO: support HTTPS
		Host:   fmt.Sprintf("%s:%d", server.Host, server.Port),
	}

	return target, nil
}

// getDiscoveryTarget gets target from service discovery
func (m *Manager) getDiscoveryTarget(route config.RouteConfig) (*url.URL, error) {
	if m.discoveryManager == nil {
		return nil, fmt.Errorf("service discovery not configured")
	}

	instances, err := m.discoveryManager.GetServiceInstances(route.Upstream.ServiceName)
	if err != nil {
		return nil, fmt.Errorf("failed to get service instances: %w", err)
	}

	if len(instances) == 0 {
		return nil, fmt.Errorf("no instances found for service: %s", route.Upstream.ServiceName)
	}

	// Simple round-robin for now
	// TODO: implement proper load balancing for discovery-based routing
	instance := instances[0]

	target := &url.URL{
		Scheme: instance.Scheme,
		Host:   fmt.Sprintf("%s:%d", instance.Host, instance.Port),
	}

	return target, nil
}

// createReverseProxy creates a reverse proxy for the target
func (m *Manager) createReverseProxy(target *url.URL, route config.RouteConfig) *httputil.ReverseProxy {
	proxy := httputil.NewSingleHostReverseProxy(target)

	// Customize director
	originalDirector := proxy.Director
	proxy.Director = func(req *http.Request) {
		originalDirector(req)
		req.Host = target.Host
		req.URL.Host = target.Host
		req.URL.Scheme = target.Scheme

		// Add forwarded headers
		req.Header.Set("X-Forwarded-Host", req.Header.Get("Host"))
		req.Header.Set("X-Forwarded-Proto", req.URL.Scheme)
		req.Header.Set("X-Real-IP", req.RemoteAddr)
	}

	// Customize error handler
	proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
		m.logger.Error("Proxy error", "target", target.String(), "error", err)
		m.metrics.RecordUpstreamError(route.Name, "proxy_error")

		w.WriteHeader(http.StatusBadGateway)
		w.Write([]byte(`{"error": "bad gateway", "message": "Upstream server error"}`))
	}

	// Set timeout
	proxy.Transport = &http.Transport{
		ResponseHeaderTimeout: route.Timeout,
	}

	return proxy
}

// applyPathRewrite applies path rewriting rules
func (m *Manager) applyPathRewrite(originalPath string, rewrite config.RewriteConfig) string {
	if !rewrite.Enabled {
		return originalPath
	}

	// Simple string replacement for now
	// TODO: implement regex-based rewriting
	return strings.Replace(originalPath, rewrite.From, rewrite.To, 1)
}

// extractServiceName extracts service name from path
func (m *Manager) extractServiceName(path string) string {
	// Simple extraction: /api/v1/users -> users
	// TODO: implement more sophisticated service name extraction
	parts := strings.Split(strings.Trim(path, "/"), "/")
	if len(parts) >= 3 && parts[0] == "api" {
		return parts[2]
	}
	return ""
}
