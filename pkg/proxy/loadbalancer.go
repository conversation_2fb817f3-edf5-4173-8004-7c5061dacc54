package proxy

import (
	"fmt"
	"hash/fnv"
	"math/rand"
	"sync"
	"sync/atomic"
	"time"

	"api-gateway/pkg/config"
)

// LoadBalancer interface defines load balancing methods
type LoadBalancer interface {
	NextServer() *config.ServerTarget
	AddServer(server config.ServerTarget)
	RemoveServer(host string, port int)
	GetServers() []config.ServerTarget
	UpdateServerHealth(host string, port int, healthy bool)
}

// NewLoadBalancer creates a new load balancer based on the algorithm
func NewLoadBalancer(algorithm string, servers []config.ServerTarget) (LoadBalancer, error) {
	switch algorithm {
	case "round_robin":
		return NewRoundRobinLB(servers), nil
	case "weighted_round_robin":
		return NewWeightedRoundRobinLB(servers), nil
	case "random":
		return NewRandomLB(servers), nil
	case "ip_hash":
		return NewIPHashLB(servers), nil
	case "least_connections":
		return NewLeastConnectionsLB(servers), nil
	default:
		return nil, fmt.Errorf("unsupported load balancing algorithm: %s", algorithm)
	}
}

// ServerState represents the state of a server
type ServerState struct {
	config.ServerTarget
	Healthy     bool
	Connections int64
	LastCheck   time.Time
}

// RoundRobinLB implements round-robin load balancing
type RoundRobinLB struct {
	servers []ServerState
	current uint64
	mu      sync.RWMutex
}

// NewRoundRobinLB creates a new round-robin load balancer
func NewRoundRobinLB(servers []config.ServerTarget) *RoundRobinLB {
	states := make([]ServerState, len(servers))
	for i, server := range servers {
		states[i] = ServerState{
			ServerTarget: server,
			Healthy:      true,
			LastCheck:    time.Now(),
		}
	}

	return &RoundRobinLB{
		servers: states,
		current: 0,
	}
}

// NextServer returns the next server in round-robin order
func (lb *RoundRobinLB) NextServer() *config.ServerTarget {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	if len(lb.servers) == 0 {
		return nil
	}

	// Find healthy servers
	healthyServers := make([]int, 0, len(lb.servers))
	for i, server := range lb.servers {
		if server.Healthy && !server.Backup {
			healthyServers = append(healthyServers, i)
		}
	}

	// If no healthy primary servers, try backup servers
	if len(healthyServers) == 0 {
		for i, server := range lb.servers {
			if server.Healthy && server.Backup {
				healthyServers = append(healthyServers, i)
			}
		}
	}

	if len(healthyServers) == 0 {
		return nil
	}

	// Get next server
	next := atomic.AddUint64(&lb.current, 1)
	index := healthyServers[int(next-1)%len(healthyServers)]
	return &lb.servers[index].ServerTarget
}

// AddServer adds a new server to the load balancer
func (lb *RoundRobinLB) AddServer(server config.ServerTarget) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	lb.servers = append(lb.servers, ServerState{
		ServerTarget: server,
		Healthy:      true,
		LastCheck:    time.Now(),
	})
}

// RemoveServer removes a server from the load balancer
func (lb *RoundRobinLB) RemoveServer(host string, port int) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	for i, server := range lb.servers {
		if server.Host == host && server.Port == port {
			lb.servers = append(lb.servers[:i], lb.servers[i+1:]...)
			break
		}
	}
}

// GetServers returns all servers
func (lb *RoundRobinLB) GetServers() []config.ServerTarget {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	servers := make([]config.ServerTarget, len(lb.servers))
	for i, state := range lb.servers {
		servers[i] = state.ServerTarget
	}
	return servers
}

// UpdateServerHealth updates the health status of a server
func (lb *RoundRobinLB) UpdateServerHealth(host string, port int, healthy bool) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	for i := range lb.servers {
		if lb.servers[i].Host == host && lb.servers[i].Port == port {
			lb.servers[i].Healthy = healthy
			lb.servers[i].LastCheck = time.Now()
			break
		}
	}
}

// WeightedRoundRobinLB implements weighted round-robin load balancing
type WeightedRoundRobinLB struct {
	servers       []ServerState
	weights       []int
	currentWeight []int
	totalWeight   int
	mu            sync.RWMutex
}

// NewWeightedRoundRobinLB creates a new weighted round-robin load balancer
func NewWeightedRoundRobinLB(servers []config.ServerTarget) *WeightedRoundRobinLB {
	states := make([]ServerState, len(servers))
	weights := make([]int, len(servers))
	currentWeight := make([]int, len(servers))
	totalWeight := 0

	for i, server := range servers {
		states[i] = ServerState{
			ServerTarget: server,
			Healthy:      true,
			LastCheck:    time.Now(),
		}
		weight := server.Weight
		if weight <= 0 {
			weight = 1
		}
		weights[i] = weight
		currentWeight[i] = 0
		totalWeight += weight
	}

	return &WeightedRoundRobinLB{
		servers:       states,
		weights:       weights,
		currentWeight: currentWeight,
		totalWeight:   totalWeight,
	}
}

// NextServer returns the next server based on weighted round-robin
func (lb *WeightedRoundRobinLB) NextServer() *config.ServerTarget {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	if len(lb.servers) == 0 {
		return nil
	}

	// Find the server with the highest current weight
	best := -1
	for i, server := range lb.servers {
		if !server.Healthy || server.Backup {
			continue
		}

		lb.currentWeight[i] += lb.weights[i]
		if best == -1 || lb.currentWeight[i] > lb.currentWeight[best] {
			best = i
		}
	}

	// If no healthy primary servers, try backup servers
	if best == -1 {
		for i, server := range lb.servers {
			if !server.Healthy || !server.Backup {
				continue
			}

			lb.currentWeight[i] += lb.weights[i]
			if best == -1 || lb.currentWeight[i] > lb.currentWeight[best] {
				best = i
			}
		}
	}

	if best == -1 {
		return nil
	}

	// Decrease the current weight
	lb.currentWeight[best] -= lb.totalWeight

	return &lb.servers[best].ServerTarget
}

// AddServer adds a new server to the weighted round-robin load balancer
func (lb *WeightedRoundRobinLB) AddServer(server config.ServerTarget) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	weight := server.Weight
	if weight <= 0 {
		weight = 1
	}

	lb.servers = append(lb.servers, ServerState{
		ServerTarget: server,
		Healthy:      true,
		LastCheck:    time.Now(),
	})
	lb.weights = append(lb.weights, weight)
	lb.currentWeight = append(lb.currentWeight, 0)
	lb.totalWeight += weight
}

// RemoveServer removes a server from the weighted round-robin load balancer
func (lb *WeightedRoundRobinLB) RemoveServer(host string, port int) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	for i, server := range lb.servers {
		if server.Host == host && server.Port == port {
			lb.totalWeight -= lb.weights[i]
			lb.servers = append(lb.servers[:i], lb.servers[i+1:]...)
			lb.weights = append(lb.weights[:i], lb.weights[i+1:]...)
			lb.currentWeight = append(lb.currentWeight[:i], lb.currentWeight[i+1:]...)
			break
		}
	}
}

// GetServers returns all servers
func (lb *WeightedRoundRobinLB) GetServers() []config.ServerTarget {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	servers := make([]config.ServerTarget, len(lb.servers))
	for i, state := range lb.servers {
		servers[i] = state.ServerTarget
	}
	return servers
}

// UpdateServerHealth updates the health status of a server
func (lb *WeightedRoundRobinLB) UpdateServerHealth(host string, port int, healthy bool) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	for i := range lb.servers {
		if lb.servers[i].Host == host && lb.servers[i].Port == port {
			lb.servers[i].Healthy = healthy
			lb.servers[i].LastCheck = time.Now()
			break
		}
	}
}

// RandomLB implements random load balancing
type RandomLB struct {
	servers []ServerState
	mu      sync.RWMutex
	rand    *rand.Rand
}

// NewRandomLB creates a new random load balancer
func NewRandomLB(servers []config.ServerTarget) *RandomLB {
	states := make([]ServerState, len(servers))
	for i, server := range servers {
		states[i] = ServerState{
			ServerTarget: server,
			Healthy:      true,
			LastCheck:    time.Now(),
		}
	}

	return &RandomLB{
		servers: states,
		rand:    rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// NextServer returns a random healthy server
func (lb *RandomLB) NextServer() *config.ServerTarget {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	if len(lb.servers) == 0 {
		return nil
	}

	// Find healthy servers
	healthyServers := make([]int, 0, len(lb.servers))
	for i, server := range lb.servers {
		if server.Healthy && !server.Backup {
			healthyServers = append(healthyServers, i)
		}
	}

	// If no healthy primary servers, try backup servers
	if len(healthyServers) == 0 {
		for i, server := range lb.servers {
			if server.Healthy && server.Backup {
				healthyServers = append(healthyServers, i)
			}
		}
	}

	if len(healthyServers) == 0 {
		return nil
	}

	// Select random server
	index := healthyServers[lb.rand.Intn(len(healthyServers))]
	return &lb.servers[index].ServerTarget
}

// AddServer adds a new server to the random load balancer
func (lb *RandomLB) AddServer(server config.ServerTarget) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	lb.servers = append(lb.servers, ServerState{
		ServerTarget: server,
		Healthy:      true,
		LastCheck:    time.Now(),
	})
}

// RemoveServer removes a server from the random load balancer
func (lb *RandomLB) RemoveServer(host string, port int) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	for i, server := range lb.servers {
		if server.Host == host && server.Port == port {
			lb.servers = append(lb.servers[:i], lb.servers[i+1:]...)
			break
		}
	}
}

// GetServers returns all servers
func (lb *RandomLB) GetServers() []config.ServerTarget {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	servers := make([]config.ServerTarget, len(lb.servers))
	for i, state := range lb.servers {
		servers[i] = state.ServerTarget
	}
	return servers
}

// UpdateServerHealth updates the health status of a server
func (lb *RandomLB) UpdateServerHealth(host string, port int, healthy bool) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	for i := range lb.servers {
		if lb.servers[i].Host == host && lb.servers[i].Port == port {
			lb.servers[i].Healthy = healthy
			lb.servers[i].LastCheck = time.Now()
			break
		}
	}
}

// IPHashLB implements IP hash load balancing
type IPHashLB struct {
	servers []ServerState
	mu      sync.RWMutex
}

// NewIPHashLB creates a new IP hash load balancer
func NewIPHashLB(servers []config.ServerTarget) *IPHashLB {
	states := make([]ServerState, len(servers))
	for i, server := range servers {
		states[i] = ServerState{
			ServerTarget: server,
			Healthy:      true,
			LastCheck:    time.Now(),
		}
	}

	return &IPHashLB{
		servers: states,
	}
}

// NextServer returns a server based on IP hash (requires client IP)
func (lb *IPHashLB) NextServer() *config.ServerTarget {
	// This method should be called with client IP
	// For now, return the first healthy server
	return lb.NextServerByIP("127.0.0.1")
}

// NextServerByIP returns a server based on client IP hash
func (lb *IPHashLB) NextServerByIP(clientIP string) *config.ServerTarget {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	if len(lb.servers) == 0 {
		return nil
	}

	// Find healthy servers
	healthyServers := make([]int, 0, len(lb.servers))
	for i, server := range lb.servers {
		if server.Healthy && !server.Backup {
			healthyServers = append(healthyServers, i)
		}
	}

	// If no healthy primary servers, try backup servers
	if len(healthyServers) == 0 {
		for i, server := range lb.servers {
			if server.Healthy && server.Backup {
				healthyServers = append(healthyServers, i)
			}
		}
	}

	if len(healthyServers) == 0 {
		return nil
	}

	// Hash client IP
	hash := fnv.New32a()
	hash.Write([]byte(clientIP))
	index := healthyServers[int(hash.Sum32())%len(healthyServers)]

	return &lb.servers[index].ServerTarget
}

// AddServer adds a new server to the IP hash load balancer
func (lb *IPHashLB) AddServer(server config.ServerTarget) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	lb.servers = append(lb.servers, ServerState{
		ServerTarget: server,
		Healthy:      true,
		LastCheck:    time.Now(),
	})
}

// RemoveServer removes a server from the IP hash load balancer
func (lb *IPHashLB) RemoveServer(host string, port int) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	for i, server := range lb.servers {
		if server.Host == host && server.Port == port {
			lb.servers = append(lb.servers[:i], lb.servers[i+1:]...)
			break
		}
	}
}

// GetServers returns all servers
func (lb *IPHashLB) GetServers() []config.ServerTarget {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	servers := make([]config.ServerTarget, len(lb.servers))
	for i, state := range lb.servers {
		servers[i] = state.ServerTarget
	}
	return servers
}

// UpdateServerHealth updates the health status of a server
func (lb *IPHashLB) UpdateServerHealth(host string, port int, healthy bool) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	for i := range lb.servers {
		if lb.servers[i].Host == host && lb.servers[i].Port == port {
			lb.servers[i].Healthy = healthy
			lb.servers[i].LastCheck = time.Now()
			break
		}
	}
}

// LeastConnectionsLB implements least connections load balancing
type LeastConnectionsLB struct {
	servers []ServerState
	mu      sync.RWMutex
}

// NewLeastConnectionsLB creates a new least connections load balancer
func NewLeastConnectionsLB(servers []config.ServerTarget) *LeastConnectionsLB {
	states := make([]ServerState, len(servers))
	for i, server := range servers {
		states[i] = ServerState{
			ServerTarget: server,
			Healthy:      true,
			LastCheck:    time.Now(),
		}
	}

	return &LeastConnectionsLB{
		servers: states,
	}
}

// NextServer returns the server with the least connections
func (lb *LeastConnectionsLB) NextServer() *config.ServerTarget {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	if len(lb.servers) == 0 {
		return nil
	}

	// Find healthy server with least connections
	best := -1
	minConnections := int64(-1)

	for i, server := range lb.servers {
		if !server.Healthy || server.Backup {
			continue
		}

		if best == -1 || server.Connections < minConnections {
			best = i
			minConnections = server.Connections
		}
	}

	// If no healthy primary servers, try backup servers
	if best == -1 {
		for i, server := range lb.servers {
			if !server.Healthy || !server.Backup {
				continue
			}

			if best == -1 || server.Connections < minConnections {
				best = i
				minConnections = server.Connections
			}
		}
	}

	if best == -1 {
		return nil
	}

	// Increment connection count
	atomic.AddInt64(&lb.servers[best].Connections, 1)

	return &lb.servers[best].ServerTarget
}

// AddServer adds a new server to the least connections load balancer
func (lb *LeastConnectionsLB) AddServer(server config.ServerTarget) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	lb.servers = append(lb.servers, ServerState{
		ServerTarget: server,
		Healthy:      true,
		LastCheck:    time.Now(),
	})
}

// RemoveServer removes a server from the least connections load balancer
func (lb *LeastConnectionsLB) RemoveServer(host string, port int) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	for i, server := range lb.servers {
		if server.Host == host && server.Port == port {
			lb.servers = append(lb.servers[:i], lb.servers[i+1:]...)
			break
		}
	}
}

// GetServers returns all servers
func (lb *LeastConnectionsLB) GetServers() []config.ServerTarget {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	servers := make([]config.ServerTarget, len(lb.servers))
	for i, state := range lb.servers {
		servers[i] = state.ServerTarget
	}
	return servers
}

// UpdateServerHealth updates the health status of a server
func (lb *LeastConnectionsLB) UpdateServerHealth(host string, port int, healthy bool) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	for i := range lb.servers {
		if lb.servers[i].Host == host && lb.servers[i].Port == port {
			lb.servers[i].Healthy = healthy
			lb.servers[i].LastCheck = time.Now()
			break
		}
	}
}

// DecrementConnections decrements the connection count for a server
func (lb *LeastConnectionsLB) DecrementConnections(host string, port int) {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	for i := range lb.servers {
		if lb.servers[i].Host == host && lb.servers[i].Port == port {
			if lb.servers[i].Connections > 0 {
				atomic.AddInt64(&lb.servers[i].Connections, -1)
			}
			break
		}
	}
}
