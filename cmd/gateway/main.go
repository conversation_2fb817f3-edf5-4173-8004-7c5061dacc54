package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"api-gateway/internal/core"
	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"

	"github.com/spf13/cobra"
)

var (
	configFile string
	version    = "1.0.0"
	buildTime  = "unknown"
	gitCommit  = "unknown"
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "gateway",
		Short: "High-performance API Gateway with Zero Trust Security",
		Long: `A high-performance, highly available API Gateway that implements 
Zero Trust security principles with comprehensive traffic control, 
monitoring, and plugin architecture.`,
		Version: fmt.Sprintf("%s (built: %s, commit: %s)", version, buildTime, gitCommit),
		RunE:    runGateway,
	}

	rootCmd.Flags().StringVarP(&configFile, "config", "c", "configs/gateway.yaml", "Configuration file path")

	if err := rootCmd.Execute(); err != nil {
		log.Fatal(err)
	}
}

func runGateway(cmd *cobra.Command, args []string) error {
	// Load configuration
	cfg, err := config.LoadConfig(configFile)
	if err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	// Initialize telemetry
	logger, err := telemetry.NewLogger(cfg.Logging)
	if err != nil {
		return fmt.Errorf("failed to initialize logger: %w", err)
	}
	defer logger.Sync()

	// Initialize metrics
	metrics, err := telemetry.NewMetrics(cfg.Metrics)
	if err != nil {
		return fmt.Errorf("failed to initialize metrics: %w", err)
	}

	// Initialize tracing
	tracer, closer, err := telemetry.NewTracer(cfg.Tracing)
	if err != nil {
		return fmt.Errorf("failed to initialize tracer: %w", err)
	}
	defer closer.Close()

	// Create gateway instance
	gateway, err := core.NewGateway(cfg, logger, metrics, tracer)
	if err != nil {
		return fmt.Errorf("failed to create gateway: %w", err)
	}

	// Start gateway
	server := &http.Server{
		Addr:         cfg.Server.Address,
		Handler:      gateway.Handler(),
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}

	// Start server in a goroutine
	go func() {
		logger.Info("Starting API Gateway", 
			"address", cfg.Server.Address,
			"version", version)
		
		if cfg.Server.TLS.Enabled {
			if err := server.ListenAndServeTLS(cfg.Server.TLS.CertFile, cfg.Server.TLS.KeyFile); err != nil && err != http.ErrServerClosed {
				logger.Error("Failed to start HTTPS server", "error", err)
			}
		} else {
			if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				logger.Error("Failed to start HTTP server", "error", err)
			}
		}
	}()

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down API Gateway...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Error("Gateway forced to shutdown", "error", err)
		return err
	}

	logger.Info("Gateway shutdown complete")
	return nil
}
