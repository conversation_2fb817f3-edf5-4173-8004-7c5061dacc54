version: '3.8'

services:
  # API Gateway
  gateway:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"  # Gateway HTTP port
      - "9090:9090"  # Metrics port
    volumes:
      - ./configs:/app/configs
      - ./logs:/app/logs
    environment:
      - GATEWAY_LOG_LEVEL=debug
      - CONSUL_ADDRESS=consul:8500
      - REDIS_URL=redis://redis:6379
    depends_on:
      - consul
      - redis
      - jaeger
    networks:
      - gateway-network
    restart: unless-stopped

  # Consul for service discovery
  consul:
    image: consul:1.16
    ports:
      - "8500:8500"  # Consul UI
      - "8600:8600/udp"  # Consul DNS
    environment:
      - CONSUL_BIND_INTERFACE=eth0
    command: >
      consul agent -dev -ui -client=0.0.0.0
      -datacenter=dc1
      -log-level=INFO
    volumes:
      - consul-data:/consul/data
    networks:
      - gateway-network
    restart: unless-stopped

  # Redis for caching and rate limiting
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - gateway-network
    restart: unless-stopped

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:1.49
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - gateway-network
    restart: unless-stopped

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:v2.47.0
    ports:
      - "9091:9090"  # Prometheus UI (avoiding conflict with gateway metrics)
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - gateway-network
    restart: unless-stopped

  # Grafana for metrics visualization
  grafana:
    image: grafana/grafana:10.1.0
    ports:
      - "3000:3000"  # Grafana UI
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - gateway-network
    restart: unless-stopped

  # Example upstream service 1
  user-service:
    image: nginx:alpine
    ports:
      - "8081:80"
    volumes:
      - ./examples/user-service:/usr/share/nginx/html
    networks:
      - gateway-network
    restart: unless-stopped

  # Example upstream service 2
  order-service:
    image: nginx:alpine
    ports:
      - "8082:80"
    volumes:
      - ./examples/order-service:/usr/share/nginx/html
    networks:
      - gateway-network
    restart: unless-stopped

  # PostgreSQL for persistent storage (optional)
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=gateway
      - POSTGRES_USER=gateway
      - POSTGRES_PASSWORD=gateway123
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - gateway-network
    restart: unless-stopped

  # Elasticsearch for log aggregation (optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
    ports:
      - "9200:9200"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - gateway-network
    restart: unless-stopped

  # Kibana for log visualization (optional)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.9.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - gateway-network
    restart: unless-stopped

volumes:
  consul-data:
  redis-data:
  prometheus-data:
  grafana-data:
  postgres-data:
  elasticsearch-data:

networks:
  gateway-network:
    driver: bridge
