#!/bin/bash

# API Gateway Build Script
# This script builds the API Gateway binary with various options

set -e

# Default values
VERSION=""
OUTPUT_DIR="./bin"
TARGET=""
DEBUG=false
STATIC=false
VERBOSE=false
LDFLAGS=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show usage information
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Build the API Gateway binary with various options.

OPTIONS:
    -v, --version VERSION    Set version (default: auto-detected from git)
    -o, --output DIR         Output directory (default: ./bin)
    -t, --target TARGET      Target OS/Arch (e.g., linux/amd64, darwin/arm64)
    --debug                  Build with debug symbols
    --static                 Build static binary (CGO_ENABLED=0)
    --verbose                Enable verbose output
    -h, --help               Show this help message

EXAMPLES:
    $0                                    # Build with default settings
    $0 --version v1.2.3                  # Build with specific version
    $0 --target linux/amd64 --static     # Build static Linux binary
    $0 --debug --verbose                  # Build with debug info and verbose output

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -v|--version)
                VERSION="$2"
                shift 2
                ;;
            -o|--output)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            -t|--target)
                TARGET="$2"
                shift 2
                ;;
            --debug)
                DEBUG=true
                shift
                ;;
            --static)
                STATIC=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Detect version from git if not provided
detect_version() {
    if [[ -z "$VERSION" ]]; then
        if git rev-parse --git-dir > /dev/null 2>&1; then
            # Try to get version from git tag
            VERSION=$(git describe --tags --exact-match 2>/dev/null || true)
            
            if [[ -z "$VERSION" ]]; then
                # Use commit hash if no tag
                VERSION=$(git rev-parse --short HEAD)
                print_warning "No git tag found, using commit hash: $VERSION"
            fi
        else
            VERSION="unknown"
            print_warning "Not in a git repository, using version: $VERSION"
        fi
    fi
    
    print_info "Building version: $VERSION"
}

# Detect target platform
detect_target() {
    if [[ -z "$TARGET" ]]; then
        TARGET="$(go env GOOS)/$(go env GOARCH)"
        print_info "Using current platform: $TARGET"
    else
        print_info "Building for target: $TARGET"
    fi
}

# Prepare build environment
prepare_build() {
    # Create output directory
    mkdir -p "$OUTPUT_DIR"
    
    # Parse target
    IFS='/' read -r GOOS GOARCH <<< "$TARGET"
    export GOOS GOARCH
    
    # Set CGO_ENABLED
    if [[ "$STATIC" == true ]]; then
        export CGO_ENABLED=0
        print_info "Building static binary (CGO_ENABLED=0)"
    else
        export CGO_ENABLED=1
    fi
    
    # Prepare ldflags
    LDFLAGS="-X main.version=$VERSION"
    LDFLAGS="$LDFLAGS -X main.buildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)"
    LDFLAGS="$LDFLAGS -X main.gitCommit=$(git rev-parse HEAD 2>/dev/null || echo 'unknown')"
    LDFLAGS="$LDFLAGS -X main.gitBranch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')"
    
    if [[ "$DEBUG" != true ]]; then
        LDFLAGS="$LDFLAGS -s -w"  # Strip debug info
    fi
    
    print_info "LDFLAGS: $LDFLAGS"
}

# Build the binary
build_binary() {
    local binary_name="api-gateway"
    local binary_path="$OUTPUT_DIR/$binary_name"
    
    # Add .exe extension for Windows
    if [[ "$GOOS" == "windows" ]]; then
        binary_path="$binary_path.exe"
    fi
    
    # Add target suffix if cross-compiling
    if [[ "$TARGET" != "$(go env GOOS)/$(go env GOARCH)" ]]; then
        local suffix="${GOOS}-${GOARCH}"
        if [[ "$GOOS" == "windows" ]]; then
            binary_path="$OUTPUT_DIR/${binary_name}-${suffix}.exe"
        else
            binary_path="$OUTPUT_DIR/${binary_name}-${suffix}"
        fi
    fi
    
    print_info "Building binary: $binary_path"
    
    # Build command
    local build_cmd="go build"
    
    if [[ "$VERBOSE" == true ]]; then
        build_cmd="$build_cmd -v"
    fi
    
    build_cmd="$build_cmd -ldflags \"$LDFLAGS\""
    build_cmd="$build_cmd -o \"$binary_path\""
    build_cmd="$build_cmd ./cmd/gateway"
    
    # Execute build
    if [[ "$VERBOSE" == true ]]; then
        print_info "Executing: $build_cmd"
    fi
    
    eval $build_cmd
    
    if [[ $? -eq 0 ]]; then
        print_success "Binary built successfully: $binary_path"
        
        # Show binary info
        if command -v file > /dev/null 2>&1; then
            print_info "Binary info: $(file "$binary_path")"
        fi
        
        if [[ -f "$binary_path" ]]; then
            local size=$(du -h "$binary_path" | cut -f1)
            print_info "Binary size: $size"
        fi
    else
        print_error "Build failed"
        exit 1
    fi
}

# Verify build
verify_build() {
    local binary_name="api-gateway"
    local binary_path="$OUTPUT_DIR/$binary_name"
    
    # Add .exe extension for Windows
    if [[ "$GOOS" == "windows" ]]; then
        binary_path="$binary_path.exe"
    fi
    
    # Add target suffix if cross-compiling
    if [[ "$TARGET" != "$(go env GOOS)/$(go env GOARCH)" ]]; then
        local suffix="${GOOS}-${GOARCH}"
        if [[ "$GOOS" == "windows" ]]; then
            binary_path="$OUTPUT_DIR/${binary_name}-${suffix}.exe"
        else
            binary_path="$OUTPUT_DIR/${binary_name}-${suffix}"
        fi
    fi
    
    if [[ ! -f "$binary_path" ]]; then
        print_error "Binary not found: $binary_path"
        exit 1
    fi
    
    # Test version output (only if not cross-compiling)
    if [[ "$TARGET" == "$(go env GOOS)/$(go env GOARCH)" ]]; then
        print_info "Testing binary..."
        if "$binary_path" --version > /dev/null 2>&1; then
            print_success "Binary verification passed"
        else
            print_warning "Binary verification failed (version check)"
        fi
    else
        print_info "Skipping binary test (cross-compiled)"
    fi
}

# Cleanup function
cleanup() {
    if [[ $? -ne 0 ]]; then
        print_error "Build failed, cleaning up..."
        # Remove partial binary if it exists
        rm -f "$OUTPUT_DIR"/api-gateway* 2>/dev/null || true
    fi
}

# Main function
main() {
    print_info "Starting API Gateway build process..."
    
    # Set up cleanup trap
    trap cleanup EXIT
    
    # Parse arguments
    parse_args "$@"
    
    # Detect version and target
    detect_version
    detect_target
    
    # Prepare build environment
    prepare_build
    
    # Build binary
    build_binary
    
    # Verify build
    verify_build
    
    print_success "Build process completed successfully!"
    print_info "Binary location: $OUTPUT_DIR"
    
    # List built binaries
    print_info "Built binaries:"
    ls -la "$OUTPUT_DIR"/api-gateway* 2>/dev/null || true
}

# Check if Go is installed
if ! command -v go > /dev/null 2>&1; then
    print_error "Go is not installed or not in PATH"
    exit 1
fi

# Check if we're in the right directory
if [[ ! -f "go.mod" ]]; then
    print_error "go.mod not found. Please run this script from the project root directory."
    exit 1
fi

# Run main function
main "$@"
