# Build and Deployment Scripts

This directory contains scripts for building, deploying, and maintaining the API Gateway.

## Structure

```
scripts/
├── README.md                    # This file
├── build/                       # Build scripts
│   ├── build.sh                 # Main build script
│   ├── docker-build.sh          # Docker build script
│   ├── cross-compile.sh         # Cross-platform compilation
│   └── release.sh               # Release build script
├── deploy/                      # Deployment scripts
│   ├── deploy-local.sh          # Local deployment
│   ├── deploy-staging.sh        # Staging deployment
│   ├── deploy-production.sh     # Production deployment
│   └── rollback.sh              # Rollback script
├── docker/                      # Docker-related scripts
│   ├── build-image.sh           # Build Docker image
│   ├── push-image.sh            # Push to registry
│   ├── run-container.sh         # Run container locally
│   └── cleanup-images.sh        # Clean up old images
├── kubernetes/                  # Kubernetes scripts
│   ├── deploy-k8s.sh            # Deploy to Kubernetes
│   ├── update-config.sh         # Update configuration
│   ├── scale.sh                 # Scale deployment
│   └── logs.sh                  # View logs
├── monitoring/                  # Monitoring scripts
│   ├── health-check.sh          # Health check script
│   ├── metrics-check.sh         # Metrics validation
│   ├── log-analysis.sh          # Log analysis
│   └── alert-test.sh            # Test alerting
├── maintenance/                 # Maintenance scripts
│   ├── backup.sh                # Backup data
│   ├── restore.sh               # Restore data
│   ├── cleanup.sh               # Clean up resources
│   └── update-deps.sh           # Update dependencies
├── development/                 # Development scripts
│   ├── setup-dev.sh             # Development setup
│   ├── run-tests.sh             # Run test suite
│   ├── lint.sh                  # Code linting
│   └── format.sh                # Code formatting
└── ci/                          # CI/CD scripts
    ├── ci-build.sh              # CI build script
    ├── ci-test.sh               # CI test script
    ├── ci-deploy.sh             # CI deployment
    └── ci-notify.sh             # CI notifications
```

## Build Scripts

### Main Build (`build/build.sh`)

Builds the API Gateway binary:

```bash
./scripts/build/build.sh [options]

Options:
  -v, --version    Set version (default: auto-detected)
  -o, --output     Output directory (default: ./bin)
  -t, --target     Target OS/Arch (default: current)
  --debug          Build with debug symbols
  --static         Build static binary
```

### Docker Build (`build/docker-build.sh`)

Builds Docker image:

```bash
./scripts/build/docker-build.sh [options]

Options:
  -t, --tag        Image tag (default: latest)
  -f, --file       Dockerfile path
  --no-cache       Build without cache
  --push           Push after build
```

### Cross Compilation (`build/cross-compile.sh`)

Builds for multiple platforms:

```bash
./scripts/build/cross-compile.sh

Builds for:
- linux/amd64
- linux/arm64
- darwin/amd64
- darwin/arm64
- windows/amd64
```

## Deployment Scripts

### Local Deployment (`deploy/deploy-local.sh`)

Deploys locally for development:

```bash
./scripts/deploy/deploy-local.sh [config]

Features:
- Starts local dependencies (Redis, Consul)
- Configures development settings
- Enables hot reload
- Sets up test data
```

### Production Deployment (`deploy/deploy-production.sh`)

Production deployment with safety checks:

```bash
./scripts/deploy/deploy-production.sh [environment]

Features:
- Pre-deployment validation
- Blue-green deployment
- Health checks
- Rollback on failure
- Notification integration
```

### Rollback (`deploy/rollback.sh`)

Rolls back to previous version:

```bash
./scripts/deploy/rollback.sh [version]

Features:
- Lists available versions
- Validates rollback target
- Performs rollback
- Verifies deployment
```

## Docker Scripts

### Build Image (`docker/build-image.sh`)

```bash
#!/bin/bash
set -e

VERSION=${1:-latest}
DOCKERFILE=${2:-Dockerfile}
CONTEXT=${3:-.}

echo "Building Docker image..."
echo "Version: $VERSION"
echo "Dockerfile: $DOCKERFILE"
echo "Context: $CONTEXT"

# Build image
docker build \
  -t api-gateway:$VERSION \
  -f $DOCKERFILE \
  --build-arg VERSION=$VERSION \
  --build-arg BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ") \
  --build-arg GIT_COMMIT=$(git rev-parse HEAD) \
  $CONTEXT

echo "Image built successfully: api-gateway:$VERSION"

# Tag as latest if not already
if [ "$VERSION" != "latest" ]; then
  docker tag api-gateway:$VERSION api-gateway:latest
  echo "Tagged as latest"
fi
```

### Run Container (`docker/run-container.sh`)

```bash
#!/bin/bash
set -e

IMAGE=${1:-api-gateway:latest}
CONFIG=${2:-configs/gateway.yaml}
PORT=${3:-8080}

echo "Running API Gateway container..."
echo "Image: $IMAGE"
echo "Config: $CONFIG"
echo "Port: $PORT"

# Run container
docker run -d \
  --name api-gateway \
  -p $PORT:8080 \
  -p 9090:9090 \
  -v $(pwd)/$CONFIG:/app/config.yaml \
  -v $(pwd)/logs:/app/logs \
  --restart unless-stopped \
  $IMAGE

echo "Container started successfully"
echo "Gateway: http://localhost:$PORT"
echo "Metrics: http://localhost:9090/metrics"
```

## Kubernetes Scripts

### Deploy to Kubernetes (`kubernetes/deploy-k8s.sh`)

```bash
#!/bin/bash
set -e

NAMESPACE=${1:-default}
CONFIG_DIR=${2:-deployments/k8s}
IMAGE_TAG=${3:-latest}

echo "Deploying to Kubernetes..."
echo "Namespace: $NAMESPACE"
echo "Config Directory: $CONFIG_DIR"
echo "Image Tag: $IMAGE_TAG"

# Create namespace if it doesn't exist
kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

# Update image tag in deployment
sed -i "s|image: api-gateway:.*|image: api-gateway:$IMAGE_TAG|g" $CONFIG_DIR/deployment.yaml

# Apply configurations
kubectl apply -f $CONFIG_DIR/ -n $NAMESPACE

# Wait for deployment
kubectl rollout status deployment/api-gateway -n $NAMESPACE

echo "Deployment completed successfully"
```

### Scale Deployment (`kubernetes/scale.sh`)

```bash
#!/bin/bash
set -e

REPLICAS=${1:-3}
NAMESPACE=${2:-default}

echo "Scaling API Gateway deployment..."
echo "Replicas: $REPLICAS"
echo "Namespace: $NAMESPACE"

# Scale deployment
kubectl scale deployment api-gateway --replicas=$REPLICAS -n $NAMESPACE

# Wait for scaling
kubectl rollout status deployment/api-gateway -n $NAMESPACE

echo "Scaling completed successfully"
```

## Monitoring Scripts

### Health Check (`monitoring/health-check.sh`)

```bash
#!/bin/bash
set -e

ENDPOINT=${1:-http://localhost:8080/health}
TIMEOUT=${2:-10}
RETRIES=${3:-3}

echo "Performing health check..."
echo "Endpoint: $ENDPOINT"
echo "Timeout: ${TIMEOUT}s"
echo "Retries: $RETRIES"

for i in $(seq 1 $RETRIES); do
  echo "Attempt $i/$RETRIES..."
  
  if curl -f -s --max-time $TIMEOUT $ENDPOINT > /dev/null; then
    echo "✅ Health check passed"
    exit 0
  else
    echo "❌ Health check failed"
    if [ $i -lt $RETRIES ]; then
      echo "Retrying in 5 seconds..."
      sleep 5
    fi
  fi
done

echo "❌ Health check failed after $RETRIES attempts"
exit 1
```

### Metrics Check (`monitoring/metrics-check.sh`)

```bash
#!/bin/bash
set -e

METRICS_ENDPOINT=${1:-http://localhost:9090/metrics}
EXPECTED_METRICS=${2:-"gateway_requests_total,gateway_request_duration_seconds"}

echo "Checking metrics availability..."
echo "Endpoint: $METRICS_ENDPOINT"

# Fetch metrics
METRICS_OUTPUT=$(curl -s $METRICS_ENDPOINT)

# Check for expected metrics
IFS=',' read -ra METRICS_ARRAY <<< "$EXPECTED_METRICS"
for metric in "${METRICS_ARRAY[@]}"; do
  if echo "$METRICS_OUTPUT" | grep -q "$metric"; then
    echo "✅ Metric found: $metric"
  else
    echo "❌ Metric missing: $metric"
    exit 1
  fi
done

echo "✅ All expected metrics are available"
```

## Development Scripts

### Development Setup (`development/setup-dev.sh`)

```bash
#!/bin/bash
set -e

echo "Setting up development environment..."

# Install dependencies
echo "Installing Go dependencies..."
go mod download
go mod tidy

# Install development tools
echo "Installing development tools..."
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
go install github.com/swaggo/swag/cmd/swag@latest

# Setup pre-commit hooks
echo "Setting up pre-commit hooks..."
cp scripts/hooks/pre-commit .git/hooks/
chmod +x .git/hooks/pre-commit

# Start development services
echo "Starting development services..."
docker-compose -f docker-compose.dev.yml up -d

echo "✅ Development environment setup completed"
echo "Run 'make dev' to start the gateway in development mode"
```

### Run Tests (`development/run-tests.sh`)

```bash
#!/bin/bash
set -e

TEST_TYPE=${1:-all}
COVERAGE=${2:-false}
VERBOSE=${3:-false}

echo "Running tests..."
echo "Type: $TEST_TYPE"
echo "Coverage: $COVERAGE"
echo "Verbose: $VERBOSE"

# Set test flags
TEST_FLAGS=""
if [ "$VERBOSE" = "true" ]; then
  TEST_FLAGS="$TEST_FLAGS -v"
fi

if [ "$COVERAGE" = "true" ]; then
  TEST_FLAGS="$TEST_FLAGS -coverprofile=coverage.out"
fi

# Run tests based on type
case $TEST_TYPE in
  "unit")
    go test $TEST_FLAGS ./tests/unit/...
    ;;
  "integration")
    go test $TEST_FLAGS ./tests/integration/...
    ;;
  "e2e")
    go test $TEST_FLAGS ./tests/e2e/...
    ;;
  "performance")
    go test $TEST_FLAGS -bench=. ./tests/performance/...
    ;;
  "all")
    go test $TEST_FLAGS ./...
    ;;
  *)
    echo "Unknown test type: $TEST_TYPE"
    exit 1
    ;;
esac

# Generate coverage report if requested
if [ "$COVERAGE" = "true" ] && [ -f coverage.out ]; then
  go tool cover -html=coverage.out -o coverage.html
  echo "Coverage report generated: coverage.html"
fi

echo "✅ Tests completed successfully"
```

## Usage Examples

### Complete Build and Deploy

```bash
# Build for production
./scripts/build/build.sh --version v1.2.3 --static

# Build Docker image
./scripts/docker/build-image.sh v1.2.3

# Deploy to staging
./scripts/deploy/deploy-staging.sh

# Run health checks
./scripts/monitoring/health-check.sh https://staging.example.com/health

# Deploy to production
./scripts/deploy/deploy-production.sh production
```

### Development Workflow

```bash
# Setup development environment
./scripts/development/setup-dev.sh

# Run tests
./scripts/development/run-tests.sh all true true

# Lint code
./scripts/development/lint.sh

# Deploy locally
./scripts/deploy/deploy-local.sh
```

### Maintenance Tasks

```bash
# Backup data
./scripts/maintenance/backup.sh

# Update dependencies
./scripts/maintenance/update-deps.sh

# Clean up old resources
./scripts/maintenance/cleanup.sh
```

## Configuration

Scripts can be configured through environment variables:

```bash
# Docker registry
export DOCKER_REGISTRY=your-registry.com
export DOCKER_NAMESPACE=api-gateway

# Kubernetes
export KUBECONFIG=/path/to/kubeconfig
export K8S_NAMESPACE=production

# Monitoring
export HEALTH_CHECK_URL=https://api.example.com/health
export METRICS_URL=https://api.example.com/metrics

# Notifications
export SLACK_WEBHOOK_URL=https://hooks.slack.com/...
export EMAIL_RECIPIENTS=<EMAIL>
```

## Best Practices

1. **Error Handling**: All scripts include proper error handling
2. **Logging**: Comprehensive logging for debugging
3. **Validation**: Input validation and pre-flight checks
4. **Rollback**: Always include rollback capabilities
5. **Monitoring**: Health checks and validation
6. **Security**: Secure handling of secrets and credentials
7. **Documentation**: Clear usage instructions and examples
